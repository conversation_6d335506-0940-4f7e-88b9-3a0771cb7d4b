import sqlite3
import json
from datetime import date

# The JSON data you provided
json_data = """
[
  { "Seq.": 1, "account": "24-000045", "Unit": "BHW1-A-5 A-5", "Customer Name": "Blue Line Factory W.L.L", "OB April": 2110.0, "Q2 invoicing": 3214.0, "April collection": null, "May collection": null, "June collection": null },
  { "Seq.": 2, "account": "24-000010", "Unit": "BHW1-A-6 A-6", "Customer Name": "Shahia Food Limited Company W.L.L.", "OB April": 112.0, "Q2 invoicing": 238.0, "April collection": null, "May collection": null, "June collection": null },
  { "Seq.": 4, "account": "24-000020", "Unit": "BHW1-B-15 B-15", "Customer Name": "Majestic Cars Center1", "OB April": 884.0, "Q2 invoicing": 26.0, "April collection": null, "May collection": null, "June collection": null },
  { "Seq.": 5, "account": "24-000012", "Unit": "BHW1-B-16 B-16", "Customer Name": "Thammanna for Trading Co., W.L.L1", "OB April": 57.0, "Q2 invoicing": 287.0, "April collection": null, "May collection": 57.0, "June collection": null },
  { "Seq.": 12, "account": "24-000001", "Unit": "BHW1-C-01 C-1", "Customer Name": "Red Finishing W.L.L.", "OB April": 3889.0, "Q2 invoicing": 625.0, "April collection": 418.0, "May collection": null, "June collection": null },
  { "Seq.": 17, "account": "24-000035", "Unit": "BHW1-C-21 C-21", "Customer Name": "Baraka Sweets Factory", "OB April": 1071.0, "Q2 invoicing": 378.0, "April collection": 350.0, "May collection": null, "June collection": 414.0 },
  { "Seq.": 28, "account": null, "Unit": "B-2", "Customer Name": "Unlimited Plus Crossfit", "OB April": 16448.0, "Q2 invoicing": 2.0, "April collection": null, "May collection": null, "June collection": null }
]
""" # Shortened for brevity, but you can use the full list

def setup_database():
    conn = sqlite3.connect('utilities.db')
    cursor = conn.cursor()

    # Drop tables if they exist for a clean setup
    cursor.execute("DROP TABLE IF EXISTS collections")
    cursor.execute("DROP TABLE IF EXISTS invoices")
    cursor.execute("DROP TABLE IF EXISTS customers")

    # Create tables
    cursor.execute("""
    CREATE TABLE customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        account_number TEXT,
        unit_code TEXT,
        customer_name TEXT NOT NULL,
        opening_balance REAL DEFAULT 0
    )""")

    cursor.execute("""
    CREATE TABLE invoices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER,
        invoice_date TEXT,
        amount REAL,
        FOREIGN KEY (customer_id) REFERENCES customers (id)
    )""")

    cursor.execute("""
    CREATE TABLE collections (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER,
        collection_date TEXT,
        amount REAL,
        FOREIGN KEY (customer_id) REFERENCES customers (id)
    )""")

    # --- Populate Data ---
    data = json.loads(json_data)
    for record in data:
        # Insert customer
        cursor.execute("INSERT INTO customers (account_number, unit_code, customer_name, opening_balance) VALUES (?, ?, ?, ?)",
                       (record.get('account'), record.get('Unit'), record.get('Customer Name'), record.get('OB April', 0)))
        customer_id = cursor.lastrowid

        # Insert Q2 invoice (assuming it's for the end of Q2)
        if record.get('Q2 invoicing'):
            cursor.execute("INSERT INTO invoices (customer_id, invoice_date, amount) VALUES (?, ?, ?)",
                           (customer_id, str(date(2023, 6, 30)), record['Q2 invoicing']))

        # Insert collections
        if record.get('April collection'):
            cursor.execute("INSERT INTO collections (customer_id, collection_date, amount) VALUES (?, ?, ?)",
                           (customer_id, str(date(2023, 4, 30)), record['April collection']))
        if record.get('May collection'):
            cursor.execute("INSERT INTO collections (customer_id, collection_date, amount) VALUES (?, ?, ?)",
                           (customer_id, str(date(2023, 5, 31)), record['May collection']))
        if record.get('June collection'):
            cursor.execute("INSERT INTO collections (customer_id, collection_date, amount) VALUES (?, ?, ?)",
                           (customer_id, str(date(2023, 6, 30)), record['June collection']))

    conn.commit()
    conn.close()
    print("Database `utilities.db` has been created and populated successfully.")

if __name__ == '__main__':
    setup_database()