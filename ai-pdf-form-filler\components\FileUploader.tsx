
import React, { useState, useCallback, ReactNode } from 'react';
import { CheckCircleIcon, UploadCloudIcon } from './icons';

interface FileUploaderProps {
  onFileSelect: (file: File) => void;
  title: string;
  acceptedFormats: string;
  icon: ReactNode;
  file: File | null;
}

const FileUploader: React.FC<FileUploaderProps> = ({ onFileSelect, title, acceptedFormats, icon, file }) => {
  const [isDragging, setIsDragging] = useState(false);

  const handleDragEnter = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = useCallback((e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      onFileSelect(e.dataTransfer.files[0]);
    }
  }, [onFileSelect]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      onFileSelect(e.target.files[0]);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-md border border-gray-200 dark:border-gray-700 h-full flex flex-col justify-center">
      <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-4 text-center">{title}</h3>
      <label
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        className={`flex flex-col items-center justify-center w-full h-48 border-2 border-dashed rounded-lg cursor-pointer transition-colors duration-300
          ${isDragging ? 'border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20' : 'border-gray-300 dark:border-gray-600'}
          ${file ? 'border-green-500 bg-green-50 dark:bg-green-900/20' : 'hover:border-indigo-400 hover:bg-gray-100 dark:hover:border-gray-500 dark:hover:bg-gray-700'}`}
      >
        <div className="flex flex-col items-center justify-center pt-5 pb-6 text-center">
          {file ? (
            <>
              <CheckCircleIcon className="w-10 h-10 mb-3 text-green-500 dark:text-green-400" />
              <p className="mb-2 text-sm font-semibold text-green-700 dark:text-green-300">{file.name}</p>
              <p className="text-xs text-gray-500 dark:text-gray-400">File selected successfully!</p>
            </>
          ) : (
            <>
                <div className="text-indigo-500 dark:text-indigo-400 mb-3">
                    {icon}
                </div>
              <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">
                <span className="font-semibold">Click to upload</span> or drag and drop
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">{acceptedFormats}</p>
            </>
          )}
        </div>
        <input id="dropzone-file" type="file" className="hidden" onChange={handleFileChange} />
      </label>
    </div>
  );
};

export default FileUploader;
