<!DOCTYPE html>
<html>
<head>
    <title>Polyclinic Organizational Chart</title>
    <style>
        body { font-family: 'Segoe UI', sans-serif; margin: 20px; background: #f0f2f5; }
        .chart { display: flex; flex-direction: column; align-items: center; }
        .node { 
            padding: 15px; 
            margin: 10px; 
            border-radius: 8px; 
            text-align: center;
            min-width: 200px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .medical-labs { background: #ffebee; border: 2px solid #c62828; }
        .clover { background: #f3e5f5; border: 2px solid #7b1fa2; }
        .clinic-row { 
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        .clinic { 
            background: #e3f2fd; 
            border: 2px solid #1976d2;
            flex: 1;
            min-width: 200px;
        }
        .connector { width: 2px; height: 30px; background: #666; margin: 2px auto; }
        h2 { color: #1a237e; text-align: center; }
    </style>
</head>
<body>
    <h2>Polyclinic Organization Chart</h2>
    <div class="chart" id="chart"></div>

    <script>
        const data = {
            name: "Medical Labs",
            children: [
                {
                    name: "Clover",
                    children: [
                        {name: "Iris - Polyclinic Mazaya 3 - Bneid Al Gar"},
                        {name: "Al Aseel International - Mazaya 3 - Bneid Al Gar"},
                        {name: "Yarow - Polyclinic - Mazaya 3 - Bneid Al Gar"},
                        {name: "Fourth Medical Center - Polyclinic Mazaya 5 - Al Jabriya"},
                        {name: "Medical Harbour - Polyclinic Mazaya 6 - Salmiya"},
                        {name: "Med Marine - Polyclinic Mazaya 6 -Salmiya"},
                        {name: "JOYA - Polyclinic  Mazaya 6 -Salmiya"},
                        {name: "Med Grey - Polyclinic Mazaya 7 - Al Jahraa'"},
                        {name: "Aram - Polyclinic Contracts - Mazaya 8 - Subah Al Saleem"}
                    ]
                }
            ]
        };

        function createChart() {
            const chart = document.getElementById('chart');
            
            // Create main nodes
            const medicalLabs = createNode(data.name, 'medical-labs');
            const cloverNode = createNode(data.children[0].name, 'clover');
            
            // Create clinic row container
            const clinicRow = document.createElement('div');
            clinicRow.className = 'clinic-row';
            
            // Add clinics horizontally
            data.children[0].children.forEach(clinic => {
                const clinicNode = document.createElement('div');
                clinicNode.className = 'node clinic';
                clinicNode.innerHTML = `<h4>${clinic.name}</h4>`;
                clinicRow.appendChild(clinicNode);
            });
            
            chart.appendChild(clinicRow);
        }

        function createNode(name, level) {
            const node = document.createElement('div');
            node.className = `node ${level}`;
            node.innerHTML = `<h3>${name}</h3>`;
            document.getElementById('chart').appendChild(node);
            return node;
        }

        // Initialize chart
        createChart();
    </script>
</body>
</html>