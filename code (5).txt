Sub DownloadEmailsAndAttachments_FinalRobust()
    ' ---CONFIGURATION---
    Const TARGET_FOLDER_NAME As String = "Bahrain"
    Const BASE_SAVE_PATH As String = "C:\Bahrain_Exports\"
    Const MIN_ATTACHMENT_SIZE_KB As Long = 20 ' Note: This only affects which attachments are saved as separate files.
    Const SUBJECT_KEYWORD As String = "Tenant"
    
    ' ---LATE-BINDING CONSTANT FOR PDF EXPORT---
    Const wdExportFormatPDF As Long = 17

    ' ---OBJECT DECLARATIONS---
    Dim olApp As Outlook.Application, olNamespace As Outlook.NameSpace, olInbox As Outlook.MAPIFolder
    Dim olTargetFolder As Outlook.MAPIFolder, olItem As Object, olMail As Outlook.MailItem
    Dim wdApp As Object, wdDoc As Object, fso As Object
    
    ' ---PROCESS VARIABLES---
    Dim dateStart As Date, dateEnd As Date, emailSubject As String, emailSubfolderPath As String
    Dim pdfPath As String, tempHtmlPath As String, processedCount As Long
    
    ' ---SETUP AND VALIDATION---
    On Error GoTo ErrorHandler
    
    dateStart = DateSerial(2025, 6, 1)
    dateEnd = DateSerial(2025, 7, 1)
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    If Not fso.FolderExists(BASE_SAVE_PATH) Then
        fso.CreateFolder BASE_SAVE_PATH
    End If
    
    On Error Resume Next
    Set olApp = GetObject(, "Outlook.Application")
    If olApp Is Nothing Then Set olApp = New Outlook.Application
    On Error GoTo ErrorHandler
    
    Set olNamespace = olApp.GetNamespace("MAPI")
    Set olInbox = olNamespace.GetDefaultFolder(olFolderInbox)
    
    On Error Resume Next
    Set olTargetFolder = olInbox.Folders(TARGET_FOLDER_NAME)
    On Error GoTo ErrorHandler
    If olTargetFolder Is Nothing Then
        MsgBox "The folder '" & TARGET_FOLDER_NAME & "' was not found in your Inbox.", vbCritical, "Folder Not Found"
        GoTo Cleanup
    End If
    
    ' ---MAIN PROCESSING LOOP---
    processedCount = 0
    
    For Each olItem In olTargetFolder.Items
        If TypeOf olItem Is Outlook.MailItem Then
            Set olMail = olItem
            
            If (olMail.ReceivedTime >= dateStart And olMail.ReceivedTime < dateEnd) And _
               (InStr(1, olMail.Subject, SUBJECT_KEYWORD, vbTextCompare) > 0) Then
               
                emailSubject = olMail.Subject
                If Trim(emailSubject) = "" Then emailSubject = "(No Subject)"
                
                emailSubfolderPath = BASE_SAVE_PATH & Format(olMail.ReceivedTime, "yyyy-mm-dd_hhnnss") & "_" & CleanFileName_Robust(emailSubject)
                
                If Not fso.FolderExists(emailSubfolderPath) Then
                    fso.CreateFolder emailSubfolderPath
                End If
                
                ' === FIX: CONSTRUCT HTML HEADER TO MATCH OUTLOOK'S APPEARANCE ===
                Dim headerHtml As String
                Dim fullHtml As String
                Dim attachmentList As String
                Dim att As Outlook.Attachment
                
                ' Build a list of attachment filenames
                For Each att In olMail.Attachments
                    attachmentList = attachmentList & att.FileName & "; "
                Next
                
                ' Create the header HTML using a table for alignment
                headerHtml = "<html><head><style>body { font-family: Calibri, sans-serif; font-size: 11pt; } td { vertical-align: top; }</style></head><body>" & _
                             "<table>" & _
                             "<tr><td style='color:grey;'><b>From:</b></td><td>&nbsp;</td><td>" & olMail.SenderName & "</td></tr>" & _
                             "<tr><td style='color:grey;'><b>Sent:</b></td><td>&nbsp;</td><td>" & Format(olMail.SentOn, "dddd, mmmm d, yyyy h:nn AM/PM") & "</td></tr>" & _
                             "<tr><td style='color:grey;'><b>To:</b></td><td>&nbsp;</td><td>" & olMail.To & "</td></tr>"
                
                If olMail.CC <> "" Then
                    headerHtml = headerHtml & "<tr><td style='color:grey;'><b>Cc:</b></td><td>&nbsp;</td><td>" & olMail.CC & "</td></tr>"
                End If
                
                headerHtml = headerHtml & "<tr><td style='color:grey;'><b>Subject:</b></td><td>&nbsp;</td><td>" & olMail.Subject & "</td></tr>"
                
                If attachmentList <> "" Then
                    headerHtml = headerHtml & "<tr><td style='color:grey;'><b>Attachments:</b></td><td>&nbsp;</td><td>" & attachmentList & "</td></tr>"
                End If
                
                headerHtml = headerHtml & "</table><hr/>" ' Add the separator line
                
                ' Combine the new header with the original email body
                fullHtml = headerHtml & olMail.HTMLBody & "</body></html>"
                ' ==============================================================

                tempHtmlPath = fso.GetSpecialFolder(2) & "\" & fso.GetTempName & ".htm"
                ' Write the FULL HTML (header + body) to the temp file
                fso.CreateTextFile(tempHtmlPath, True, True).Write fullHtml ' Use True for Unicode
                
                If wdApp Is Nothing Then Set wdApp = CreateObject("Word.Application")
                wdApp.Visible = False
                
                Set wdDoc = wdApp.Documents.Open(FileName:=tempHtmlPath, ReadOnly:=True)
                pdfPath = emailSubfolderPath & "\" & "Email_Body.pdf"
                wdDoc.ExportAsFixedFormat OutputFileName:=pdfPath, ExportFormat:=wdExportFormatPDF
                wdDoc.Close 0
                Set wdDoc = Nothing
                
                fso.DeleteFile tempHtmlPath, True
                
                ' Save attachments separately (this logic remains the same)
                If olMail.Attachments.Count > 0 Then
                    For Each att In olMail.Attachments
                        If att.Size >= (MIN_ATTACHMENT_SIZE_KB * 1024) Then
                            att.SaveAsFile emailSubfolderPath & "\" & CleanFileName_Robust(att.FileName)
                        End If
                    Next att
                End If
                
                processedCount = processedCount + 1
            End If
        End If
    Next olItem
    
    MsgBox "Processing complete." & vbCrLf & vbCrLf & processedCount & " emails matching the criteria were downloaded to:" & vbCrLf & BASE_SAVE_PATH, vbInformation, "Success"

Cleanup:
    On Error Resume Next
    If Not wdDoc Is Nothing Then wdDoc.Close 0
    If Not wdApp Is Nothing Then wdApp.Quit
    Set att = Nothing: Set olMail = Nothing: Set olItem = Nothing: Set olTargetFolder = Nothing
    Set olInbox = Nothing: Set olNamespace = Nothing: Set olApp = Nothing: Set wdDoc = Nothing
    Set wdApp = Nothing: Set fso = Nothing
    Exit Sub

ErrorHandler:
    If Err.Number = 429 Then
         MsgBox "Microsoft Word could not be found.", vbCritical, "Prerequisite Missing"
    Else
         MsgBox "An unexpected error occurred:" & vbCrLf & vbCrLf & _
                "Error Number: " & Err.Number & vbCrLf & _
                "Description: " & Err.Description, vbCritical, "Runtime Error"
    End If
    GoTo Cleanup
End Sub

Private Function CleanFileName_Robust(fileName As String) As String
    ' A more robust function to clean file/folder names, preventing "Invalid directory" errors.
    Dim invalidChars As Variant, i As Long, tempName As String
    invalidChars = Array("\", "/", ":", "*", "?", """", "<", ">", "|")
    tempName = fileName
    tempName = Replace(Replace(Replace(Replace(tempName, vbCrLf, " "), vbCr, " "), vbLf, " "), vbTab, " ")
    For i = LBound(invalidChars) To UBound(invalidChars)
        tempName = Replace(tempName, invalidChars(i), "_")
    Next i
    tempName = RTrim(tempName)
    Do While Right(tempName, 1) = ".": tempName = Left(tempName, Len(tempName) - 1): tempName = RTrim(tempName): Loop
    If Trim(tempName) = "" Then tempName = "(Invalid_Name)"
    If Len(tempName) > 120 Then tempName = Left(tempName, 120)
    CleanFileName_Robust = tempName
End Function