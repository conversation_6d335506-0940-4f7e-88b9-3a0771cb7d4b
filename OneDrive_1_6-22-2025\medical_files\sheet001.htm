<html xmlns:v="urn:schemas-microsoft-com:vml"
xmlns:o="urn:schemas-microsoft-com:office:office"
xmlns:x="urn:schemas-microsoft-com:office:excel"
xmlns="http://www.w3.org/TR/REC-html40">

<head>
<meta http-equiv=Content-Type content="text/html; charset=windows-1252">
<meta name=ProgId content=Excel.Sheet>
<meta name=Generator content="Microsoft Excel 15">
<link id=Main-File rel=Main-File href="../medical.htm">
<link rel=File-List href=filelist.xml>
<link rel=Stylesheet href=stylesheet.css>
<style>
<!--table
	{mso-displayed-decimal-separator:"\.";
	mso-displayed-thousand-separator:"\,";}
@page
	{margin:.75in .7in .75in .7in;
	mso-header-margin:.3in;
	mso-footer-margin:.3in;}
-->
</style>
<![if !supportTabStrip]><script language="JavaScript">
<!--
function fnUpdateTabs()
 {
  if (parent.window.g_iIEVer>=4) {
   if (parent.document.readyState=="complete"
    && parent.frames['frTabs'].document.readyState=="complete")
   parent.fnSetActiveSheet(0);
  else
   window.setTimeout("fnUpdateTabs();",150);
 }
}

if (window.name!="frSheet")
 window.location.replace("../medical.htm");
else
 fnUpdateTabs();
//-->
</script>
<![endif]>
</head>

<body link="#467886" vlink="#96607D">
<span style='font-variant-ligatures: normal;font-variant-caps: normal;
orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;
text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial'>

<table border=0 cellpadding=0 cellspacing=0 width=4736 style='border-collapse:
 collapse;table-layout:fixed;width:3552pt'>
 <col width=64 span=74 style='width:48pt'>
 <tr height=19 style='height:14.4pt'>
  <td height=19 class=xl65 colspan=74 width=4736 style='height:14.4pt;
  mso-ignore:colspan;width:3552pt'>&lt;!DOCTYPE html&gt; &lt;html&gt;
  &lt;head&gt; &lt;title&gt;Polyclinic Organizational Chart&lt;/title&gt;
  &lt;style&gt; body { font-family: 'Segoe UI', sans-serif; margin: 20px;
  background: #f0f2f5; } .chart { display: flex; flex-direction: column; align-items:
  center; } .node { padding: 15px; margin: 10px; border-radius: 8px;
  text-align: center; min-width: 200px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  } .clinic { background: #e3f2fd; border: 2px solid #1976d2; } .floor {
  background: #f3e5f5; border: 2px solid #7b1fa2; } .clinic-code { background:
  #e8f5e9; border: 2px solid #388e3c; } .doctor { background: #fffde7; border:
  2px solid #f9a825; } .connector { width: 2px; height: 30px; background: #666;
  margin: 2px auto; } h2 { color: #1a237e; text-align: center; } &lt;/style&gt;
  &lt;/head&gt; &lt;body&gt; &lt;h2&gt;Polyclinic Organization Chart&lt;/h2&gt;
  &lt;div class=&quot;chart&quot; id=&quot;chart&quot;&gt;&lt;/div&gt;
  &lt;script&gt; const data = [ { name: &quot;Iris - Polyclinic Mazaya 3&quot;,
  floors: [ { floor: 2, codes: [ {code: &quot;A&quot;, doctor: &quot;Abdullah
  Abdul Aziz Al Hajri&quot;}, {code: &quot;B&quot;, doctor: &quot;Smart Health
  Co.&quot;}, {code: &quot;C&quot;, doctor: &quot;Walid Hamad Ashwi
  Raheel&quot;} ] }, { floor: 3, codes: [ {code: &quot;A+B&quot;, doctor:
  &quot;Amr Nabil Qutb&quot;}, {code: &quot;C&quot;, doctor: &quot;Oxcana
  Bogdanovic&quot;} ] }, { floor: 4, codes: [ {code: &quot;A&quot;, doctor:
  &quot;Hassaan A Jaber &amp; Obaid Metni&quot;}, {code: &quot;B&quot;, doctor:
  &quot;Mohamed Youssef Al Eissa&quot;}, {code: &quot;C&quot;, doctor:
  &quot;Dr. Mariam Abed Ali Al-Turki&quot;} ] } ] }, { name: &quot;Al Aseel
  International&quot;, floors: [ { floor: 7, codes: [{code: &quot;C&quot;,
  doctor: &quot;Daniel Alain&quot;}] }, { floor: 8, codes: [ {code:
  &quot;A&quot;, doctor: &quot;Abdullah Abdul Rahman Al Hassan&quot;}, {code:
  &quot;B&quot;, doctor: &quot;Hossam Mohamed El Badri&quot;}, {code:
  &quot;C&quot;, doctor: &quot;Mustafa Samy Al Kaddousy&quot;} ] }, { floor: 9,
  codes: [ {code: &quot;A&quot;, doctor: &quot;Nasser Faisal Al Mutairy&quot;},
  {code: &quot;B&quot;, doctor: &quot;Andro George Mikha'eel&quot;}, {code:
  &quot;C&quot;, doctor: &quot;Dr. Noor Aladdin Alomar &amp; Dr. Aya
  Samara&quot;} ] }, { floor: 10, codes: [ {code: &quot;A+B&quot;, doctor:
  &quot;Dr. Ali Al-Mukaimi &amp; Nisreen Baeij&quot;}, {code: &quot;C&quot;,
  doctor: &quot;Dr. Ali Al-Mukaimi&quot;} ] } ] }, { name: &quot;Yarow -
  Polyclinic&quot;, floors: [ { floor: 11, codes: [ {code: &quot;A&quot;,
  doctor: &quot;Dr. Ahmed Abdulsamad Yehya Jassem&quot;}, {code: &quot;B&quot;,
  doctor: &quot;Dr. Osamah J M Albaker&quot;}, {code: &quot;C&quot;, doctor:
  &quot;Hossam Mohamed El Badri&quot;} ] }, { floor: 12, codes: [ {code:
  &quot;A&quot;, doctor: &quot;Ahmed Mohamed Ahmed Ibrahim&quot;}, {code:
  &quot;B&quot;, doctor: &quot;Sale Abdul Ghaffar Ma'arafie&quot;}, {code:
  &quot;C&quot;, doctor: &quot;Adnan Ibrahim Ibrahim&quot;} ] } ] }, { name:
  &quot;Fourth Medical Center&quot;, floors: [ {floor: 1, codes: [{code:
  &quot;A&quot;, doctor: &quot;Salam Attar&quot;}]}, {floor: &quot;2-3&quot;,
  codes: [{code: &quot;A&quot;, doctor: &quot;One Day to Manage Projects
  Co.&quot;}]}, {floor: &quot;4-5&quot;, codes: [{code: &quot;A&quot;, doctor:
  &quot;Athba Co.&quot;}]}, {floor: 6, codes: [{code: &quot;A&quot;, doctor:
  &quot;Health Care Co.&quot;}]}, {floor: 7, codes: [{code: &quot;A&quot;,
  doctor: &quot;Abdul Aziz Fahad Al Mezeiny&quot;}]}, {floor: 13, codes:
  [{code: &quot;A&quot;, doctor: &quot;Revolution Medical Co.&quot;}]}, {floor:
  14, codes: [{code: &quot;A&quot;, doctor: &quot;Dr. Farouk
  Alzoubani&quot;}]}, {floor: 15, codes: [{code: &quot;A&quot;, doctor:
  &quot;Assem Drwesh Mostafa Abdulnabi&quot;}]}, {floor: 16, codes: [{code:
  &quot;A&quot;, doctor: &quot;One Day to Manage Projects Co.&quot;}]}, {floor:
  17, codes: [{code: &quot;A&quot;, doctor: &quot;Dr. Abdullah Sadad Sabri
  Al-Ozairi&quot;}]}, {floor: &quot;18-19&quot;, codes: [{code: &quot;A&quot;,
  doctor: &quot;Gulf Care Co.&quot;}]} ] }, { name: &quot;Medical
  Harbour&quot;, floors: [ {floor: 1, codes: [{code: &quot;C&quot;, doctor:
  &quot;Moaeyed Zaid Al Saq'abi&quot;}]}, {floor: 2, codes: [{code:
  &quot;C&quot;, doctor: &quot;Mohamed Abdul Majid Hassan&quot;}]}, {floor: 3,
  codes: [{code: &quot;C&quot;, doctor: &quot;Salah El Din Mohamed El
  Sherbini&quot;}]}, {floor: 4, codes: [{code: &quot;C&quot;, doctor:
  &quot;Youssef Al Khleify/Rawan Al Khatib&quot;}]}, {floor: 8, codes: [{code:
  &quot;C&quot;, doctor: &quot;Amir Eissa Attia Killa&quot;}]}, {floor: 9,
  codes: [{code: &quot;C&quot;, doctor: &quot;Dr. Hesham Mohamed Yassin
  Ibrahim&quot;}]}, {floor: 10, codes: [{code: &quot;C&quot;, doctor: &quot;Med
  Vision Medical Services&quot;}]}, {floor: 11, codes: [{code: &quot;C&quot;,
  doctor: &quot;Fatmah Mohamed Badawy&quot;}]}, {floor: 12, codes: [{code:
  &quot;C&quot;, doctor: &quot;Othman Youssef Al Mas'oud&quot;}]}, {floor: 13,
  codes: [{code: &quot;C&quot;, doctor: &quot;Btissam Ibn Kiran&quot;}]},
  {floor: 14, codes: [{code: &quot;C&quot;, doctor: &quot;Misha'al Al
  Dahsh&quot;}]}, {floor: 15, codes: [{code: &quot;C&quot;, doctor: &quot;Amal
  Al Shaiji / Faisal Al Terkeet&quot;}]}, {floor: 16, codes: [{code:
  &quot;C&quot;, doctor: &quot;Signofa Co./Ahmed Eissa&quot;}]}, {floor: 17,
  codes: [{code: &quot;C&quot;, doctor: &quot;Waleed Hamid Raheel&quot;}]},
  {floor: 18, codes: [{code: &quot;C&quot;, doctor: &quot;Eman Ghorab&quot;}]},
  {floor: 19, codes: [{code: &quot;C&quot;, doctor: &quot;Emad Morkos/Ahmed
  Youssef&quot;}]}, {floor: 20, codes: [{code: &quot;C&quot;, doctor:
  &quot;Mohamed Al Kolk&quot;}]}, {floor: 21, codes: [{code: &quot;C&quot;,
  doctor: &quot;Youssef Al Khleify&quot;}]} ] }, { name: &quot;Med
  Marine&quot;, floors: [ {floor: 5, codes: [ {code: &quot;A&quot;, doctor:
  &quot;Fatima Ne'ma Al Awadhi&quot;}, {code: &quot;B&quot;, doctor:
  &quot;Mohamed As'ad Eid/Wael Bezrah&quot;} ]}, {floor: 6, codes: [{code:
  &quot;A+B&quot;, doctor: &quot;Mohamed Youssef Al Sabty&quot;}]}, {floor: 7,
  codes: [{code: &quot;A+B&quot;, doctor: &quot;Mostafa Mohamed Tomsu&quot;}]}
  ] }, { name: &quot;JOYA - Polyclinic&quot;, floors: [ {floor: 8, codes: [
  {code: &quot;A&quot;, doctor: &quot;Ihab Mohamed Younes Omar&quot;}, {code:
  &quot;B&quot;, doctor: &quot;Huda Mahmoud Selim&quot;} ]}, {floor: 9, codes:
  [{code: &quot;A+B&quot;, doctor: &quot;Berlin Co./Mohamed Riyadh&quot;}]},
  {floor: 10, codes: [{code: &quot;A+B&quot;, doctor: &quot;Shehta Mostafa
  Ze'reb&quot;}]} ] }, { name: &quot;Med Grey&quot;, floors: [ {floor: 5,
  codes: [{code: &quot;A&quot;, doctor: &quot;Dr. Amr Nabil Qutb&quot;}]},
  {floor: &quot;6-7&quot;, codes: [{code: &quot;A&quot;, doctor: &quot;Dr.
  Shehta Mostafa Zurub&quot;}]} ] }, { name: &quot;Aram - Polyclinic&quot;,
  floors: [ {floor: 2, codes: [{code: &quot;A+B&quot;, doctor: &quot;Dalia/Mina/Osama/Mahmoud&quot;}]},
  {floor: 3, codes: [{code: &quot;A+B&quot;, doctor:
  &quot;Ayman/Islam&quot;}]}, {floor: 4, codes: [ {code: &quot;A&quot;, doctor:
  &quot;Mohamed Al Sayyad&quot;}, {code: &quot;B&quot;, doctor: &quot;Mohamed
  Al Sayyad&quot;} ]}, {floor: 5, codes: [ {code: &quot;A&quot;, doctor:
  &quot;Bishoy/Mina/Zaher&quot;}, {code: &quot;B&quot;, doctor:
  &quot;Nasser/Mohamed&quot;} ]}, {floor: 6, codes: [ {code: &quot;A&quot;,
  doctor: &quot;Munira/Anjoud&quot;}, {code: &quot;B&quot;, doctor:
  &quot;Munira/Anjoud&quot;} ]}, {floor: 7, codes: [{code: &quot;A+B&quot;,
  doctor: &quot;Sondos Ghaneim&quot;}]}, {floor: 8, codes: [ {code:
  &quot;A&quot;, doctor: &quot;Marina/Mary/Mariana&quot;}, {code:
  &quot;B&quot;, doctor: &quot;Dr. Mohammed Salem&quot;} ]}, {floor: 9, codes:
  [ {code: &quot;A&quot;, doctor: &quot;Rwda Ahmed&quot;}, {code:
  &quot;B&quot;, doctor: &quot;Marawan Essam&quot;} ]} ] } ]; function
  createChart() { const chart = document.getElementById('chart');
  data.forEach(clinic =&gt; { // Create clinic node const clinicNode =
  document.createElement('div'); clinicNode.className = 'node clinic';
  clinicNode.innerHTML = `&lt;h3&gt;${clinic.name}&lt;/h3&gt;`;
  chart.appendChild(clinicNode); // Create floor nodes
  clinic.floors.forEach(floor =&gt; { const floorNode =
  document.createElement('div'); floorNode.className = 'node floor';
  floorNode.innerHTML = `&lt;h4&gt;Floor ${floor.floor}&lt;/h4&gt;`;
  chart.appendChild(floorNode); // Create connector const connector =
  document.createElement('div'); connector.className = 'connector';
  chart.appendChild(connector); // Create clinic code nodes
  floor.codes.forEach(code =&gt; { const codeNode =
  document.createElement('div'); codeNode.className = 'node clinic-code';
  codeNode.innerHTML = ` &lt;strong&gt;Clinic
  ${code.code}&lt;/strong&gt;&lt;br&gt;
  &lt;small&gt;${code.doctor}&lt;/small&gt; `; chart.appendChild(codeNode); });
  }); }); } // Initialize chart createChart(); &lt;/script&gt; &lt;/body&gt;
  &lt;/html&gt;</span></td>
 </tr>
 <![if supportMisalignedColumns]>
 <tr height=0 style='display:none'>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
  <td width=64 style='width:48pt'></td>
 </tr>
 <![endif]>
</table>

</body>

</html>
