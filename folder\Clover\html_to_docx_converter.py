from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.oxml.shared import OxmlElement, qn
import re

def add_hyperlink(paragraph, url, text):
    """Add a hyperlink to a paragraph."""
    part = paragraph.part
    r_id = part.relate_to(url, "http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink", is_external=True)
    
    hyperlink = OxmlElement('w:hyperlink')
    hyperlink.set(qn('r:id'), r_id)
    
    new_run = OxmlElement('w:r')
    rPr = OxmlElement('w:rPr')
    
    new_run.append(rPr)
    new_run.text = text
    hyperlink.append(new_run)
    
    paragraph._p.append(hyperlink)
    return hyperlink

def create_polyclinic_document():
    # Create a new document
    doc = Document()
    
    # Set document margins
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)
        section.left_margin = Inches(1)
        section.right_margin = Inches(1)
    
    # Add title
    title = doc.add_heading('Polyclinic Organization Chart', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Add a line break
    doc.add_paragraph()
    
    # Define the clinic data (extracted from the HTML)
    clinic_data = [
        {
            "name": "Iris - Polyclinic Mazaya 3",
            "floors": [
                {
                    "floor": 2,
                    "codes": [
                        {"code": "A", "doctor": "Abdullah Abdul Aziz Al Hajri"},
                        {"code": "B", "doctor": "Smart Health Co."},
                        {"code": "C", "doctor": "Walid Hamad Ashwi Raheel"}
                    ]
                },
                {
                    "floor": 3,
                    "codes": [
                        {"code": "A+B", "doctor": "Amr Nabil Qutb"},
                        {"code": "C", "doctor": "Oxcana Bogdanovic"}
                    ]
                },
                {
                    "floor": 4,
                    "codes": [
                        {"code": "A", "doctor": "Hassaan A Jaber & Obaid Metni"},
                        {"code": "B", "doctor": "Mohamed Youssef Al Eissa"},
                        {"code": "C", "doctor": "Dr. Mariam Abed Ali Al-Turki"}
                    ]
                }
            ]
        },
        {
            "name": "Al Aseel International",
            "floors": [
                {
                    "floor": 7,
                    "codes": [{"code": "C", "doctor": "Daniel Alain"}]
                },
                {
                    "floor": 8,
                    "codes": [
                        {"code": "A", "doctor": "Abdullah Abdul Rahman Al Hassan"},
                        {"code": "B", "doctor": "Hossam Mohamed El Badri"},
                        {"code": "C", "doctor": "Mustafa Samy Al Kaddousy"}
                    ]
                },
                {
                    "floor": 9,
                    "codes": [
                        {"code": "A", "doctor": "Nasser Faisal Al Mutairy"},
                        {"code": "B", "doctor": "Andro George Mikha'eel"},
                        {"code": "C", "doctor": "Dr. Noor Aladdin Alomar & Dr. Aya Samara"}
                    ]
                },
                {
                    "floor": 10,
                    "codes": [
                        {"code": "A+B", "doctor": "Dr. Ali Al-Mukaimi & Nisreen Baeij"},
                        {"code": "C", "doctor": "Dr. Ali Al-Mukaimi"}
                    ]
                }
            ]
        },
        {
            "name": "Yarow - Polyclinic",
            "floors": [
                {
                    "floor": 11,
                    "codes": [
                        {"code": "A", "doctor": "Dr. Ahmed Abdulsamad Yehya Jassem"},
                        {"code": "B", "doctor": "Dr. Osamah J M Albaker"},
                        {"code": "C", "doctor": "Hossam Mohamed El Badri"}
                    ]
                },
                {
                    "floor": 12,
                    "codes": [
                        {"code": "A", "doctor": "Ahmed Mohamed Ahmed Ibrahim"},
                        {"code": "B", "doctor": "Sale Abdul Ghaffar Ma'arafie"},
                        {"code": "C", "doctor": "Adnan Ibrahim Ibrahim"}
                    ]
                }
            ]
        },
        {
            "name": "Fourth Medical Center",
            "floors": [
                {"floor": 1, "codes": [{"code": "A", "doctor": "Salam Attar"}]},
                {"floor": "2-3", "codes": [{"code": "A", "doctor": "One Day to Manage Projects Co."}]},
                {"floor": "4-5", "codes": [{"code": "A", "doctor": "Athba Co."}]},
                {"floor": 6, "codes": [{"code": "A", "doctor": "Health Care Co."}]},
                {"floor": 7, "codes": [{"code": "A", "doctor": "Abdul Aziz Fahad Al Mezeiny"}]},
                {"floor": 13, "codes": [{"code": "A", "doctor": "Revolution Medical Co."}]},
                {"floor": 14, "codes": [{"code": "A", "doctor": "Dr. Farouk Alzoubani"}]},
                {"floor": 15, "codes": [{"code": "A", "doctor": "Assem Drwesh Mostafa Abdulnabi"}]},
                {"floor": 16, "codes": [{"code": "A", "doctor": "One Day to Manage Projects Co."}]},
                {"floor": 17, "codes": [{"code": "A", "doctor": "Dr. Abdullah Sadad Sabri Al-Ozairi"}]},
                {"floor": "18-19", "codes": [{"code": "A", "doctor": "Gulf Care Co."}]}
            ]
        },
        {
            "name": "Medical Harbour",
            "floors": [
                {"floor": 1, "codes": [{"code": "C", "doctor": "Moaeyed Zaid Al Saq'abi"}]},
                {"floor": 2, "codes": [{"code": "C", "doctor": "Mohamed Abdul Majid Hassan"}]},
                {"floor": 3, "codes": [{"code": "C", "doctor": "Salah El Din Mohamed El Sherbini"}]},
                {"floor": 4, "codes": [{"code": "C", "doctor": "Youssef Al Khleify/Rawan Al Khatib"}]},
                {"floor": 8, "codes": [{"code": "C", "doctor": "Amir Eissa Attia Killa"}]},
                {"floor": 9, "codes": [{"code": "C", "doctor": "Dr. Hesham Mohamed Yassin Ibrahim"}]},
                {"floor": 10, "codes": [{"code": "C", "doctor": "Med Vision Medical Services"}]},
                {"floor": 11, "codes": [{"code": "C", "doctor": "Fatmah Mohamed Badawy"}]},
                {"floor": 12, "codes": [{"code": "C", "doctor": "Othman Youssef Al Mas'oud"}]},
                {"floor": 13, "codes": [{"code": "C", "doctor": "Btissam Ibn Kiran"}]},
                {"floor": 14, "codes": [{"code": "C", "doctor": "Misha'al Al Dahsh"}]},
                {"floor": 15, "codes": [{"code": "C", "doctor": "Amal Al Shaiji / Faisal Al Terkeet"}]},
                {"floor": 16, "codes": [{"code": "C", "doctor": "Signofa Co./Ahmed Eissa"}]},
                {"floor": 17, "codes": [{"code": "C", "doctor": "Waleed Hamid Raheel"}]},
                {"floor": 18, "codes": [{"code": "C", "doctor": "Eman Ghorab"}]},
                {"floor": 19, "codes": [{"code": "C", "doctor": "Emad Morkos/Ahmed Youssef"}]},
                {"floor": 20, "codes": [{"code": "C", "doctor": "Mohamed Al Kolk"}]},
                {"floor": 21, "codes": [{"code": "C", "doctor": "Youssef Al Khleify"}]}
            ]
        },
        {
            "name": "Med Marine",
            "floors": [
                {
                    "floor": 5,
                    "codes": [
                        {"code": "A", "doctor": "Fatima Ne'ma Al Awadhi"},
                        {"code": "B", "doctor": "Mohamed As'ad Eid/Wael Bezrah"}
                    ]
                },
                {"floor": 6, "codes": [{"code": "A+B", "doctor": "Mohamed Youssef Al Sabty"}]},
                {"floor": 7, "codes": [{"code": "A+B", "doctor": "Mostafa Mohamed Tomsu"}]}
            ]
        },
        {
            "name": "JOYA - Polyclinic",
            "floors": [
                {
                    "floor": 8,
                    "codes": [
                        {"code": "A", "doctor": "Ihab Mohamed Younes Omar"},
                        {"code": "B", "doctor": "Huda Mahmoud Selim"}
                    ]
                },
                {"floor": 9, "codes": [{"code": "A+B", "doctor": "Berlin Co./Mohamed Riyadh"}]},
                {"floor": 10, "codes": [{"code": "A+B", "doctor": "Shehta Mostafa Ze'reb"}]}
            ]
        },
        {
            "name": "Med Grey",
            "floors": [
                {"floor": 5, "codes": [{"code": "A", "doctor": "Dr. Amr Nabil Qutb"}]},
                {"floor": "6-7", "codes": [{"code": "A", "doctor": "Dr. Shehta Mostafa Zurub"}]}
            ]
        },
        {
            "name": "Aram - Polyclinic",
            "floors": [
                {"floor": 2, "codes": [{"code": "A+B", "doctor": "Dalia/Mina/Osama/Mahmoud"}]},
                {"floor": 3, "codes": [{"code": "A+B", "doctor": "Ayman/Islam"}]},
                {
                    "floor": 4,
                    "codes": [
                        {"code": "A", "doctor": "Mohamed Al Sayyad"},
                        {"code": "B", "doctor": "Mohamed Al Sayyad"}
                    ]
                },
                {
                    "floor": 5,
                    "codes": [
                        {"code": "A", "doctor": "Bishoy/Mina/Zaher"},
                        {"code": "B", "doctor": "Nasser/Mohamed"}
                    ]
                },
                {
                    "floor": 6,
                    "codes": [
                        {"code": "A", "doctor": "Munira/Anjoud"},
                        {"code": "B", "doctor": "Munira/Anjoud"}
                    ]
                },
                {"floor": 7, "codes": [{"code": "A+B", "doctor": "Sondos Ghaneim"}]},
                {
                    "floor": 8,
                    "codes": [
                        {"code": "A", "doctor": "Marina/Mary/Mariana"},
                        {"code": "B", "doctor": "Dr. Mohammed Salem"}
                    ]
                },
                {
                    "floor": 9,
                    "codes": [
                        {"code": "A", "doctor": "Rwda Ahmed"},
                        {"code": "B", "doctor": "Marawan Essam"}
                    ]
                }
            ]
        }
    ]
    
    # Add organizational structure overview
    overview = doc.add_heading('Organizational Structure', level=1)
    
    # Add hierarchy description
    hierarchy_para = doc.add_paragraph()
    hierarchy_para.add_run("Medical Labs").bold = True
    hierarchy_para.add_run(" → ")
    hierarchy_para.add_run("Clover").bold = True
    hierarchy_para.add_run(" → ")
    hierarchy_para.add_run("Individual Clinics").bold = True
    hierarchy_para.add_run(" → ")
    hierarchy_para.add_run("Floors").bold = True
    hierarchy_para.add_run(" → ")
    hierarchy_para.add_run("Clinic Codes").bold = True
    hierarchy_para.add_run(" → ")
    hierarchy_para.add_run("Doctors/Companies").bold = True
    
    doc.add_paragraph()
    
    # Process each clinic
    for clinic in clinic_data:
        # Add clinic heading
        clinic_heading = doc.add_heading(clinic["name"], level=1)
        
        # Create table for this clinic
        table = doc.add_table(rows=1, cols=3)
        table.style = 'Table Grid'
        table.alignment = WD_TABLE_ALIGNMENT.CENTER
        
        # Set table headers
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = 'Floor'
        hdr_cells[1].text = 'Clinic Code'
        hdr_cells[2].text = 'Doctor/Company'
        
        # Make headers bold
        for cell in hdr_cells:
            for paragraph in cell.paragraphs:
                for run in paragraph.runs:
                    run.bold = True
        
        # Add data rows
        for floor_data in clinic["floors"]:
            for code_data in floor_data["codes"]:
                row_cells = table.add_row().cells
                row_cells[0].text = str(floor_data["floor"])
                row_cells[1].text = code_data["code"]
                row_cells[2].text = code_data["doctor"]
        
        # Add spacing after each clinic
        doc.add_paragraph()
    
    # Add summary section
    summary_heading = doc.add_heading('Summary', level=1)
    
    # Calculate totals
    total_clinics = len(clinic_data)
    total_floors = sum(len(clinic["floors"]) for clinic in clinic_data)
    total_entries = sum(len(floor["codes"]) for clinic in clinic_data for floor in clinic["floors"])
    
    summary_para = doc.add_paragraph()
    summary_para.add_run(f"Total Medical Facilities: ").bold = True
    summary_para.add_run(f"{total_clinics}\n")
    summary_para.add_run(f"Total Floor Levels: ").bold = True
    summary_para.add_run(f"{total_floors}\n")
    summary_para.add_run(f"Total Clinic Entries: ").bold = True
    summary_para.add_run(f"{total_entries}")
    
    # Save the document
    doc.save('Polyclinic_Organization_Chart.docx')
    print("Document created successfully: Polyclinic_Organization_Chart.docx")

if __name__ == "__main__":
    create_polyclinic_document()
