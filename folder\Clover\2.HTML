<!DOCTYPE html>
<html>
<head>
    <title>Polyclinic Organizational Chart</title>
    <style>
        body { font-family: 'Segoe UI', sans-serif; margin: 20px; background: #f0f2f5; }
        .chart-container { display: flex; flex-direction: column; align-items: center; }
        .top-level-nodes { display: flex; flex-direction: column; align-items: center; margin-bottom: 20px; }
        .clinic-columns-container { display: flex; justify-content: center; flex-wrap: wrap; gap: 20px; }
        .clinic-column { display: flex; flex-direction: column; align-items: center; }
        .node {
            padding: 15px;
            margin: 10px;
            border-radius: 8px;
            text-align: center;
            min-width: 200px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .medical-labs { background: #ffebee; border: 2px solid #c62828; }
        .clover { background: #f3e5f5; border: 2px solid #7b1fa2; }
        .clinic { background: #e3f2fd; border: 2px solid #1976d2; }
        .floor { background: #f3e5f5; border: 2px solid #7b1fa2; }
        .clinic-code { background: #e8f5e9; border: 2px solid #388e3c; }
        .doctor { background: #fffde7; border: 2px solid #f9a825; }
        .connector { width: 2px; height: 20px; background: #666; margin: 0 auto; }
        h2 { color: #1a237e; text-align: center; }
    </style>
</head>
<body>
    <h2>Polyclinic Organization Chart</h2>
    <div class="chart-container" id="chart"></div>

    <script>
        const clinicData = [
            {
                name: "Iris - Polyclinic Mazaya 3",
                floors: [
                    {
                        floor: 2,
                        codes: [
                            {code: "A", doctor: "Abdullah Abdul Aziz Al Hajri"},
                            {code: "B", doctor: "Smart Health Co."},
                            {code: "C", doctor: "Walid Hamad Ashwi Raheel"}
                        ]
                    },
                    {
                        floor: 3,
                        codes: [
                            {code: "A+B", doctor: "Amr Nabil Qutb"},
                            {code: "C", doctor: "Oxcana Bogdanovic"}
                        ]
                    },
                    {
                        floor: 4,
                        codes: [
                            {code: "A", doctor: "Hassaan A Jaber & Obaid Metni"},
                            {code: "B", doctor: "Mohamed Youssef Al Eissa"},
                            {code: "C", doctor: "Dr. Mariam Abed Ali Al-Turki"}
                        ]
                    }
                ]
            },
            {
                name: "Al Aseel International",
                floors: [
                    {
                        floor: 7,
                        codes: [{code: "C", doctor: "Daniel Alain"}]
                    },
                    {
                        floor: 8,
                        codes: [
                            {code: "A", doctor: "Abdullah Abdul Rahman Al Hassan"},
                            {code: "B", doctor: "Hossam Mohamed El Badri"},
                            {code: "C", doctor: "Mustafa Samy Al Kaddousy"}
                        ]
                    },
                    {
                        floor: 9,
                        codes: [
                            {code: "A", doctor: "Nasser Faisal Al Mutairy"},
                            {code: "B", doctor: "Andro George Mikha'eel"},
                            {code: "C", doctor: "Dr. Noor Aladdin Alomar & Dr. Aya Samara"}
                        ]
                    },
                    {
                        floor: 10,
                        codes: [
                            {code: "A+B", doctor: "Dr. Ali Al-Mukaimi & Nisreen Baeij"},
                            {code: "C", doctor: "Dr. Ali Al-Mukaimi"}
                        ]
                    }
                ]
            },
            {
                name: "Yarow - Polyclinic",
                floors: [
                    {
                        floor: 11,
                        codes: [
                            {code: "A", doctor: "Dr. Ahmed Abdulsamad Yehya Jassem"},
                            {code: "B", doctor: "Dr. Osamah J M Albaker"},
                            {code: "C", doctor: "Hossam Mohamed El Badri"}
                        ]
                    },
                    {
                        floor: 12,
                        codes: [
                            {code: "A", doctor: "Ahmed Mohamed Ahmed Ibrahim"},
                            {code: "B", doctor: "Sale Abdul Ghaffar Ma'arafie"},
                            {code: "C", doctor: "Adnan Ibrahim Ibrahim"}
                        ]
                    }
                ]
            },
            {
                name: "Fourth Medical Center",
                floors: [
                    {floor: 1, codes: [{code: "A", doctor: "Salam Attar"}]},
                    {floor: "2-3", codes: [{code: "A", doctor: "One Day to Manage Projects Co."}]},
                    {floor: "4-5", codes: [{code: "A", doctor: "Athba Co."}]},
                    {floor: 6, codes: [{code: "A", doctor: "Health Care Co."}]},
                    {floor: 7, codes: [{code: "A", doctor: "Abdul Aziz Fahad Al Mezeiny"}]},
                    {floor: 13, codes: [{code: "A", doctor: "Revolution Medical Co."}]},
                    {floor: 14, codes: [{code: "A", doctor: "Dr. Farouk Alzoubani"}]},
                    {floor: 15, codes: [{code: "A", doctor: "Assem Drwesh Mostafa Abdulnabi"}]},
                    {floor: 16, codes: [{code: "A", doctor: "One Day to Manage Projects Co."}]},
                    {floor: 17, codes: [{code: "A", doctor: "Dr. Abdullah Sadad Sabri Al-Ozairi"}]},
                    {floor: "18-19", codes: [{code: "A", doctor: "Gulf Care Co."}]}
                ]
            },
            {
                name: "Medical Harbour",
                floors: [
                    {floor: 1, codes: [{code: "C", doctor: "Moaeyed Zaid Al Saq'abi"}]},
                    {floor: 2, codes: [{code: "C", doctor: "Mohamed Abdul Majid Hassan"}]},
                    {floor: 3, codes: [{code: "C", doctor: "Salah El Din Mohamed El Sherbini"}]},
                    {floor: 4, codes: [{code: "C", doctor: "Youssef Al Khleify/Rawan Al Khatib"}]},
                    {floor: 8, codes: [{code: "C", doctor: "Amir Eissa Attia Killa"}]},
                    {floor: 9, codes: [{code: "C", doctor: "Dr. Hesham Mohamed Yassin Ibrahim"}]},
                    {floor: 10, codes: [{code: "C", doctor: "Med Vision Medical Services"}]},
                    {floor: 11, codes: [{code: "C", doctor: "Fatmah Mohamed Badawy"}]},
                    {floor: 12, codes: [{code: "C", doctor: "Othman Youssef Al Mas'oud"}]},
                    {floor: 13, codes: [{code: "C", doctor: "Btissam Ibn Kiran"}]},
                    {floor: 14, codes: [{code: "C", doctor: "Misha'al Al Dahsh"}]},
                    {floor: 15, codes: [{code: "C", doctor: "Amal Al Shaiji / Faisal Al Terkeet"}]},
                    {floor: 16, codes: [{code: "C", doctor: "Signofa Co./Ahmed Eissa"}]},
                    {floor: 17, codes: [{code: "C", doctor: "Waleed Hamid Raheel"}]},
                    {floor: 18, codes: [{code: "C", doctor: "Eman Ghorab"}]},
                    {floor: 19, codes: [{code: "C", doctor: "Emad Morkos/Ahmed Youssef"}]},
                    {floor: 20, codes: [{code: "C", doctor: "Mohamed Al Kolk"}]},
                    {floor: 21, codes: [{code: "C", doctor: "Youssef Al Khleify"}]}
                ]
            },
            {
                name: "Med Marine",
                floors: [
                    {floor: 5, codes: [
                        {code: "A", doctor: "Fatima Ne'ma Al Awadhi"},
                        {code: "B", doctor: "Mohamed As'ad Eid/Wael Bezrah"}
                    ]},
                    {floor: 6, codes: [{code: "A+B", doctor: "Mohamed Youssef Al Sabty"}]},
                    {floor: 7, codes: [{code: "A+B", doctor: "Mostafa Mohamed Tomsu"}]}
                ]
            },
            {
                name: "JOYA - Polyclinic",
                floors: [
                    {floor: 8, codes: [
                        {code: "A", doctor: "Ihab Mohamed Younes Omar"},
                        {code: "B", doctor: "Huda Mahmoud Selim"}
                    ]},
                    {floor: 9, codes: [{code: "A+B", doctor: "Berlin Co./Mohamed Riyadh"}]},
                    {floor: 10, codes: [{code: "A+B", doctor: "Shehta Mostafa Ze'reb"}]}
                ]
            },
            {
                name: "Med Grey",
                floors: [
                    {floor: 5, codes: [{code: "A", doctor: "Dr. Amr Nabil Qutb"}]},
                    {floor: "6-7", codes: [{code: "A", doctor: "Dr. Shehta Mostafa Zurub"}]}
                ]
            },
            {
                name: "Aram - Polyclinic",
                floors: [
                    {floor: 2, codes: [{code: "A+B", doctor: "Dalia/Mina/Osama/Mahmoud"}]},
                    {floor: 3, codes: [{code: "A+B", doctor: "Ayman/Islam"}]},
                    {floor: 4, codes: [
                        {code: "A", doctor: "Mohamed Al Sayyad"},
                        {code: "B", doctor: "Mohamed Al Sayyad"}
                    ]},
                    {floor: 5, codes: [
                        {code: "A", doctor: "Bishoy/Mina/Zaher"},
                        {code: "B", doctor: "Nasser/Mohamed"}
                    ]},
                    {floor: 6, codes: [
                        {code: "A", doctor: "Munira/Anjoud"},
                        {code: "B", doctor: "Munira/Anjoud"}
                    ]},
                    {floor: 7, codes: [{code: "A+B", doctor: "Sondos Ghaneim"}]},
                    {floor: 8, codes: [
                        {code: "A", doctor: "Marina/Mary/Mariana"},
                        {code: "B", doctor: "Dr. Mohammed Salem"}
                    ]},
                    {floor: 9, codes: [
                        {code: "A", doctor: "Rwda Ahmed"},
                        {code: "B", doctor: "Marawan Essam"}
                    ]}
                ]
            }
        ];

        function createChart() {
            const chart = document.getElementById('chart');
            chart.innerHTML = ''; // Clear previous content

            // Create top-level nodes
            const medicalLabsNode = createNode('Medical Labs', 'medical-labs', '<h3>Medical Labs</h3>');
            chart.appendChild(medicalLabsNode);
            chart.appendChild(createConnector());

            const cloverNode = createNode('Clover', 'clover', '<h3>Clover</h3>');
            chart.appendChild(cloverNode);
            chart.appendChild(createConnector());

            const clinicColumnsContainer = document.createElement('div');
            clinicColumnsContainer.className = 'clinic-columns-container';
            chart.appendChild(clinicColumnsContainer);


            clinicData.forEach(clinic => {
                // Create a column for each clinic
                const clinicColumn = document.createElement('div');
                clinicColumn.className = 'clinic-column';

                // Create and append clinic node
                const clinicNode = createNode(clinic.name, 'clinic', `<h3>${clinic.name}</h3>`);
                clinicColumn.appendChild(clinicNode);

                // Process floors and codes
                clinic.floors.forEach(floor => {
                    clinicColumn.appendChild(createConnector());
                    const floorNode = createNode(`Floor ${floor.floor}`, 'floor', `<h4>Floor ${floor.floor}</h4>`);
                    clinicColumn.appendChild(floorNode);

                    floor.codes.forEach(code => {
                        clinicColumn.appendChild(createConnector());
                        const codeContent = `<strong>Clinic ${code.code}</strong><br><small>${code.doctor}</small>`;
                        const codeNode = createNode('', 'clinic-code', codeContent);
                        clinicColumn.appendChild(codeNode);
                    });
                });

                clinicColumnsContainer.appendChild(clinicColumn);
            });
        }

        function createNode(name, type, innerHTML) {
            const node = document.createElement('div');
            node.className = `node ${type}`;
            node.innerHTML = innerHTML;
            return node;
        }

        function createConnector() {
            const connector = document.createElement('div');
            connector.className = 'connector';
            return connector;
        }

        // Initialize chart
        createChart();
    </script>
</body>
</html>