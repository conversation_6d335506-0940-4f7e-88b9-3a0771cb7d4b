
import React from 'react';
import OrganizationChart from './components/OrganizationChart';
import { ORGANIZATION_DATA } from './data/chartData';

const App: React.FC = () => {
  return (
    <div className="min-h-screen bg-slate-100 text-slate-800 p-4 sm:p-8">
      <header className="mb-6 text-center sm:text-left">
        <h1 className="text-2xl sm:text-3xl font-bold text-indigo-700">
          Polyclinic Organization Chart 
        </h1>
        <p className="text-sm text-slate-600">(Left-to-Right Tree View)</p>
      </header>
      <main className="bg-white p-4 sm:p-6 rounded-lg shadow-xl overflow-auto">
        <OrganizationChart data={ORGANIZATION_DATA} />
      </main>
      <footer className="mt-8 text-center text-xs text-slate-500">
        Chart data visualized from provided information.
      </footer>
    </div>
  );
};

export default App;
