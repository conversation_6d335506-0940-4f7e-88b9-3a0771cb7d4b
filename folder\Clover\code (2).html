<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Polyclinic Organizational Chart</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .controls {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
        }

        .search-container {
            flex: 1;
            min-width: 300px;
            position: relative;
        }

        #searchInput {
            width: 100%;
            padding: 12px 45px 12px 15px;
            border: 2px solid #dee2e6;
            border-radius: 25px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        #searchInput:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.25);
        }

        .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }

        .filter-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 8px 16px;
            border: 2px solid #007bff;
            background: white;
            color: #007bff;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .filter-btn:hover, .filter-btn.active {
            background: #007bff;
            color: white;
        }

        .stats {
            display: flex;
            gap: 20px;
            margin-left: auto;
        }

        .stat-item {
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            min-width: 80px;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #007bff;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 2px;
        }

        .content {
            padding: 30px;
        }

        .clinic-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
            margin-top: 20px;
        }

        .clinic-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .clinic-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .clinic-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            cursor: pointer;
            position: relative;
        }

        .clinic-header h3 {
            font-size: 1.3rem;
            margin-bottom: 5px;
        }

        .clinic-meta {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .expand-icon {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.2rem;
            transition: transform 0.3s ease;
        }

        .clinic-card.expanded .expand-icon {
            transform: translateY(-50%) rotate(180deg);
        }

        .clinic-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .clinic-card.expanded .clinic-content {
            max-height: 1000px;
        }

        .floor-section {
            padding: 15px 20px;
            border-bottom: 1px solid #f1f3f4;
        }

        .floor-section:last-child {
            border-bottom: none;
        }

        .floor-header {
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .codes-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .code-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 10px 12px;
            flex: 1;
            min-width: 200px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .code-item:hover {
            background: #e9ecef;
            transform: scale(1.02);
        }

        .code-label {
            font-weight: bold;
            color: #007bff;
            font-size: 0.9rem;
        }

        .doctor-name {
            color: #495057;
            font-size: 0.85rem;
            margin-top: 3px;
            line-height: 1.3;
        }

        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }

        .no-results {
            text-align: center;
            padding: 40px;
            color: #6c757d;
            font-size: 1.1rem;
        }

        .result-count {
            margin: 20px 0;
            padding: 10px 15px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            color: #155724;
            font-weight: bold;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 30px;
            border-radius: 12px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            color: #000;
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .stats {
                margin-left: 0;
                justify-content: center;
            }

            .clinic-grid {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2rem;
            }

            .codes-container {
                flex-direction: column;
            }

            .code-item {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 Polyclinic Organization Chart</h1>
            <p>Interactive Medical Facilities Directory</p>
        </div>

        <div class="controls">
            <div class="search-container">
                <input type="text" id="searchInput" placeholder="Search clinics, doctors, floors...">
                <span class="search-icon">🔍</span>
            </div>
            
            <div class="filter-buttons">
                <button class="filter-btn active" data-filter="all">All</button>
                <button class="filter-btn" data-filter="doctors">Doctors</button>
                <button class="filter-btn" data-filter="companies">Companies</button>
            </div>

            <div class="stats">
                <div class="stat-item">
                    <div class="stat-number" id="totalClinics">0</div>
                    <div class="stat-label">Clinics</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalFloors">0</div>
                    <div class="stat-label">Floors</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalEntries">0</div>
                    <div class="stat-label">Entries</div>
                </div>
            </div>
        </div>

        <div class="content">
            <div id="resultCount" class="result-count" style="display: none;"></div>
            <div id="clinicGrid" class="clinic-grid"></div>
            <div id="noResults" class="no-results" style="display: none;">
                <h3>No results found</h3>
                <p>Try adjusting your search terms or filters</p>
            </div>
        </div>
    </div>

    <!-- Modal for detailed view -->
    <div id="detailModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="modalContent"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const clinicData = [
                {
                    "name": "Iris - Polyclinic Mazaya 3",
                    "floors": [
                        {
                            "floor": 2,
                            "codes": [
                                {"code": "A", "doctor": "Abdullah Abdul Aziz Al Hajri"},
                                {"code": "B", "doctor": "Smart Health Co."},
                                {"code": "C", "doctor": "Walid Hamad Ashwi Raheel"}
                            ]
                        },
                        {
                            "floor": 3,
                            "codes": [
                                {"code": "A+B", "doctor": "Amr Nabil Qutb"},
                                {"code": "C", "doctor": "Oxcana Bogdanovic"}
                            ]
                        },
                        {
                            "floor": 4,
                            "codes": [
                                {"code": "A", "doctor": "Hassaan A Jaber & Obaid Metni"},
                                {"code": "B", "doctor": "Mohamed Youssef Al Eissa"},
                                {"code": "C", "doctor": "Dr. Mariam Abed Ali Al-Turki"}
                            ]
                        }
                    ]
                },
                {
                    "name": "Al Aseel International",
                    "floors": [
                        {
                            "floor": 7,
                            "codes": [{"code": "C", "doctor": "Daniel Alain"}]
                        },
                        {
                            "floor": 8,
                            "codes": [
                                {"code": "A", "doctor": "Abdullah Abdul Rahman Al Hassan"},
                                {"code": "B", "doctor": "Hossam Mohamed El Badri"},
                                {"code": "C", "doctor": "Mustafa Samy Al Kaddousy"}
                            ]
                        },
                        {
                            "floor": 9,
                            "codes": [
                                {"code": "A", "doctor": "Nasser Faisal Al Mutairy"},
                                {"code": "B", "doctor": "Andro George Mikha'eel"},
                                {"code": "C", "doctor": "Dr. Noor Aladdin Alomar & Dr. Aya Samara"}
                            ]
                        },
                        {
                            "floor": 10,
                            "codes": [
                                {"code": "A+B", "doctor": "Dr. Ali Al-Mukaimi & Nisreen Baeij"},
                                {"code": "C", "doctor": "Dr. Ali Al-Mukaimi"}
                            ]
                        }
                    ]
                },
                {
                    "name": "Yarow - Polyclinic",
                    "floors": [
                        {
                            "floor": 11,
                            "codes": [
                                {"code": "A", "doctor": "Dr. Ahmed Abdulsamad Yehya Jassem"},
                                {"code": "B", "doctor": "Dr. Osamah J M Albaker"},
                                {"code": "C", "doctor": "Hossam Mohamed El Badri"}
                            ]
                        },
                        {
                            "floor": 12,
                            "codes": [
                                {"code": "A", "doctor": "Ahmed Mohamed Ahmed Ibrahim"},
                                {"code": "B", "doctor": "Sale Abdul Ghaffar Ma'arafie"},
                                {"code": "C", "doctor": "Adnan Ibrahim Ibrahim"}
                            ]
                        }
                    ]
                },
                {
                    "name": "Fourth Medical Center",
                    "floors": [
                        {"floor": 1, "codes": [{"code": "A", "doctor": "Salam Attar"}]},
                        {"floor": "2-3", "codes": [{"code": "A", "doctor": "One Day to Manage Projects Co."}]},
                        {"floor": "4-5", "codes": [{"code": "A", "doctor": "Athba Co."}]},
                        {"floor": 6, "codes": [{"code": "A", "doctor": "Health Care Co."}]},
                        {"floor": 7, "codes": [{"code": "A", "doctor": "Abdul Aziz Fahad Al Mezeiny"}]},
                        {"floor": 13, "codes": [{"code": "A", "doctor": "Revolution Medical Co."}]},
                        {"floor": 14, "codes": [{"code": "A", "doctor": "Dr. Farouk Alzoubani"}]},
                        {"floor": 15, "codes": [{"code": "A", "doctor": "Assem Drwesh Mostafa Abdulnabi"}]},
                        {"floor": 16, "codes": [{"code": "A", "doctor": "One Day to Manage Projects Co."}]},
                        {"floor": 17, "codes": [{"code": "A", "doctor": "Dr. Abdullah Sadad Sabri Al-Ozairi"}]},
                        {"floor": "18-19", "codes": [{"code": "A", "doctor": "Gulf Care Co."}]}
                    ]
                },
                {
                    "name": "Medical Harbour",
                    "floors": [
                        {"floor": 1, "codes": [{"code": "C", "doctor": "Moaeyed Zaid Al Saq'abi"}]},
                        {"floor": 2, "codes": [{"code": "C", "doctor": "Mohamed Abdul Majid Hassan"}]},
                        {"floor": 3, "codes": [{"code": "C", "doctor": "Salah El Din Mohamed El Sherbini"}]},
                        {"floor": 4, "codes": [{"code": "C", "doctor": "Youssef Al Khleify/Rawan Al Khatib"}]},
                        {"floor": 8, "codes": [{"code": "C", "doctor": "Amir Eissa Attia Killa"}]},
                        {"floor": 9, "codes": [{"code": "C", "doctor": "Dr. Hesham Mohamed Yassin Ibrahim"}]},
                        {"floor": 10, "codes": [{"code": "C", "doctor": "Med Vision Medical Services"}]},
                        {"floor": 11, "codes": [{"code": "C", "doctor": "Fatmah Mohamed Badawy"}]},
                        {"floor": 12, "codes": [{"code": "C", "doctor": "Othman Youssef Al Mas'oud"}]},
                        {"floor": 13, "codes": [{"code": "C", "doctor": "Btissam Ibn Kiran"}]},
                        {"floor": 14, "codes": [{"code": "C", "doctor": "Misha'al Al Dahsh"}]},
                        {"floor": 15, "codes": [{"code": "C", "doctor": "Amal Al Shaiji / Faisal Al Terkeet"}]},
                        {"floor": 16, "codes": [{"code": "C", "doctor": "Signofa Co./Ahmed Eissa"}]},
                        {"floor": 17, "codes": [{"code": "C", "doctor": "Waleed Hamid Raheel"}]},
                        {"floor": 18, "codes": [{"code": "C", "doctor": "Eman Ghorab"}]},
                        {"floor": 19, "codes": [{"code": "C", "doctor": "Emad Morkos/Ahmed Youssef"}]},
                        {"floor": 20, "codes": [{"code": "C", "doctor": "Mohamed Al Kolk"}]},
                        {"floor": 21, "codes": [{"code": "C", "doctor": "Youssef Al Khleify"}]}
                    ]
                },
                {
                    "name": "Med Marine",
                    "floors": [
                        {
                            "floor": 5,
                            "codes": [
                                {"code": "A", "doctor": "Fatima Ne'ma Al Awadhi"},
                                {"code": "B", "doctor": "Mohamed As'ad Eid/Wael Bezrah"}
                            ]
                        },
                        {"floor": 6, "codes": [{"code": "A+B", "doctor": "Mohamed Youssef Al Sabty"}]},
                        {"floor": 7, "codes": [{"code": "A+B", "doctor": "Mostafa Mohamed Tomsu"}]}
                    ]
                },
                {
                    "name": "JOYA - Polyclinic",
                    "floors": [
                        {
                            "floor": 8,
                            "codes": [
                                {"code": "A", "doctor": "Ihab Mohamed Younes Omar"},
                                {"code": "B", "doctor": "Huda Mahmoud Selim"}
                            ]
                        },
                        {"floor": 9, "codes": [{"code": "A+B", "doctor": "Berlin Co./Mohamed Riyadh"}]},
                        {"floor": 10, "codes": [{"code": "A+B", "doctor": "Shehta Mostafa Ze'reb"}]}
                    ]
                },
                {
                    "name": "Med Grey",
                    "floors": [
                        {"floor": 5, "codes": [{"code": "A", "doctor": "Dr. Amr Nabil Qutb"}]},
                        {"floor": "6-7", "codes": [{"code": "A", "doctor": "Dr. Shehta Mostafa Zurub"}]}
                    ]
                },
                {
                    "name": "Aram - Polyclinic",
                    "floors": [
                        {"floor": 2, "codes": [{"code": "A+B", "doctor": "Dalia/Mina/Osama/Mahmoud"}]},
                        {"floor": 3, "codes": [{"code": "A+B", "doctor": "Ayman/Islam"}]},
                        {
                            "floor": 4,
                            "codes": [
                                {"code": "A", "doctor": "Mohamed Al Sayyad"},
                                {"code": "B", "doctor": "Mohamed Al Sayyad"}
                            ]
                        },
                        {
                            "floor": 5,
                            "codes": [
                                {"code": "A", "doctor": "Bishoy/Mina/Zaher"},
                                {"code": "B", "doctor": "Nasser/Mohamed"}
                            ]
                        },
                        {
                            "floor": 6,
                            "codes": [
                                {"code": "A", "doctor": "Munira/Anjoud"},
                                {"code": "B", "doctor": "Munira/Anjoud"}
                            ]
                        },
                        {"floor": 7, "codes": [{"code": "A+B", "doctor": "Sondos Ghaneim"}]},
                        {
                            "floor": 8,
                            "codes": [
                                {"code": "A", "doctor": "Marina/Mary/Mariana"},
                                {"code": "B", "doctor": "Dr. Mohammed Salem"}
                            ]
                        },
                        {
                            "floor": 9,
                            "codes": [
                                {"code": "A", "doctor": "Rwda Ahmed"},
                                {"code": "B", "doctor": "Marawan Essam"}
                            ]
                        }
                    ]
                }
            ];

            let currentFilter = 'all';
            let currentSearch = '';

            // Update stats based on full dataset
            function updateStats() {
                const totalClinics = clinicData.length;
                const totalFloors = clinicData.reduce((sum, clinic) => sum + clinic.floors.length, 0);
                const totalEntries = clinicData.reduce((sum, clinic) => sum + clinic.floors.reduce((floorSum, floor) => floorSum + floor.codes.length, 0), 0);
                
                document.getElementById('totalClinics').textContent = totalClinics;
                document.getElementById('totalFloors').textContent = totalFloors;
                document.getElementById('totalEntries').textContent = totalEntries;
            }

            // Debounce function for search
            function debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }

            // Handle search
            function handleSearch(e) {
                currentSearch = e.target.value.toLowerCase().trim();
                renderClinics();
            }

            // Handle filter
            function handleFilter(e) {
                document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
                e.target.classList.add('active');
                currentFilter = e.target.dataset.filter;
                renderClinics();
            }

            // Check if entry matches current filter
            function matchesFilter(doctor) {
                if (currentFilter === 'all') return true;
                const isCo = doctor.toLowerCase().includes('co.') || doctor.toLowerCase().includes('company') || doctor.toLowerCase().includes('services');
                if (currentFilter === 'companies') return isCo;
                if (currentFilter === 'doctors') return !isCo;
                return true;
            }

            // Check if entry matches search
            function matchesSearch(clinic, floor, code) {
                if (!currentSearch) return true;
                const searchableText = [
                    clinic.name,
                    `floor ${floor.floor}`,
                    code.code,
                    code.doctor
                ].join(' ').toLowerCase();
                return searchableText.includes(currentSearch);
            }

            // Highlight search terms
            function highlightText(text, search) {
                if (!search) return text;
                const regex = new RegExp(`(${search.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')})`, 'gi');
                return text.replace(regex, '<span class="highlight">$1</span>');
            }

            // Render clinics
            function renderClinics() {
                const grid = document.getElementById('clinicGrid');
                const resultCountEl = document.getElementById('resultCount');
                const noResultsEl = document.getElementById('noResults');
                
                let filteredClinics = [];
                let totalMatches = 0;

                clinicData.forEach(clinic => {
                    const filteredFloors = [];
                    clinic.floors.forEach(floor => {
                        const filteredCodes = floor.codes.filter(code => {
                            const filterMatch = matchesFilter(code.doctor);
                            const searchMatch = matchesSearch(clinic, floor, code);
                            return filterMatch && searchMatch;
                        });
                        
                        if (filteredCodes.length > 0) {
                            filteredFloors.push({ ...floor, codes: filteredCodes });
                            totalMatches += filteredCodes.length;
                        }
                    });
                    
                    if (filteredFloors.length > 0) {
                        filteredClinics.push({ ...clinic, floors: filteredFloors });
                    }
                });

                if (currentSearch || currentFilter !== 'all') {
                    resultCountEl.style.display = 'block';
                    resultCountEl.textContent = `Found ${totalMatches} matching ${totalMatches === 1 ? 'entry' : 'entries'} in ${filteredClinics.length} ${filteredClinics.length === 1 ? 'clinic' : 'clinics'}.`;
                } else {
                    resultCountEl.style.display = 'none';
                }

                grid.style.display = filteredClinics.length > 0 ? 'grid' : 'none';
                noResultsEl.style.display = filteredClinics.length === 0 ? 'block' : 'none';

                grid.innerHTML = filteredClinics.map(clinic => {
                    const totalFloors = clinic.floors.length;
                    const totalCodes = clinic.floors.reduce((sum, floor) => sum + floor.codes.length, 0);
                    
                    return `
                        <div class="clinic-card" data-clinic="${clinic.name}">
                            <div class="clinic-header">
                                <h3>${highlightText(clinic.name, currentSearch)}</h3>
                                <div class="clinic-meta">${totalFloors} floors • ${totalCodes} entries</div>
                                <span class="expand-icon">▼</span>
                            </div>
                            <div class="clinic-content">
                                ${clinic.floors.map(floor => `
                                    <div class="floor-section">
                                        <div class="floor-header">Floor ${highlightText(String(floor.floor), currentSearch)}</div>
                                        <div class="codes-container">
                                            ${floor.codes.map(code => `
                                                <div class="code-item" data-clinic="${clinic.name}" data-floor="${floor.floor}" data-code="${code.code}" data-doctor="${code.doctor}">
                                                    <div class="code-label">Clinic ${highlightText(code.code, currentSearch)}</div>
                                                    <div class="doctor-name">${highlightText(code.doctor, currentSearch)}</div>
                                                </div>
                                            `).join('')}
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    `;
                }).join('');
            }
            
            // Show details in modal
            function showDetails(clinicName, floor, code, doctor) {
                const modal = document.getElementById('detailModal');
                const modalContent = document.getElementById('modalContent');
                
                modalContent.innerHTML = `
                    <h2>📋 Clinic Details</h2>
                    <div style="margin: 20px 0; line-height: 1.6;">
                        <p><strong>🏥 Medical Facility:</strong> ${clinicName}</p>
                        <p><strong>🏢 Floor:</strong> ${floor}</p>
                        <p><strong>🚪 Clinic Code:</strong> ${code}</p>
                        <p><strong>👨‍⚕️ Doctor/Company:</strong> ${doctor}</p>
                    </div>
                    <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee;">
                        <small style="color: #6c757d;">Information is for directory purposes only.</small>
                    </div>
                `;
                modal.style.display = 'block';
            }

            // Setup all event listeners
            function setupEventListeners() {
                document.getElementById('searchInput').addEventListener('input', debounce(handleSearch, 300));
                
                document.querySelectorAll('.filter-btn').forEach(btn => {
                    btn.addEventListener('click', handleFilter);
                });

                const modal = document.getElementById('detailModal');
                document.querySelector('.close').addEventListener('click', () => modal.style.display = 'none');
                window.addEventListener('click', (e) => {
                    if (e.target === modal) modal.style.display = 'none';
                });

                // Use event delegation for dynamically created elements
                document.getElementById('clinicGrid').addEventListener('click', function(e) {
                    const header = e.target.closest('.clinic-header');
                    if (header) {
                        header.closest('.clinic-card').classList.toggle('expanded');
                        return;
                    }

                    const codeItem = e.target.closest('.code-item');
                    if (codeItem) {
                        const { clinic, floor, code, doctor } = codeItem.dataset;
                        showDetails(clinic, floor, code, doctor);
                        return;
                    }
                });
            }

            // Initial call
            updateStats();
            renderClinics();
            setupEventListeners();
        });
    </script>
</body>
</html>