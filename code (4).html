<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attendance Delay Calculator</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 20px auto;
            padding: 0 20px;
            background-color: #f9f9f9;
        }
        .container {
            background: #fff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #0056b3;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
        }
        input[type="file"] {
            border: 1px solid #ccc;
            display: inline-block;
            padding: 6px 12px;
            cursor: pointer;
            border-radius: 4px;
        }
        #resultsContainer table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 0.9em;
        }
        #resultsContainer th,
        #resultsContainer td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        #resultsContainer th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        #summaryContainer {
            margin-top: 30px;
            padding: 20px;
            border: 2px solid #007bff;
            background-color: #f0f8ff;
            border-radius: 8px;
        }
        #summaryContainer h2 {
            margin-top: 0;
            color: #007bff;
        }
        #summaryContainer p {
            font-size: 1.1em;
            margin: 10px 0;
        }
        #summaryContainer input[type="number"] {
            width: 90px;
            padding: 5px;
            font-size: 1em;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        strong {
            color: #d9534f;
        }
        .highlight {
            background-color: yellow !important;
        }
        .placeholder {
            text-align: center;
            padding: 40px;
            color: #777;
            border: 2px dashed #ccc;
            border-radius: 8px;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>Attendance Delay Analyzer</h1>
        <p>Upload your attendance history HTML file to automatically calculate and analyze delays.</p>
        
        <input type="file" id="attendanceFile" accept=".html, .htm">

        <div id="resultsContainer">
            <div class="placeholder">
                <p>Your processed attendance report will appear here.</p>
            </div>
        </div>

        <div id="summaryContainer" style="display:none;">
            <h2>Delay Summary</h2>
            <p>Total Calculated Delay: <strong><span id="totalDelaySpan">0</span> minutes</strong></p>
            <p>
                Adjustment (add or subtract minutes): 
                <input type="number" id="adjustmentInput" value="0">
            </p>
            <p>New Total Delay: <strong><span id="newTotalSpan">0</span> minutes</strong></p>
        </div>
    </div>

    <script>
        document.getElementById('attendanceFile').addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const htmlContent = e.target.result;
                    processAttendance(htmlContent);
                };
                reader.readAsText(file);
            }
        });

        document.getElementById('adjustmentInput').addEventListener('input', updateAdjustedTotal);

        function parseTimeToMinutes(timeStr) {
            if (!timeStr || !timeStr.includes(':')) return null;
            
            const [time, modifier] = timeStr.trim().split(' ');
            let [hours, minutes] = time.split(':').map(Number);
            
            if (modifier.toUpperCase() === 'PM' && hours < 12) {
                hours += 12;
            }
            if (modifier.toUpperCase() === 'AM' && hours === 12) { // Midnight case
                hours = 0;
            }
            return (hours * 60) + minutes;
        }

        function processAttendance(htmlContent) {
            const parser = new DOMParser();
            const doc = parser.parseFromString(htmlContent, 'text/html');
            const tables = doc.querySelectorAll('table#result');
            let totalDelayMinutes = 0;
            
            if (tables.length === 0) {
                alert("Could not find a valid attendance table in the uploaded file.");
                return;
            }

            tables.forEach(table => {
                // Add new header for the delay column
                const headerRow = table.querySelector('thead > tr:last-child');
                if (headerRow) {
                    const entryTimeHeader = headerRow.children[3];
                    const delayHeader = document.createElement('th');
                    delayHeader.scope = "col";
                    delayHeader.className = "text-center";
                    delayHeader.innerHTML = "Delay (minutes)";
                    entryTimeHeader.insertAdjacentElement('afterend', delayHeader);

                    // Adjust colspan of the header above
                    const mainHeaderRow = table.querySelector('thead > tr:first-child');
                    if (mainHeaderRow && mainHeaderRow.children.length > 2) {
                       mainHeaderRow.children[1].colSpan = 3; // "Shift Start"
                    }
                }

                // Process each data row
                const rows = table.querySelectorAll('tbody > tr');
                const gracePeriodMinutes = (8 * 60) + 30; // 8:30 AM

                rows.forEach(row => {
                    const cells = row.children;
                    const workTimeCell = cells[2];
                    const entryTimeCell = cells[3];
                    const delayCell = document.createElement('td');
                    delayCell.align = "center";
                    
                    let delay = 0;
                    if (workTimeCell && entryTimeCell && workTimeCell.textContent.trim() !== "") {
                        const entryTime = entryTimeCell.textContent.trim();
                        const entryMinutes = parseTimeToMinutes(entryTime);

                        if (entryMinutes !== null && entryMinutes > gracePeriodMinutes) {
                            delay = entryMinutes - gracePeriodMinutes;
                            totalDelayMinutes += delay;
                            entryTimeCell.classList.add('highlight');
                        }
                    }

                    delayCell.textContent = (workTimeCell.textContent.trim() === "") ? "" : delay;
                    entryTimeCell.insertAdjacentElement('afterend', delayCell);
                });
            });

            // Display results
            const resultsContainer = document.getElementById('resultsContainer');
            resultsContainer.innerHTML = '';
            tables.forEach(table => {
                // Clean up the totals row if it exists from the original file
                const totalsRow = Array.from(table.querySelectorAll('tbody > tr')).find(r => r.textContent.includes('Totals'));
                if (totalsRow) {
                   totalsRow.remove();
                }
                resultsContainer.appendChild(table);
            });
            
            // Update and show the summary
            document.getElementById('totalDelaySpan').textContent = totalDelayMinutes;
            document.getElementById('adjustmentInput').value = 0;
            document.getElementById('summaryContainer').style.display = 'block';
            updateAdjustedTotal();
        }

        function updateAdjustedTotal() {
            const totalDelay = parseInt(document.getElementById('totalDelaySpan').textContent, 10);
            const adjustment = parseInt(document.getElementById('adjustmentInput').value, 10) || 0;
            const newTotal = totalDelay + adjustment;
            document.getElementById('newTotalSpan').textContent = newTotal;
        }

    </script>
</body>
</html>