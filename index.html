<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>DeedFind - Professional Property Search</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    :root {
      /* Professional Color Palette */
      --background: 210 20% 98%;
      --foreground: 222 84% 4.9%;
      --card: 0 0% 100%;
      --card-foreground: 222 84% 4.9%;
      --popover: 0 0% 100%;
      --popover-foreground: 222 84% 4.9%;
      --primary: 221 83% 53%;
      --primary-foreground: 210 40% 98%;
      --secondary: 210 40% 96%;
      --secondary-foreground: 222 47% 11%;
      --muted: 210 40% 96%;
      --muted-foreground: 215 16% 47%;
      --accent: 210 40% 96%;
      --accent-foreground: 222 47% 11%;
      --destructive: 0 84% 60%;
      --destructive-foreground: 210 40% 98%;
      --border: 214 32% 91%;
      --input: 214 32% 91%;
      --ring: 221 83% 53%;
      --radius: 0.75rem;

      /* Professional Gradients */
      --gradient-primary: linear-gradient(135deg, hsl(221 83% 53%) 0%, hsl(221 83% 45%) 100%);
      --gradient-secondary: linear-gradient(135deg, hsl(210 40% 96%) 0%, hsl(210 40% 92%) 100%);
      --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
      --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
      --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
      --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    }

    .dark {
      --background: 222 84% 4.9%;
      --foreground: 210 40% 98%;
      --card: 222 84% 4.9%;
      --card-foreground: 210 40% 98%;
      --popover: 222 84% 4.9%;
      --popover-foreground: 210 40% 98%;
      --primary: 217 91% 60%;
      --primary-foreground: 222 84% 4.9%;
      --secondary: 217 32% 17%;
      --secondary-foreground: 210 40% 98%;
      --muted: 217 32% 17%;
      --muted-foreground: 215 20% 65%;
      --accent: 217 32% 17%;
      --accent-foreground: 210 40% 98%;
      --destructive: 0 62% 30%;
      --destructive-foreground: 210 40% 98%;
      --border: 217 32% 17%;
      --input: 217 32% 17%;
      --ring: 217 91% 60%;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
      background: hsl(var(--background));
      color: hsl(var(--foreground));
      line-height: 1.6;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    /* Professional animations */
    .animate-fade-in {
      animation: fadeIn 0.5s ease-in-out;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .animate-slide-up {
      animation: slideUp 0.3s ease-out;
    }

    @keyframes slideUp {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    /* Professional gradients */
    .bg-gradient-primary {
      background: var(--gradient-primary);
    }

    .bg-gradient-secondary {
      background: var(--gradient-secondary);
    }

    /* Professional shadows */
    .shadow-professional {
      box-shadow: var(--shadow-lg);
    }

    .shadow-card {
      box-shadow: var(--shadow-md);
    }

    /* Custom scrollbar */
    .custom-scrollbar::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    .custom-scrollbar::-webkit-scrollbar-track {
      background: hsl(var(--muted));
      border-radius: 3px;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb {
      background: hsl(var(--muted-foreground) / 0.3);
      border-radius: 3px;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
      background: hsl(var(--muted-foreground) / 0.5);
    }
  </style>

<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@18.2.0?dev",
    "react/": "https://esm.sh/react@18.2.0?dev/",
    "react-dom/client": "https://esm.sh/react-dom@18.2.0/client?dev",
    "react-dom/": "https://esm.sh/react-dom@18.2.0?dev/",
    "react/jsx-runtime": "https://esm.sh/react@18.2.0/jsx-runtime?dev",

    "@radix-ui/react-slot": "https://esm.sh/@radix-ui/react-slot@1.1.0",
    "@radix-ui/react-separator": "https://esm.sh/@radix-ui/react-separator@1.1.0",
    "@radix-ui/react-select": "https://esm.sh/@radix-ui/react-select@2.1.1",
    "@radix-ui/react-accordion": "https://esm.sh/@radix-ui/react-accordion@1.2.0",
    "@radix-ui/react-label": "https://esm.sh/@radix-ui/react-label@2.1.0",
    "@radix-ui/react-alert-dialog": "https://esm.sh/@radix-ui/react-alert-dialog@1.1.1",
    "@radix-ui/react-avatar": "https://esm.sh/@radix-ui/react-avatar@1.1.0",

    "lucide-react": "https://esm.sh/lucide-react@0.475.0",

    "class-variance-authority": "https://esm.sh/class-variance-authority@0.7.0",
    "clsx": "https://esm.sh/clsx@2.1.1",
    "tailwind-merge": "https://esm.sh/tailwind-merge@2.4.0",
    "xlsx": "https://esm.sh/xlsx@0.18.5"
  }
}
</script>
  <script type="module" crossorigin src="/assets/index-BncVGKCd.js"></script>
</head>
<body>
  <div id="root"></div>
</body>
</html>
<link rel="stylesheet" href="index.css">
