// Polyfill for older browsers
if (!NodeList.prototype.forEach) {
    NodeList.prototype.forEach = Array.prototype.forEach;
}

const clinicData = [
    {
        name: "Iris - Polyclinic Mazaya 3",
        floors: [
            {
                floor: 2,
                codes: [
                    {code: "A", doctor: "<PERSON>"},
                    {code: "B", doctor: "Smart Health Co."},
                    {code: "C", doctor: "<PERSON><PERSON><PERSON>"}
                ]
            },
            {
                floor: 3,
                codes: [
                    {code: "A+B", doctor: "<PERSON><PERSON>"},
                    {code: "<PERSON>", doctor: "<PERSON><PERSON><PERSON><PERSON>"}
                ]
            },
            {
                floor: 4,
                codes: [
                    {code: "A", doctor: "<PERSON><PERSON><PERSON> & <PERSON>"},
                    {code: "B", doctor: "<PERSON>"},
                    {code: "C", doctor: "Dr. <PERSON><PERSON>"}
                ]
            }
        ]
    },
    {
        name: "Al Aseel International",
        floors: [
            {
                floor: 7,
                codes: [{code: "<PERSON>", doctor: "<PERSON>"}]
            },
            {
                floor: 8,
                codes: [
                    {code: "A", doctor: "<PERSON>"},
                    {code: "B", doctor: "<PERSON><PERSON><PERSON>"},
                    {code: "C", doctor: "<PERSON>"}
                ]
            },
            {
                floor: 9,
                codes: [
                    {code: "A", doctor: "<PERSON><PERSON> F<PERSON>al <PERSON>"},
                    {code: "<PERSON>", doctor: "And<PERSON> <PERSON><PERSON>'e<PERSON>"},
                    {code: "<PERSON>", doctor: "Dr. Noor <PERSON><PERSON><PERSON><PERSON>r & Dr. Aya Samara"}
                ]
            },
            {
                floor: 10,
                codes: [
                    {code: "A+<PERSON>", doctor: "Dr. <PERSON>-<PERSON><PERSON> & Nis<PERSON> <PERSON>eij"},
                    {code: "C", doctor: "Dr. Ali Al-Mukaimi"}
                ]
            }
        ]
    },
    {
        name: "Yarow - Polyclinic",
        floors: [
            {
                floor: 11,
                codes: [
                    {code: "A", doctor: "Dr. Ahmed Abdulsamad Yehya Jassem"},
                    {code: "B", doctor: "Dr. Osamah J M Albaker"},
                    {code: "C", doctor: "Hossam Mohamed El Badri"}
                ]
            },
            {
                floor: 12,
                codes: [
                    {code: "A", doctor: "Ahmed Mohamed Ahmed Ibrahim"},
                    {code: "B", doctor: "Sale Abdul Ghaffar Ma'arafie"},
                    {code: "C", doctor: "Adnan Ibrahim Ibrahim"}
                ]
            }
        ]
    },
    {
        name: "Fourth Medical Center",
        floors: [
            {floor: 1, codes: [{code: "A", doctor: "Salam Attar"}]},
            {floor: "2-3", codes: [{code: "A", doctor: "One Day to Manage Projects Co."}]},
            {floor: "4-5", codes: [{code: "A", doctor: "Athba Co."}]},
            {floor: 6, codes: [{code: "A", doctor: "Health Care Co."}]},
            {floor: 7, codes: [{code: "A", doctor: "Abdul Aziz Fahad Al Mezeiny"}]},
            {floor: 13, codes: [{code: "A", doctor: "Revolution Medical Co."}]},
            {floor: 14, codes: [{code: "A", doctor: "Dr. Farouk Alzoubani"}]},
            {floor: 15, codes: [{code: "A", doctor: "Assem Drwesh Mostafa Abdulnabi"}]},
            {floor: 16, codes: [{code: "A", doctor: "One Day to Manage Projects Co."}]},
            {floor: 17, codes: [{code: "A", doctor: "Dr. Abdullah Sadad Sabri Al-Ozairi"}]},
            {floor: "18-19", codes: [{code: "A", doctor: "Gulf Care Co."}]}
        ]
    },
    {
        name: "Medical Harbour",
        floors: [
            {floor: 1, codes: [{code: "C", doctor: "Moaeyed Zaid Al Saq'abi"}]},
            {floor: 2, codes: [{code: "C", doctor: "Mohamed Abdul Majid Hassan"}]},
            {floor: 3, codes: [{code: "C", doctor: "Salah El Din Mohamed El Sherbini"}]},
            {floor: 4, codes: [{code: "C", doctor: "Youssef Al Khleify/Rawan Al Khatib"}]},
            {floor: 8, codes: [{code: "C", doctor: "Amir Eissa Attia Killa"}]},
            {floor: 9, codes: [{code: "C", doctor: "Dr. Hesham Mohamed Yassin Ibrahim"}]},
            {floor: 10, codes: [{code: "C", doctor: "Med Vision Medical Services"}]},
            {floor: 11, codes: [{code: "C", doctor: "Fatmah Mohamed Badawy"}]},
            {floor: 12, codes: [{code: "C", doctor: "Othman Youssef Al Mas'oud"}]},
            {floor: 13, codes: [{code: "C", doctor: "Btissam Ibn Kiran"}]},
            {floor: 14, codes: [{code: "C", doctor: "Misha'al Al Dahsh"}]},
            {floor: 15, codes: [{code: "C", doctor: "Amal Al Shaiji / Faisal Al Terkeet"}]},
            {floor: 16, codes: [{code: "C", doctor: "Signofa Co./Ahmed Eissa"}]},
            {floor: 17, codes: [{code: "C", doctor: "Waleed Hamid Raheel"}]},
            {floor: 18, codes: [{code: "C", doctor: "Eman Ghorab"}]},
            {floor: 19, codes: [{code: "C", doctor: "Emad Morkos/Ahmed Youssef"}]},
            {floor: 20, codes: [{code: "C", doctor: "Mohamed Al Kolk"}]},
            {floor: 21, codes: [{code: "C", doctor: "Youssef Al Khleify"}]}
        ]
    },
    {
        name: "Med Marine",
        floors: [
            {floor: 5, codes: [
                {code: "A", doctor: "Fatima Ne'ma Al Awadhi"},
                {code: "B", doctor: "Mohamed As'ad Eid/Wael Bezrah"}
            ]},
            {floor: 6, codes: [{code: "A+B", doctor: "Mohamed Youssef Al Sabty"}]},
            {floor: 7, codes: [{code: "A+B", doctor: "Mostafa Mohamed Tomsu"}]}
        ]
    },
    {
        name: "JOYA - Polyclinic",
        floors: [
            {floor: 8, codes: [
                {code: "A", doctor: "Ihab Mohamed Younes Omar"},
                {code: "B", doctor: "Huda Mahmoud Selim"}
            ]},
            {floor: 9, codes: [{code: "A+B", doctor: "Berlin Co./Mohamed Riyadh"}]},
            {floor: 10, codes: [{code: "A+B", doctor: "Shehta Mostafa Ze'reb"}]}
        ]
    },
    {
        name: "Med Grey",
        floors: [
            {floor: 5, codes: [{code: "A", doctor: "Dr. Amr Nabil Qutb"}]},
            {floor: "6-7", codes: [{code: "A", doctor: "Dr. Shehta Mostafa Zurub"}]}
        ]
    },
    {
        name: "Aram - Polyclinic",
        floors: [
            {floor: 2, codes: [{code: "A+B", doctor: "Dalia/Mina/Osama/Mahmoud"}]},
            {floor: 3, codes: [{code: "A+B", doctor: "Ayman/Islam"}]},
            {floor: 4, codes: [
                {code: "A", doctor: "Mohamed Al Sayyad"},
                {code: "B", doctor: "Mohamed Al Sayyad"}
            ]},
            {floor: 5, codes: [
                {code: "A", doctor: "Bishoy/Mina/Zaher"},
                {code: "B", doctor: "Nasser/Mohamed"}
            ]},
            {floor: 6, codes: [
                {code: "A", doctor: "Munira/Anjoud"},
                {code: "B", doctor: "Munira/Anjoud"}
            ]},
            {floor: 7, codes: [{code: "A+B", doctor: "Sondos Ghaneim"}]},
            {floor: 8, codes: [
                {code: "A", doctor: "Marina/Mary/Mariana"},
                {code: "B", doctor: "Dr. Mohammed Salem"}
            ]},
            {floor: 9, codes: [
                {code: "A", doctor: "Rwda Ahmed"},
                {code: "B", doctor: "Marawan Essam"}
            ]}
        ]
    }
];

function populateDropdown(selectId, options) {
    const selectElement = document.getElementById(selectId);
    selectElement.innerHTML = `<option value="">Filter by ${selectId.replace('FilterInput', '').replace('clinic', 'Clinic ').replace('floor', 'Floor').replace('doctor', 'Doctor ')}...</option>`;
    options.forEach(option => {
        const optionElement = document.createElement('option');
        optionElement.value = option;
        optionElement.textContent = option;
        selectElement.appendChild(optionElement);
    });
}

function createNode(name, type, innerHTML) {
    const node = document.createElement('div');
    node.className = `node ${type}`;
    node.innerHTML = innerHTML;
    
    // Add hover effects
    node.addEventListener('mouseenter', () => {
        node.style.transform = 'scale(1.02)';
        node.style.webkitTransform = 'scale(1.02)';
        node.style.boxShadow = '0 4px 15px rgba(0,0,0,0.2)';
    });
    node.addEventListener('mouseleave', () => {
        node.style.transform = 'scale(1)';
        node.style.webkitTransform = 'scale(1)';
        node.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';
    });

    // Add click handler
    node.addEventListener('click', () => {
        const content = node.querySelector('h3, h4, strong')?.textContent || '';
        alert(`Selected: ${content}\n${node.querySelector('small')?.textContent || ''}`);
    });

    return node;
}

function createConnector() {
    const connector = document.createElement('div');
    connector.className = 'connector';
    return connector;
}

document.addEventListener('DOMContentLoaded', (event) => {
    // Get elements
    const searchInput = document.getElementById('searchInput');
    const clinicFilterInput = document.getElementById('clinicFilterInput');
    const floorFilterInput = document.getElementById('floorFilterInput');
    const doctorFilterInput = document.getElementById('doctorFilterInput');

    // Define applyFilters function
    function applyFilters() {
        const generalFilter = searchInput.value.toLowerCase().trim();
        const clinicNameFilter = clinicFilterInput.value.toLowerCase().trim();
        const floorFilter = floorFilterInput.value.toLowerCase().trim();
        const doctorFilter = doctorFilterInput.value.toLowerCase().trim();
        
        let matchCount = 0;
        const chart = document.getElementById('chart');
        chart.innerHTML = ''; // Clear previous content

        // Populate dropdowns (only once or when data changes significantly)
        populateDropdown('clinicFilterInput', [...new Set(clinicData.map(c => c.name))].sort());
        populateDropdown('floorFilterInput', [...new Set(clinicData.flatMap(c => c.floors.map(f => f.floor)))].sort());
        populateDropdown('doctorFilterInput', [...new Set(clinicData.flatMap(c => c.floors.flatMap(f => f.codes.map(code => code.doctor))))].sort());


        // Create top-level nodes (these are always visible)
        const medicalLabsNode = createNode('Medical Labs', 'medical-labs', '<h3>Medical Labs</h3>');
        chart.appendChild(medicalLabsNode);
        chart.appendChild(createConnector());

        const cloverNode = createNode('Clover', 'clover', '<h3>Clover</h3>');
        chart.appendChild(cloverNode);
        chart.appendChild(createConnector());

        const clinicColumnsContainer = document.createElement('div');
        clinicColumnsContainer.className = 'clinic-columns-container';
        chart.appendChild(clinicColumnsContainer);

        clinicData.forEach(clinic => {
            let clinicMatches = false;
            const filteredFloors = clinic.floors.filter(floor => {
                let floorMatches = false;
                const filteredCodes = floor.codes.filter(code => {
                    const content = `${clinic.name} Floor ${floor.floor} Clinic ${code.code} ${code.doctor}`.toLowerCase();
                    const generalMatch = generalFilter === '' || content.includes(generalFilter);
                    const clinicMatch = clinicNameFilter === '' || clinic.name.toLowerCase().includes(clinicNameFilter);
                    const floorMatch = floorFilter === '' || String(floor.floor).toLowerCase().includes(floorFilter);
                    const doctorMatch = doctorFilter === '' || code.doctor.toLowerCase().includes(doctorFilter);
                    
                    const isMatch = generalMatch && clinicMatch && floorMatch && doctorMatch;
                    if (isMatch) {
                        matchCount++;
                        floorMatches = true;
                    }
                    return isMatch;
                });
                if (filteredCodes.length > 0) {
                    clinicMatches = true;
                }
                return filteredCodes.length > 0;
            });

            if (clinicMatches) {
                const clinicColumn = document.createElement('div');
                clinicColumn.className = 'clinic-column';
                clinicColumn.appendChild(createNode(clinic.name, 'clinic', `<h3>${clinic.name}</h3>`));
                clinicColumn.appendChild(createConnector()); // Connector after clinic name

                const floorsContainer = document.createElement('div');
                floorsContainer.className = 'floors-container';
                
                // Apply horizontal-floors class if only one clinic is filtered and no other filters are active
                if (clinicNameFilter && !generalFilter && !floorFilter && !doctorFilter) {
                    floorsContainer.classList.add('horizontal');
                }

                filteredFloors.forEach(floor => {
                    const floorGroup = document.createElement('div');
                    floorGroup.className = 'floor-group'; // Group floor and its doctors

                    floorGroup.appendChild(createNode(`Floor ${floor.floor}`, 'floor', `<h4>Floor ${floor.floor}</h4>`));
                    
                    floor.codes.forEach(code => {
                        floorGroup.appendChild(createConnector()); // Connector before each doctor
                        let codeContent = `<strong>Clinic ${code.code}</strong><br><small>${code.doctor}</small>`;
                        
                        // Highlight logic for general search
                        if (generalFilter) {
                            codeContent = codeContent.replace(
                                new RegExp(generalFilter, 'gi'), 
                                match => `<span class="highlight">${match}</span>`
                            );
                        }
                        const codeNode = createNode('', 'clinic-code', codeContent);
                        floorGroup.appendChild(codeNode);
                    });
                    floorsContainer.appendChild(floorGroup);
                });
                clinicColumn.appendChild(floorsContainer); // Append the floors container to the clinic column
                clinicColumnsContainer.appendChild(clinicColumn);
            }
        });
        const resultCount = document.getElementById('resultCount');
        if (generalFilter || clinicNameFilter || floorFilter || doctorFilter) {
            resultCount.textContent = matchCount > 0 
                ? `${matchCount} matching ${matchCount === 1 ? 'entry' : 'entries'} found` 
                : 'No results found';
            resultCount.className = matchCount > 0 ? 'success' : 'error-message';
        } else {
            resultCount.textContent = '';
        }
    }

    // Initial chart creation and event listeners
    applyFilters();

    searchInput.addEventListener('input', applyFilters);
    clinicFilterInput.addEventListener('change', applyFilters);
    floorFilterInput.addEventListener('change', applyFilters);
    doctorFilterInput.addEventListener('change', applyFilters);
});
