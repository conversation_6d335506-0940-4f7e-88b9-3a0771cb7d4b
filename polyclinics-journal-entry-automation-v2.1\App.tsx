
import React, { useState, useCallback, useRef } from 'react';
import { JournalEntry, ExtractedData, ExtractedTransaction } from './types';
import { extractTransactionsFromText } from './services/geminiService';
import { generateJournalEntries, convertToXLSX } from './services/journalService';
import { SpinnerIcon, ProcessIcon, DownloadIcon } from './components/icons';
import { OUTPUT_HEADER } from './constants';
import * as pdfjs from 'pdfjs-dist';

// Set worker path to a static CDN URL to ensure robust loading.
// This avoids dynamic URL construction errors and matches the version in the import map.
pdfjs.GlobalWorkerOptions.workerSrc = `https://esm.sh/pdfjs-dist@4.5.136/build/pdf.worker.mjs`;


const Header: React.FC = () => (
    <header className="w-full bg-gray-900 shadow-md p-4 flex flex-col sm:flex-row items-center justify-between">
        <div className="flex items-center">
            <ProcessIcon className="w-8 h-8 text-teal-400 mr-3" aria-hidden="true" />
            <h1 className="text-2xl font-bold text-gray-200">Journal Entry Automation</h1>
        </div>
        <p className="text-sm text-gray-400 mt-2 sm:mt-0">Prepared by Haitham Soliman Abdou</p>
    </header>
);

const ReviewData: React.FC<{ data: ExtractedData; onConfirm: () => void, onCancel: () => void }> = ({ data, onConfirm }) => (
  <div className="flex flex-col h-full">
    <div className="bg-gray-800 p-3 rounded-lg border border-gray-700">
      <p className="text-sm text-gray-400">Detected Account Name:</p>
      <p className="font-bold text-lg text-teal-400">{data.accountName}</p>
    </div>
    <div className="overflow-y-auto flex-grow my-4 rounded-lg border border-gray-700" style={{maxHeight: '400px'}}>
      <table className="min-w-full text-sm text-left text-gray-300">
        <thead className="text-xs text-teal-400 uppercase bg-gray-700/50 sticky top-0 backdrop-blur-sm">
          <tr>
            <th scope="col" className="px-4 py-2">Date</th>
            <th scope="col" className="px-4 py-2">Description</th>
            <th scope="col" className="px-4 py-2 text-right">Amount</th>
            <th scope="col" className="px-4 py-2 text-center">Type</th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-700">
          {data.transactions.map((tx: ExtractedTransaction, index: number) => (
            <tr key={index} className="hover:bg-gray-800">
              <td className="px-4 py-2 whitespace-nowrap">{tx.date}</td>
              <td className="px-4 py-2">{tx.description}</td>
              <td className="px-4 py-2 text-right font-mono">{tx.amount.toFixed(2)}</td>
              <td className="px-4 py-2 text-center">
                <span className={`px-2 py-1 text-xs font-semibold rounded-full ${tx.type === 'credit' ? 'bg-green-900 text-green-300' : 'bg-yellow-900 text-yellow-300'}`}>
                  {tx.type.toUpperCase()}
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
     <button
        onClick={onConfirm}
        className="w-full flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700 transition duration-200"
    >
        <ProcessIcon className="-ml-1 mr-3 h-5 w-5" aria-hidden="true"/>
        Confirm & Generate Journal Entries
    </button>
  </div>
);

const ResultsTable: React.FC<{ entries: JournalEntry[] }> = ({ entries }) => (
    <div className="bg-gray-900 mt-6">
        <h3 className="text-xl font-semibold text-gray-200 mb-4">Generated Journal Entries</h3>
        <div className="overflow-x-auto border border-gray-700 rounded-lg">
            <table className="min-w-full text-sm text-left text-gray-300">
                <thead className="text-xs text-teal-400 uppercase bg-gray-700/50">
                    <tr>
                        {OUTPUT_HEADER.map(h => (
                            <th key={h} scope="col" className="px-4 py-3 whitespace-nowrap">{h}</th>
                        ))}
                    </tr>
                </thead>
                <tbody>
                    {entries.map((entry, index) => (
                        <tr key={index} className="border-b border-gray-700 hover:bg-gray-800">
                           <td className="px-4 py-2 whitespace-nowrap">{entry.journalNumber}</td>
                            <td className="px-4 py-2 whitespace-nowrap">{entry.journalName}</td>
                            <td className="px-4 py-2 whitespace-nowrap">{entry.lineNum}</td>
                            <td className="px-4 py-2 whitespace-nowrap">{entry.postingDate}</td>
                            <td className="px-4 py-2 whitespace-nowrap">{entry.accountType}</td>
                            <td className="px-4 py-2 whitespace-nowrap">{entry.accountNo}</td>
                            <td className="px-4 py-2 whitespace-nowrap max-w-xs truncate" title={entry.description}>{entry.description}</td>
                            <td className="px-4 py-2 whitespace-nowrap text-right">{String(entry.debitAmount)}</td>
                            <td className="px-4 py-2 whitespace-nowrap text-right">{String(entry.creditAmount)}</td>
                            <td className="px-4 py-2 whitespace-nowrap">{entry.currencyCode}</td>
                            <td className="px-4 py-2 whitespace-nowrap">{entry.exchangeRate}</td>
                            <td className="px-4 py-2 whitespace-nowrap">{entry.offsetAccountType}</td>
                            <td className="px-4 py-2 whitespace-nowrap">{entry.offsetAccount}</td>
                            <td className="px-4 py-2 whitespace-nowrap">{entry.invoiceNo}</td>
                            <td className="px-4 py-2 whitespace-nowrap">{entry.documentNo}</td>
                            <td className="px-4 py-2 whitespace-nowrap">{entry.documentDate}</td>
                            <td className="px-4 py-2 whitespace-nowrap">{entry.dueDate}</td>
                            <td className="px-4 py-2 whitespace-nowrap">{entry.assetTransType}</td>
                            <td className="px-4 py-2 whitespace-nowrap">{entry.postingProfile}</td>
                            <td className="px-4 py-2 whitespace-nowrap">{entry.paymentMode}</td>
                            <td className="px-4 py-2 whitespace-nowrap">{entry.paymentReference}</td>
                            <td className="px-4 py-2 whitespace-nowrap">{entry.numberOfVoucher}</td>
                            <td className="px-4 py-2 whitespace-nowrap">{entry.activities}</td>
                            <td className="px-4 py-2 whitespace-nowrap">{entry.country}</td>
                            <td className="px-4 py-2 whitespace-nowrap">{entry.departments}</td>
                            <td className="px-4 py-2 whitespace-nowrap">{entry.projectId}</td>
                            <td className="px-4 py-2 whitespace-nowrap">{entry.propertyId}</td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    </div>
);


export default function App() {
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [extractedData, setExtractedData] = useState<ExtractedData | null>(null);
    const [journalEntries, setJournalEntries] = useState<JournalEntry[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const getTextFromPdf = async (file: File): Promise<string> => {
        const arrayBuffer = await file.arrayBuffer();
        const pdf = await pdfjs.getDocument({ data: arrayBuffer }).promise;
        let fullText = '';
        for (let i = 1; i <= pdf.numPages; i++) {
            const page = await pdf.getPage(i);
            const textContent = await page.getTextContent();
            const pageText = textContent.items.map(item => 'str' in item ? item.str : '').join(' ');
            fullText += pageText + '\n';
        }
        return fullText;
    };

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            setSelectedFile(file);
            setError(null);
            setExtractedData(null);
            setJournalEntries([]);
        }
    };
    
    const resetState = () => {
        setSelectedFile(null);
        setError(null);
        setExtractedData(null);
        setJournalEntries([]);
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    const handleProcess = useCallback(async () => {
        if (!selectedFile) {
            setError("Please upload a PDF file first.");
            return;
        }
        setIsLoading(true);
        setError(null);
        setExtractedData(null);
        setJournalEntries([]);

        try {
            const rawText = await getTextFromPdf(selectedFile);
            if (!rawText.trim()) {
              throw new Error("Could not extract any text from the PDF. It might be an image-based PDF or corrupted.");
            }
            const data: ExtractedData = await extractTransactionsFromText(rawText);
            setExtractedData(data);
        } catch (err: any) {
            setError(err.message || "An unknown error occurred.");
            setExtractedData(null);
        } finally {
            setIsLoading(false);
        }
    }, [selectedFile]);

    const handleGenerateJournals = useCallback(() => {
        if (!extractedData) return;
        try {
            const entries: JournalEntry[] = generateJournalEntries(extractedData);
            setJournalEntries(entries);
        } catch (err: any) {
            setError(err.message || "An error occurred while generating entries.");
            setJournalEntries([]);
        }
    }, [extractedData]);

    const handleDownload = useCallback(() => {
        if (journalEntries.length === 0) return;
        
        const xlsxContent = convertToXLSX(journalEntries);
        const blob = new Blob([xlsxContent], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        const link = document.createElement('a');
        if (link.href) {
            URL.revokeObjectURL(link.href);
        }
        link.href = URL.createObjectURL(blob);
        link.download = "JournalEntries.xlsx";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }, [journalEntries]);

    const getRightPanelTitle = () => {
        if (journalEntries.length > 0) return "3. Download Results";
        if (extractedData) return "2. Review Extracted Data";
        return "2. Review & Generate";
    };
    
    const getRightPanelDescription = () => {
        if (journalEntries.length > 0) return "Review the final journal entries. If they look correct, download the XLSX file.";
        if (extractedData) return "Verify the transactions extracted by the AI. Confirm to generate the journal entries.";
        return "After processing, your results will appear here.";
    }

    return (
        <div className="min-h-screen bg-gray-800 text-gray-200 font-sans">
            <Header />
            <main className="p-4 md:p-8">
                <div className="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Input Panel */}
                    <div className="bg-gray-900 p-6 rounded-lg shadow-lg">
                        <h2 className="text-xl font-semibold mb-3">1. Upload Bank Statement</h2>
                        <p className="text-sm text-gray-400 mb-4">
                           Select a bank statement in PDF format. The AI will extract transactions.
                        </p>
                        
                        <div 
                            className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-700 border-dashed rounded-md cursor-pointer hover:border-teal-500 transition-colors"
                            onClick={() => fileInputRef.current?.click()}
                            onDrop={(e) => { e.preventDefault(); handleFileChange({ target: { files: e.dataTransfer.files } } as any); }}
                            onDragOver={(e) => e.preventDefault()}
                        >
                            <div className="space-y-1 text-center">
                                <svg className="mx-auto h-12 w-12 text-gray-500" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                </svg>
                                <div className="flex text-sm text-gray-400">
                                    <p className="pl-1">
                                      {selectedFile ? 'File selected:' : 'Click or drag PDF to upload'}
                                    </p>
                                </div>
                                {selectedFile ? (
                                    <p className="text-xs text-teal-400 font-semibold">{selectedFile.name}</p>
                                ) : (
                                    <p className="text-xs text-gray-500">PDF up to 10MB</p>
                                )}
                            </div>
                        </div>
                        <input
                            ref={fileInputRef}
                            type="file"
                            accept=".pdf"
                            onChange={handleFileChange}
                            className="hidden"
                        />
                        
                        <button
                            onClick={handleProcess}
                            disabled={isLoading || !selectedFile}
                            className="mt-4 w-full flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-teal-500 hover:bg-teal-600 disabled:bg-gray-600 disabled:cursor-not-allowed transition duration-200"
                        >
                            {isLoading ? (
                                <>
                                    <SpinnerIcon className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" aria-hidden="true"/>
                                    Processing...
                                </>
                            ) : (
                                <>
                                    <ProcessIcon className="-ml-1 mr-3 h-5 w-5" aria-hidden="true"/>
                                    Process with AI
                                </>
                            )}
                        </button>
                        {error && <p className="mt-4 text-red-400 text-sm">{error}</p>}
                    </div>

                    {/* Output Panel */}
                    <div className="bg-gray-900 p-6 rounded-lg shadow-lg">
                        <h2 className="text-xl font-semibold mb-3">{getRightPanelTitle()}</h2>
                        <p className="text-sm text-gray-400 mb-4">
                           {getRightPanelDescription()}
                        </p>
                        
                        {isLoading && (
                            <div className="mt-6 flex flex-col items-center justify-center h-64 border-2 border-dashed border-gray-700 rounded-lg">
                                <SpinnerIcon className="animate-spin h-12 w-12 text-teal-400" aria-hidden="true"/>
                                <p className="mt-4 text-gray-400">AI is analyzing your document...</p>
                             </div>
                        )}

                        {!isLoading && !extractedData && journalEntries.length === 0 && (
                             <div className="mt-6 flex flex-col items-center justify-center h-64 border-2 border-dashed border-gray-700 rounded-lg">
                                <p className="text-gray-500">{ selectedFile ? 'Ready to process.' : 'Upload a file to begin.'}</p>
                             </div>
                        )}
                        
                        {!isLoading && extractedData && journalEntries.length === 0 && (
                            <ReviewData data={extractedData} onConfirm={handleGenerateJournals} onCancel={resetState}/>
                        )}

                        {journalEntries.length > 0 && (
                            <>
                                <button
                                    onClick={handleDownload}
                                    disabled={isLoading}
                                    className="w-full flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-gray-900 bg-teal-400 hover:bg-teal-300 disabled:bg-gray-700 disabled:text-gray-400 disabled:cursor-not-allowed transition duration-200"
                                >
                                    <DownloadIcon className="-ml-1 mr-3 h-5 w-5" aria-hidden="true"/>
                                    Download XLSX
                                </button>
                                <ResultsTable entries={journalEntries} />
                            </>
                        )}

                    </div>
                </div>
            </main>
        </div>
    );
}
