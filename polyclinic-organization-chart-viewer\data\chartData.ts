
import { OrganizationData, PolyclinicType } from '../types';

export const ORGANIZATION_DATA: OrganizationData = {
  polyclinics: [
    {
      id: 'poly-iris',
      name: 'Iris-Polyclinic Mazaya 3',
      floors: [
        {
          id: 'floor-iris-f2',
          name: 'Floor 2',
          clinics: [
            { id: 'clinic-iris-f2-ca', name: 'Clinic A', occupant: '<PERSON>' },
            { id: 'clinic-iris-f2-cb', name: 'Clinic B', occupant: 'Smart Health Co' },
            { id: 'clinic-iris-f2-cc', name: 'Clinic C' },
            { id: 'clinic-iris-f2-cab', name: 'Clinic A+B' },
          ],
        },
        {
          id: 'floor-iris-f3',
          name: 'Floor 3',
          clinics: [{ id: 'clinic-iris-f3-cab', name: 'Clinic A+B' }],
        },
      ],
    },
    {
      id: 'poly-aseel',
      name: 'Al Aseel International',
      floors: [
        {
          id: 'floor-aseel-f4',
          name: 'Floor 4',
          clinics: [
            { id: 'clinic-aseel-f4-ca', name: 'Clinic A', occupant: '<PERSON>' },
            { id: 'clinic-aseel-f4-cb', name: 'Clinic B' },
            { id: 'clinic-aseel-f4-cc', name: 'Clinic C' },
          ],
        },
        { id: 'floor-aseel-f7', name: 'Floor 7', clinics: [{ id: 'clinic-aseel-f7-cc', name: 'Clinic C' }] },
        {
          id: 'floor-aseel-f8',
          name: 'Floor 8',
          clinics: [
            { id: 'clinic-aseel-f8-ca', name: 'Clinic A', occupant: 'Mustafa Samy Al Kaddouty' },
            { id: 'clinic-aseel-f8-cb', name: 'Clinic B' },
            { id: 'clinic-aseel-f8-cc', name: 'Clinic C' },
          ],
        },
        {
          id: 'floor-aseel-f9',
          name: 'Floor 9',
          clinics: [
            { id: 'clinic-aseel-f9-ca', name: 'Clinic A' },
            { id: 'clinic-aseel-f9-cb', name: 'Clinic B' },
            { id: 'clinic-aseel-f9-cc', name: 'Clinic C' },
          ],
        },
        {
          id: 'floor-aseel-f10',
          name: 'Floor 10',
          clinics: [
            { id: 'clinic-aseel-f10-ca', name: 'Clinic A' },
            { id: 'clinic-aseel-f10-cb', name: 'Clinic B' },
            { id: 'clinic-aseel-f10-cc', name: 'Clinic C' },
          ],
        },
        {
          id: 'floor-aseel-f11',
          name: 'Floor 11',
          clinics: [
            { id: 'clinic-aseel-f11-ca', name: 'Clinic A', occupant: 'Ahmed' },
            { id: 'clinic-aseel-f11-cb', name: 'Clinic B' },
          ],
        },
        { id: 'floor-aseel-f12', name: 'Floor 12', clinics: [{ id: 'clinic-aseel-f12-cc', name: 'Clinic C', occupant: 'Sale Abdul Ghatbor Mara' }] },
      ],
    },
    {
      id: 'poly-yarow',
      name: 'Yarow - Polyclinic',
      floors: [
        { // Assuming an effective "Floor" for these clinics under Yarow
          id: 'floor-yarow-main',
          name: 'Main Section', // Placeholder floor name
          clinics: [
            { id: 'clinic-yarow-ca', name: 'Clinic A', occupant: 'Rahma' },
            { id: 'clinic-yarow-cb', name: 'Clinic B' },
          ],
        },
      ],
    },
    {
      id: 'poly-fourth',
      name: 'Fourth Medical Center',
      floors: [
        { id: 'floor-fourth-f1', name: 'Floor 1', clinics: [{ id: 'clinic-fourth-f1-ca', name: 'Clinic A' }] },
        { id: 'floor-fourth-f23', name: 'Floor 2-3', clinics: [{ id: 'clinic-fourth-f23-ca', name: 'Clinic A', occupant: 'garage Projects' }] },
        { id: 'floor-fourth-f45', name: 'Floor 4-5', clinics: [{ id: 'clinic-fourth-f45-ca', name: 'Clinic A' }] },
        { id: 'floor-fourth-f6', name: 'Floor 6', clinics: [{ id: 'clinic-fourth-f6-ca', name: 'Clinic A', occupant: 'Health Care Co' }] },
        { id: 'floor-fourth-f7', name: 'Floor 7', clinics: [{ id: 'clinic-fourth-f7-ca', name: 'Clinic A' }] },
        { id: 'floor-fourth-f13', name: 'Floor 13', clinics: [{ id: 'clinic-fourth-f13-ca', name: 'Clinic A', occupant: 'Evolution Medical Co.' }] },
        { id: 'floor-fourth-f14', name: 'Floor 14', clinics: [{ id: 'clinic-fourth-f14-ca', name: 'Clinic A' }] },
        { id: 'floor-fourth-f15', name: 'Floor 15', clinics: [{ id: 'clinic-fourth-f15-ca', name: 'Clinic A'}] }, // Added placeholder clinic A based on pattern
        { id: 'floor-fourth-f16', name: 'Floor 16', clinics: [{ id: 'clinic-fourth-f16-ca', name: 'Clinic A' }] },
        { id: 'floor-fourth-f17', name: 'Floor 17', clinics: [{ id: 'clinic-fourth-f17-ca', name: 'Clinic A', occupant: 'Abdullah Sadad Sabri Al-Ca' }] },
        { id: 'floor-fourth-f1819', name: 'Floor 18-19', clinics: [{ id: 'clinic-fourth-f1819-cc', name: 'Clinic C', occupant: 'Gulf Care Co' }] },
      ],
    },
     {
      id: 'poly-medicallabs',
      name: 'Medical Labs',
      type: PolyclinicType.SPECIAL,
      floors: [ 
        // No direct floors/clinics listed under "Medical Labs" itself in the detailed breakdown, only under "Clover".
        // If it were to have its own, they'd be here. For now, it's a top-level category.
      ],
    },
    {
      id: 'poly-clover',
      name: 'Clover',
      type: PolyclinicType.SPECIAL, // To indicate pink/purple styling
      floors: [
        { id: 'floor-clover-f1', name: 'Floor 1', clinics: [{ id: 'clinic-clover-f1-cc', name: 'Clinic C' }] },
        { id: 'floor-clover-f2', name: 'Floor 2', clinics: [{ id: 'clinic-clover-f2-cc', name: 'Clinic C', occupant: 'Mohamed Abdul Majid Hassan' }] },
        { id: 'floor-clover-f3', name: 'Floor 3', clinics: [{ id: 'clinic-clover-f3-cc', name: 'Clinic C' }] },
        { id: 'floor-clover-f4', name: 'Floor 4', clinics: [{ id: 'clinic-clover-f4-cc', name: 'Clinic C', occupant: 'Youssef Al Khleify/Rawa Alh' }] },
        { id: 'floor-clover-f8', name: 'Floor 8', clinics: [{ id: 'clinic-clover-f8-cc', name: 'Clinic C' }] },
        { id: 'floor-clover-f9', name: 'Floor 9', clinics: [{ id: 'clinic-clover-f9-cc', name: 'Clinic C', occupant: 'Dr. Hesham Mohamed sin' }] },
        { id: 'floor-clover-f10', name: 'Floor 10', clinics: [{ id: 'clinic-clover-f10-cc', name: 'Clinic C', occupant: 'ed Vision Medical Services' }] },
        { id: 'floor-clover-f11', name: 'Floor 11', clinics: [{ id: 'clinic-clover-f11-cc', name: 'Clinic C' }] },
      ],
    },
    {
      id: 'poly-harbour',
      name: 'Medical Harbour',
      floors: [
        { id: 'floor-harbour-f1', name: 'Floor 1', clinics: [{ id: 'clinic-harbour-f1-cc', name: 'Clinic C', occupant: 'Othmar Youssef All' }] },
        { id: 'floor-harbour-f13', name: 'Floor 13', clinics: [{ id: 'clinic-harbour-f13-cc', name: 'Clinic C' }] },
        { id: 'floor-harbour-f14', name: 'Floor 14', clinics: [{ id: 'clinic-harbour-f14-cc', name: 'Clinic C', occupant: 'Misha\'al All Dahsh' }] },
        { id: 'floor-harbour-f15', name: 'Floor 15', clinics: [{ id: 'clinic-harbour-f15-cc', name: 'Clinic C' }] }, // Added placeholder based on pattern
        { id: 'floor-harbour-f16', name: 'Floor 16', clinics: [{ id: 'clinic-harbour-f16-cc', name: 'Clinic C' }] },
        { id: 'floor-harbour-f17', name: 'Floor 17', clinics: [{ id: 'clinic-harbour-f17-cc', name: 'Clinic C' }] },
        { id: 'floor-harbour-f18', name: 'Floor 18', clinics: [{ id: 'clinic-harbour-f18-cc', name: 'Clinic C' }] }, // Added placeholder
        { id: 'floor-harbour-f19', name: 'Floor 19', clinics: [{ id: 'clinic-harbour-f19-cc', name: 'Clinic C', occupant: 'Emad Morkos Ahmed' }] },
        { id: 'floor-harbour-f20', name: 'Floor 20', clinics: [{ id: 'clinic-harbour-f20-cc', name: 'Clinic C' }] },
        { id: 'floor-harbour-f21', name: 'Floor 21', clinics: [{ id: 'clinic-harbour-f21-cc', name: 'Clinic C' }] },
      ],
    },
    {
      id: 'poly-medmarine',
      name: 'Med Marine',
      floors: [
        { id: 'floor-medmarine-f5', name: 'Floor 5', clinics: [{ id: 'clinic-medmarine-f5-c', name: 'Clinic' }] },
        { id: 'floor-medmarine-f6', name: 'Floor 6', clinics: [
            { id: 'clinic-medmarine-f6-cb', name: 'Clinic B' },
            { id: 'clinic-medmarine-f6-cab', name: 'Clinic A+B' }
          ]
        },
      ],
    },
    {
      id: 'poly-joya',
      name: 'JOYA - Polyclinic',
      floors: [
        { id: 'floor-joya-f7', name: 'Floor 7', clinics: [{ id: 'clinic-joya-f7-cab', name: 'Clinic A+B' }] },
        { id: 'floor-joya-f8', name: 'Floor 8', clinics: [
            { id: 'clinic-joya-f8-ca', name: 'Clinic A', occupant: 'Mohamed' },
            { id: 'clinic-joya-f8-cb', name: 'Clinic B' }
          ]
        },
        { id: 'floor-joya-f9', name: 'Floor 9', clinics: [{ id: 'clinic-joya-f9-cab', name: 'Clinic A+B', occupant: 'Berlin Co Mohamed Riyadh' }] },
        { id: 'floor-joya-f10', name: 'Floor 10', clinics: [{ id: 'clinic-joya-f10-cab', name: 'Clinic A+B' }] },
      ],
    },
    {
      id: 'poly-medgrey',
      name: 'Med Grey',
      floors: [
        { id: 'floor-medgrey-f5', name: 'Floor 5', clinics: [{ id: 'clinic-medgrey-f5-c', name: 'Clinic', occupant: 'Dr. Amr Nabil Qutb' }] },
        { id: 'floor-medgrey-f67', name: 'Floor 6-7', clinics: [{ id: 'clinic-medgrey-f67-cab', name: 'Clinic A+B' }] },
      ],
    },
    {
      id: 'poly-aram',
      name: 'Aram - Polyclinic',
      floors: [
        { id: 'floor-aram-f2', name: 'Floor 2', clinics: [{ id: 'clinic-aram-f2-c', name: 'Clinic' }] }, // Placeholder clinic
        { id: 'floor-aram-f3', name: 'Floor 3', clinics: [{ id: 'clinic-aram-f3-ca', name: 'Clinic A', occupant: 'Mohamed Al Sayyad' }] },
        { id: 'floor-aram-f4', name: 'Floor 4', clinics: [{ id: 'clinic-aram-f4-cb', name: 'Clinic B', occupant: 'Mohamed Al Sayyad' }] },
        { id: 'floor-aram-f5', name: 'Floor 5', clinics: [
            { id: 'clinic-aram-f5-ca', name: 'Clinic A' },
            { id: 'clinic-aram-f5-cb', name: 'Clinic B' }
          ]
        },
        { id: 'floor-aram-f6', name: 'Floor 6', clinics: [
            { id: 'clinic-aram-f6-ca', name: 'Clinic A', occupant: 'Anjou' },
            { id: 'clinic-aram-f6-cb', name: 'Clinic B', occupant: 'Munira Anjoud' }
          ]
        },
        { id: 'floor-aram-f7', name: 'Floor 7', clinics: [{ id: 'clinic-aram-f7-cab', name: 'Clinic A+B', occupant: 'Sondos Ghanem' }] },
        { id: 'floor-aram-f8', name: 'Floor 8', clinics: [
            { id: 'clinic-aram-f8-ca', name: 'Clinic A' },
            { id: 'clinic-aram-f8-c', name: 'Clinic' }
          ]
        },
        { id: 'floor-aram-f9', name: 'Floor 9', clinics: [{ id: 'clinic-aram-f9-ca', name: 'Clinic A' }] },
      ],
    },
  ],
};
