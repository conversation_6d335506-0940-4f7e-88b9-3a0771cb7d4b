
import React from 'react';

interface LoaderProps {
  message: string;
}

const Loader: React.FC<LoaderProps> = ({ message }) => {
  return (
    <div className="flex flex-col items-center justify-center p-8 text-center bg-white dark:bg-gray-800 rounded-2xl shadow-md border border-gray-200 dark:border-gray-700">
      <div className="relative flex items-center justify-center w-16 h-16">
        <div className="absolute w-full h-full border-4 border-indigo-200 dark:border-indigo-700 rounded-full"></div>
        <div className="absolute w-full h-full border-t-4 border-indigo-500 dark:border-indigo-400 rounded-full animate-spin"></div>
      </div>
      <p className="mt-4 text-lg font-medium text-gray-700 dark:text-gray-300">{message}</p>
      <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">Please wait while the AI works its magic...</p>
    </div>
  );
};

export default Loader;
