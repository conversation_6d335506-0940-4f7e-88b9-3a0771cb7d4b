
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Polyclinic Organization Chart</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    /* Custom scrollbar styling for a cleaner look */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 10px;
    }
    ::-webkit-scrollbar-thumb {
      background: #c7d2fe; /* Tailwind's indigo-200 */
      border-radius: 10px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #a5b4fc; /* Tailwind's indigo-300 */
    }
  </style>
<script type="importmap">
{
  "imports": {
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
<body class="bg-slate-100">
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>
