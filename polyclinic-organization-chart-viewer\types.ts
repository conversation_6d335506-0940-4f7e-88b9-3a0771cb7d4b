
export interface Clinic {
  id: string;
  name: string; // e.g., "Clinic A", "Clinic A+B"
  occupant?: string; // e.g., "<PERSON>", "Smart Health Co"
}

export interface Floor {
  id: string;
  name: string; // e.g., "Floor 2", "Floor 2-3"
  clinics: Clinic[];
  occupant?: string; // Sometimes a name is associated with the whole floor if no specific clinics detailed
}

export enum PolyclinicType {
  DEFAULT = 'default',
  SPECIAL = 'special', // For 'Medical Labs', 'Clover'
}

export interface Polyclinic {
  id: string;
  name: string;
  type?: PolyclinicType; // To handle special styling for 'Medical Labs' and 'Clover'
  floors: Floor[];
}

export interface OrganizationData {
  polyclinics: Polyclinic[];
}
