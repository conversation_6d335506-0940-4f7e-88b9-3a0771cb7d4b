<html><body>


    <title>Polyclinic Organizational Chart (Left-to-Right)</title>
    <style>
        /* General Setup */
        body { 
            font-family: 'Segoe UI', sans-serif; 
            background: #f0f2f5; 
            margin: 0;
        }
        h2 { 
            color: #1a237e; 
            text-align: center; 
            padding: 20px;
        }
        .chart-container {
            padding: 20px;
            overflow: auto; /* Allow scrolling for large charts */
            text-align: left; /* Align chart to the left */
        }

        /* --- Core LTR Tree Structure & Lines --- */
        .tree {
            display: inline-block; /* Allow the container to size to its content */
            white-space: nowrap; /* Prevent wrapping of tree elements */
        }
        .tree ul {
            padding-left: 25px;
            position: relative;
            list-style-type: none;
            margin: 0;
            /* LTR Change: Children are stacked vertically */
            display: flex;
            flex-direction: column;
        }
        .tree li {
            list-style-type: none;
            position: relative;
            padding: 10px 0 10px 25px;
            /* LTR Change: The node and its children list are arranged horizontally */
            display: flex;
            align-items: center; /* Vertically center node with connector */
        }

        /* --- Connector Lines --- */
        
        /* The vertical line connecting all children of a node */
        .tree ul::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            border-left: 2px solid #ccc;
            width: 0;
            height: 100%;
        }
        /* The horizontal line from the vertical trunk to the child node */
        .tree li::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            border-top: 2px solid #ccc;
            width: 25px;
            height: 0;
            margin-top: -1px; /* Center the 2px border */
        }
        
        /* --- Line Cleanup for Edges --- */
        
        /* Hide vertical line on the root node's list */
        .tree > ul::before {
            display: none;
        }
        /* Round the top of the vertical line for the first child */
        .tree li:first-child::before {
            border-radius: 5px 0 0 0;
        }
        .tree li:first-child::after {
             content: '';
             position: absolute;
             top: 0;
             left: 0;
             height: 50%;
             width: 2px;
             background: #f0f2f5; /* Use body background to "erase" the top part of the line */
        }
        /* Round the bottom of the vertical line for the last child */
        .tree li:last-child::before {
            border-radius: 0 0 0 5px;
        }
        .tree li:last-child::after {
             content: '';
             position: absolute;
             top: 50%;
             left: 0;
             height: 50%;
             width: 2px;
             background: #f0f2f5; /* Use body background to "erase" the bottom part of the line */
        }
        /* A single child doesn't need the vertical trunk line */
        .tree li:only-child::after {
            display: none;
        }

        /* --- Node Styling (Unchanged) --- */
        .node { 
            padding: 15px; 
            border-radius: 8px; 
            text-align: center;
            min-width: 220px;
            max-width: 280px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            position: relative;
            z-index: 1;
            background: #fff; /* Give nodes a solid background to sit on top of lines */
            flex-shrink: 0; /* Prevent node from shrinking */
        }

        /* Specific Node Type Styles */
        .node.medical-labs { background: #ffebee; border: 2px solid #c62828; }
        .node.clover { background: #f3e5f5; border: 2px solid #7b1fa2; }
        .node.clinic { background: #e3f2fd; border: 2px solid #1976d2; }
        .node.floor { background: #e8f5e9; border: 2px solid #388e3c; }
        .node.clinic-code { background: #fffde7; border: 2px solid #f9a825; min-width: 200px; }
        
        /* Typography within nodes */
        .node h3, .node h4, .node h5 { margin: 0; padding: 0; font-size: 1.1em; }
        .node small { display: block; margin-top: 5px; color: #555; }
    </style>


    <h2>Polyclinic Organization Chart (Left-to-Right)</h2>
    <div class="chart-container">
        <div id="chart"></div>
    </div>

    <script>
        const data = {
            name: "Medical Labs",
            children: [
                {
                    name: "Clover",
                    children: [
                        {
                            name: "Iris - Polyclinic Mazaya 3",
                            floors: [
                                {
                                    floor: 2,
                                    codes: [
                                        {code: "A", doctor: "Abdullah Abdul Aziz Al Hajri"},
                                        {code: "B", doctor: "Smart Health Co."},
                                        {code: "C", doctor: "Walid Hamad Ashwi Raheel"}
                                    ]
                                },
                                {
                                    floor: 3,
                                    codes: [
                                        {code: "A+B", doctor: "Amr Nabil Qutb"},
                                        {code: "C", doctor: "Oxcana Bogdanovic"}
                                    ]
                                },
                                {
                                    floor: 4,
                                    codes: [
                                        {code: "A", doctor: "Hassaan A Jaber & Obaid Metni"},
                                        {code: "B", doctor: "Mohamed Youssef Al Eissa"},
                                        {code: "C", doctor: "Dr. Mariam Abed Ali Al-Turki"}
                                    ]
                                }
                            ]
                        },
                        {
                            name: "Al Aseel International",
                            floors: [
                                {
                                    floor: 7,
                                    codes: [{code: "C", doctor: "Daniel Alain"}]
                                },
                                {
                                    floor: 8,
                                    codes: [
                                        {code: "A", doctor: "Abdullah Abdul Rahman Al Hassan"},
                                        {code: "B", doctor: "Hossam Mohamed El Badri"},
                                        {code: "C", doctor: "Mustafa Samy Al Kaddousy"}
                                    ]
                                },
                                {
                                    floor: 9,
                                    codes: [
                                        {code: "A", doctor: "Nasser Faisal Al Mutairy"},
                                        {code: "B", doctor: "Andro George Mikha'eel"},
                                        {code: "C", doctor: "Dr. Noor Aladdin Alomar & Dr. Aya Samara"}
                                    ]
                                },
                                {
                                    floor: 10,
                                    codes: [
                                        {code: "A+B", doctor: "Dr. Ali Al-Mukaimi & Nisreen Baeij"},
                                        {code: "C", doctor: "Dr. Ali Al-Mukaimi"}
                                    ]
                                }
                            ]
                        },
                        {
                            name: "Yarow - Polyclinic",
                            floors: [
                                {
                                    floor: 11,
                                    codes: [
                                        {code: "A", doctor: "Dr. Ahmed Abdulsamad Yehya Jassem"},
                                        {code: "B", doctor: "Dr. Osamah J M Albaker"},
                                        {code: "C", doctor: "Hossam Mohamed El Badri"}
                                    ]
                                },
                                {
                                    floor: 12,
                                    codes: [
                                        {code: "A", doctor: "Ahmed Mohamed Ahmed Ibrahim"},
                                        {code: "B", doctor: "Sale Abdul Ghaffar Ma'arafie"},
                                        {code: "C", doctor: "Adnan Ibrahim Ibrahim"}
                                    ]
                                }
                            ]
                        },
                        {
                            name: "Fourth Medical Center",
                            floors: [
                                {floor: 1, codes: [{code: "A", doctor: "Salam Attar"}]},
                                {floor: "2-3", codes: [{code: "A", doctor: "One Day to Manage Projects Co."}]},
                                {floor: "4-5", codes: [{code: "A", doctor: "Athba Co."}]},
                                {floor: 6, codes: [{code: "A", doctor: "Health Care Co."}]},
                                {floor: 7, codes: [{code: "A", doctor: "Abdul Aziz Fahad Al Mezeiny"}]},
                                {floor: 13, codes: [{code: "A", doctor: "Revolution Medical Co."}]},
                                {floor: 14, codes: [{code: "A", doctor: "Dr. Farouk Alzoubani"}]},
                                {floor: 15, codes: [{code: "A", doctor: "Assem Drwesh Mostafa Abdulnabi"}]},
                                {floor: 16, codes: [{code: "A", doctor: "One Day to Manage Projects Co."}]},
                                {floor: 17, codes: [{code: "A", doctor: "Dr. Abdullah Sadad Sabri Al-Ozairi"}]},
                                {floor: "18-19", codes: [{code: "A", doctor: "Gulf Care Co."}]}
                            ]
                        },
                        {
                            name: "Medical Harbour",
                            floors: [
                                {floor: 1, codes: [{code: "C", doctor: "Moaeyed Zaid Al Saq'abi"}]},
                                {floor: 2, codes: [{code: "C", doctor: "Mohamed Abdul Majid Hassan"}]},
                                {floor: 3, codes: [{code: "C", doctor: "Salah El Din Mohamed El Sherbini"}]},
                                {floor: 4, codes: [{code: "C", doctor: "Youssef Al Khleify/Rawan Al Khatib"}]},
                                {floor: 8, codes: [{code: "C", doctor: "Amir Eissa Attia Killa"}]},
                                {floor: 9, codes: [{code: "C", doctor: "Dr. Hesham Mohamed Yassin Ibrahim"}]},
                                {floor: 10, codes: [{code: "C", doctor: "Med Vision Medical Services"}]},
                                {floor: 11, codes: [{code: "C", doctor: "Fatmah Mohamed Badawy"}]},
                                {floor: 12, codes: [{code: "C", doctor: "Othman Youssef Al Mas'oud"}]},
                                {floor: 13, codes: [{code: "C", doctor: "Btissam Ibn Kiran"}]},
                                {floor: 14, codes: [{code: "C", doctor: "Misha'al Al Dahsh"}]},
                                {floor: 15, codes: [{code: "C", doctor: "Amal Al Shaiji / Faisal Al Terkeet"}]},
                                {floor: 16, codes: [{code: "C", doctor: "Signofa Co./Ahmed Eissa"}]},
                                {floor: 17, codes: [{code: "C", doctor: "Waleed Hamid Raheel"}]},
                                {floor: 18, codes: [{code: "C", doctor: "Eman Ghorab"}]},
                                {floor: 19, codes: [{code: "C", doctor: "Emad Morkos/Ahmed Youssef"}]},
                                {floor: 20, codes: [{code: "C", doctor: "Mohamed Al Kolk"}]},
                                {floor: 21, codes: [{code: "C", doctor: "Youssef Al Khleify"}]}
                            ]
                        },
                        {
                            name: "Med Marine",
                            floors: [
                                {floor: 5, codes: [
                                    {code: "A", doctor: "Fatima Ne'ma Al Awadhi"},
                                    {code: "B", doctor: "Mohamed As'ad Eid/Wael Bezrah"}
                                ]},
                                {floor: 6, codes: [{code: "A+B", doctor: "Mohamed Youssef Al Sabty"}]},
                                {floor: 7, codes: [{code: "A+B", doctor: "Mostafa Mohamed Tomsu"}]}
                            ]
                        },
                        {
                            name: "JOYA - Polyclinic",
                            floors: [
                                {floor: 8, codes: [
                                    {code: "A", doctor: "Ihab Mohamed Younes Omar"},
                                    {code: "B", doctor: "Huda Mahmoud Selim"}
                                ]},
                                {floor: 9, codes: [{code: "A+B", doctor: "Berlin Co./Mohamed Riyadh"}]},
                                {floor: 10, codes: [{code: "A+B", doctor: "Shehta Mostafa Ze'reb"}]}
                            ]
                        },
                        {
                            name: "Med Grey",
                            floors: [
                                {floor: 5, codes: [{code: "A", doctor: "Dr. Amr Nabil Qutb"}]},
                                {floor: "6-7", codes: [{code: "A", doctor: "Dr. Shehta Mostafa Zurub"}]}
                            ]
                        },
                        {
                            name: "Aram - Polyclinic",
                            floors: [
                                {floor: 2, codes: [{code: "A+B", doctor: "Dalia/Mina/Osama/Mahmoud"}]},
                                {floor: 3, codes: [{code: "A+B", doctor: "Ayman/Islam"}]},
                                {floor: 4, codes: [
                                    {code: "A", doctor: "Mohamed Al Sayyad"},
                                    {code: "B", doctor: "Mohamed Al Sayyad"}
                                ]},
                                {floor: 5, codes: [
                                    {code: "A", doctor: "Bishoy/Mina/Zaher"},
                                    {code: "B", doctor: "Nasser/Mohamed"}
                                ]},
                                {floor: 6, codes: [
                                    {code: "A", doctor: "Munira/Anjoud"},
                                    {code: "B", doctor: "Munira/Anjoud"}
                                ]},
                                {floor: 7, codes: [{code: "A+B", doctor: "Sondos Ghaneim"}]},
                                {floor: 8, codes: [
                                    {code: "A", doctor: "Marina/Mary/Mariana"},
                                    {code: "B", doctor: "Dr. Mohammed Salem"}
                                ]},
                                {floor: 9, codes: [
                                    {code: "A", doctor: "Rwda Ahmed"},
                                    {code: "B", doctor: "Marawan Essam"}
                                ]}
                            ]
                        }
                    ]
                }
            ]
        };

        /**
         * Recursively builds the DOM for a single branch of the tree.
         * @param {object} nodeData - The data object for the current node.
         * @param {string} nodeClass - The CSS class for the current node type.
         * @returns {HTMLLIElement} The list item element for the created branch.
         */
        function createBranch(nodeData, nodeClass) {
            const listItem = document.createElement('li');

            const nodeDiv = document.createElement('div');
            nodeDiv.className = `node ${nodeClass}`;

            // Create title based on node type
            let title;
            if (nodeClass === 'medical-labs') title = `<h3>${nodeData.name}</h3>`;
            else title = `<h4>${nodeData.name}</h4>`;
            nodeDiv.innerHTML = title;
            listItem.appendChild(nodeDiv);

            // Check for children (either in a 'children' or 'floors' array)
            const children = nodeData.children || nodeData.floors;
            
            if (children && children.length > 0) {
                const childrenList = document.createElement('ul');
                children.forEach(child => {
                    if (child.name) { // It's a standard child node
                        const nextClass = (nodeClass === 'medical-labs') ? 'clover' : 'clinic';
                        childrenList.appendChild(createBranch(child, nextClass));
                    } else if (child.floor) { // It's a special 'floor' node
                        childrenList.appendChild(createFloorBranch(child));
                    }
                });
                listItem.appendChild(childrenList);
            }
            return listItem;
        }

        /**
         * Creates a branch specifically for the 'floor' and 'code' data structure.
         * @param {object} floorData - The data object for the floor.
         * @returns {HTMLLIElement} The list item element for the floor and its codes.
         */
        function createFloorBranch(floorData) {
            const floorListItem = document.createElement('li');

            const floorDiv = document.createElement('div');
            floorDiv.className = 'node floor';
            floorDiv.innerHTML = `<h5>Floor ${floorData.floor}</h5>`;
            floorListItem.appendChild(floorDiv);

            // Create a sub-list for the clinic codes
            if (floorData.codes && floorData.codes.length > 0) {
                const codesList = document.createElement('ul');
                floorData.codes.forEach(code => {
                    const codeListItem = document.createElement('li');
                    const codeDiv = document.createElement('div');
                    codeDiv.className = 'node clinic-code';
                    codeDiv.innerHTML = `
                        <strong>Clinic ${code.code}</strong><br>
                        <small>${code.doctor}</small>
                    `;
                    // A code is a leaf node, so it has no children
                    codeListItem.appendChild(codeDiv);
                    codesList.appendChild(codeListItem);
                });
                floorListItem.appendChild(codesList);
            }
            return floorListItem;
        }
        
        /**
         * Initializes the chart building process.
         */
        function initializeChart() {
            const chartContainer = document.getElementById('chart');
            if (!data) return;

            chartContainer.className = 'tree'; // Add base class for CSS styling
            const rootList = document.createElement('ul');
            const rootNode = createBranch(data, 'medical-labs');
            
            rootList.appendChild(rootNode);
            chartContainer.appendChild(rootList);
        }

        // Render the chart when the script loads
        initializeChart();
    </script>

</body></html>