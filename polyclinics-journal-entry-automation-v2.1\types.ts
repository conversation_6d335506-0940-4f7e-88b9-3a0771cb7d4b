
export interface CloverBankInfo {
  accountName: string;
  accountNo: string;
  activities: string;
  propertyId: string;
  country: string;
  departments: string;
  projectId: string;
}

export interface VendorOffsetAccounts {
  [key: string]: string;
}

export interface ExtractedTransaction {
  date: string;
  description: string;
  amount: number;
  type: 'credit' | 'debit';
}

export interface ExtractedData {
    accountName: string;
    transactions: ExtractedTransaction[];
}

export interface JournalEntry {
  journalNumber: number;
  journalName: string;
  lineNum: number;
  postingDate: string;
  accountType: number;
  accountNo: string;
  description: string;
  debitAmount: number | string;
  creditAmount: number | string;
  currencyCode: string;
  exchangeRate: number;
  offsetAccountType: number;
  offsetAccount: string;
  invoiceNo: string;
  documentNo: string;
  documentDate: string;
  dueDate: string;
  assetTransType: string;
  postingProfile: string;
  paymentMode: string;
  paymentReference: string;
  numberOfVoucher: number;
  activities: string;
  country: string;
  departments: string;
  projectId: string;
  propertyId: string;
}
