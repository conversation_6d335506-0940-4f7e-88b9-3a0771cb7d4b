from flask import Flask, jsonify
from flask_cors import CORS
import sqlite3

app = Flask(__name__)
CORS(app) # This allows our React frontend to make requests to this backend

def get_db_connection():
    conn = sqlite3.connect('utilities.db')
    conn.row_factory = sqlite3.Row # This allows us to access columns by name
    return conn

@app.route('/api/data')
def get_data():
    conn = get_db_connection()
    customers = conn.execute('SELECT * FROM customers').fetchall()
    conn.close()

    results = []
    for customer in customers:
        customer_dict = dict(customer)
        customer_id = customer_dict['id']

        # Calculate total invoiced for this customer
        conn = get_db_connection()
        total_invoiced = conn.execute(
            'SELECT SUM(amount) as total FROM invoices WHERE customer_id = ?', (customer_id,)
        ).fetchone()['total'] or 0

        # Calculate total collected for this customer
        total_collected = conn.execute(
            'SELECT SUM(amount) as total FROM collections WHERE customer_id = ?', (customer_id,)
        ).fetchone()['total'] or 0
        conn.close()

        # Calculate due balance
        opening_balance = customer_dict['opening_balance'] or 0
        due_balance = (opening_balance + total_invoiced) - total_collected

        customer_dict['total_invoiced'] = total_invoiced
        customer_dict['total_collected'] = total_collected
        customer_dict['due_balance'] = round(due_balance, 2)
        results.append(customer_dict)

    return jsonify(results)

if __name__ == '__main__':
    app.run(debug=True)