import React, { useState, useCallback } from 'react';
import FileUploader from './components/FileUploader';
import Loader from './components/Loader';
import PdfPreview from './components/PdfPreview';
import { extractDataFromText } from './services/geminiService';
import { AppStatus } from './types';
import { FileTextIcon, AlertTriangleIcon, CheckCircleIcon, FileJsonIcon } from './components/icons';
import { PDFDocument, PDFTextField, PDFFont } from 'pdf-lib';
import fontkit from '@pdf-lib/fontkit';

const App: React.FC = () => {
    const [pdfFile, setPdfFile] = useState<File | null>(null);
    const [dataFile, setDataFile] = useState<File | null>(null);
    const [extractedData, setExtractedData] = useState<Record<string, string> | null>(null);
    const [status, setStatus] = useState<AppStatus>(AppStatus.Idle);
    const [error, setError] = useState<string | null>(null);
    const [progressMessage, setProgressMessage] = useState<string>('');
    const [isDownloading, setIsDownloading] = useState<boolean>(false);
    const [downloadError, setDownloadError] = useState<string | null>(null);

    const handleProcess = useCallback(async () => {
        if (!pdfFile || !dataFile) {
            setError('Please upload both a PDF form and a data file.');
            setStatus(AppStatus.Error);
            return;
        }

        setStatus(AppStatus.Processing);
        setError(null);
        setExtractedData(null);

        try {
            setProgressMessage('Reading data file...');
            const fileContent = await dataFile.text();

            setProgressMessage('Extracting data with AI...');
            const data = await extractDataFromText(fileContent);
            setExtractedData(data);

            setProgressMessage('PDF ready for review...');
            setStatus(AppStatus.Success);
        } catch (e) {
            console.error(e);
            const errorMessage = e instanceof Error ? e.message : 'An unknown error occurred.';
            setError(`Operation failed: ${errorMessage}`);
            setStatus(AppStatus.Error);
        }
    }, [pdfFile, dataFile]);

    const handleReset = () => {
        setPdfFile(null);
        setDataFile(null);
        setExtractedData(null);
        setError(null);
        setStatus(AppStatus.Idle);
        setProgressMessage('');
        setDownloadError(null);
    };

    const isArabic = (text: string) => /[\u0600-\u06FF]/.test(text);

    const handleDownloadPdf = async () => {
        if (!pdfFile || !extractedData) return;
    
        setIsDownloading(true);
        setDownloadError(null);
    
        try {
            const existingPdfBytes = await pdfFile.arrayBuffer();
            const pdfDoc = await PDFDocument.load(existingPdfBytes, { ignoreEncryption: true });
            
            // Register fontkit to handle custom fonts
            pdfDoc.registerFontkit(fontkit);
    
            let cairoFont: PDFFont | undefined = undefined;
    
            try {
                // Use a direct link to the raw TTF file to avoid format issues.
                const fontUrl = 'https://github.com/google/fonts/raw/main/ofl/cairo/Cairo-Regular.ttf';
                const fontBytes = await fetch(fontUrl).then(res => res.arrayBuffer());
                cairoFont = await pdfDoc.embedFont(fontBytes);
            } catch (e) {
                console.error("Could not load custom font for Arabic text.", e);
            }
    
            const form = pdfDoc.getForm();
            const unfilledKeys: string[] = [];
            const hasArabic = Object.values(extractedData).some(isArabic);
    
            for (const key in extractedData) {
                const value = extractedData[key] || '';
                try {
                    const field = form.getField(key);
                    if (field instanceof PDFTextField) {
                        field.setText(value);
                        if (isArabic(value) && cairoFont) {
                            field.updateAppearances(cairoFont);
                        }
                    }
                } catch (e) {
                    console.warn(`Could not find or fill field: "${key}"`);
                    unfilledKeys.push(key);
                }
            }
    
            try {
                form.flatten();
            } catch(e) {
                console.error("Failed to flatten form. Content might not be visible.", e);
                setDownloadError("Could not finalize the PDF. Some data may not appear.");
            }
    
            let warnings: string[] = [];
            if (unfilledKeys.length > 0) {
                 warnings.push(`Could not fill some fields: ${unfilledKeys.join(', ')}. Ensure data keys exactly match PDF field names.`);
            }
            
            if (hasArabic && !cairoFont) {
                warnings.push('Warning: Failed to load Arabic font. Arabic text may not render correctly.');
            }
    
            if (warnings.length > 0) {
                setDownloadError(warnings.join(' '));
            }
    
            const pdfBytes = await pdfDoc.save();
    
            const blob = new Blob([pdfBytes], { type: 'application/pdf' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            const cleanName = pdfFile.name.replace(/\.pdf$/i, '');
            link.download = `${cleanName}-filled.pdf`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(link.href);
    
        } catch (e) {
            console.error("Failed to fill PDF", e);
            const errorMessage = e instanceof Error ? e.message : 'An unknown error occurred.';
            setDownloadError(`Error creating PDF: ${errorMessage}`);
        } finally {
            setIsDownloading(false);
        }
    };


    const renderResults = () => {
        switch (status) {
            case AppStatus.Processing:
                return <Loader message={progressMessage} />;
            case AppStatus.Error:
                return (
                    <div className="flex flex-col items-center justify-center p-8 text-center bg-red-50 dark:bg-red-900/20 rounded-2xl shadow-md border border-red-200 dark:border-red-700">
                        <AlertTriangleIcon className="w-12 h-12 text-red-500 dark:text-red-400 mb-4" />
                        <h3 className="text-xl font-bold text-red-800 dark:text-red-200">An Error Occurred</h3>
                        <p className="mt-2 text-sm text-red-600 dark:text-red-300 max-w-md">{error}</p>
                        <button
                            onClick={handleReset}
                            className="mt-6 px-4 py-2 bg-red-500 text-white font-semibold rounded-lg shadow-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-opacity-75 transition-colors"
                        >
                            Try Again
                        </button>
                    </div>
                );
            case AppStatus.Success:
                if (!extractedData) return null;
                return (
                    <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-md border border-gray-200 dark:border-gray-700">
                        <div className="flex items-center gap-3 mb-4">
                            <CheckCircleIcon className="w-8 h-8 text-green-500" />
                            <div>
                               <h3 className="text-xl font-bold text-gray-800 dark:text-white">Extraction Successful</h3>
                               <p className="text-sm text-gray-500 dark:text-gray-400">Review the data extracted by the AI.</p>
                            </div>
                        </div>
                        
                        <div className="max-h-60 overflow-y-auto p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600 mb-6 font-mono text-sm">
                            <h4 className="font-sans font-bold text-md text-gray-700 dark:text-gray-200 mb-3 flex items-center gap-2"><FileJsonIcon className="w-5 h-5"/>Extracted Data:</h4>
                             {Object.entries(extractedData).map(([key, value]) => (
                                <div key={key} className="grid grid-cols-3 gap-2 py-2 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                                    <span className={`col-span-1 font-semibold text-gray-600 dark:text-gray-300 break-words ${isArabic(key) ? 'font-cairo text-right' : ''}`}>{key}</span>
                                    <span className={`col-span-2 text-indigo-700 dark:text-indigo-300 break-words ${isArabic(value) ? 'font-cairo text-right' : ''}`}>{value}</span>
                                </div>
                            ))}
                        </div>

                        <div>
                            <div className="flex items-center gap-4">
                                <button
                                    onClick={handleDownloadPdf}
                                    disabled={isDownloading}
                                    className="w-full px-6 py-3 bg-indigo-600 text-white font-bold rounded-lg shadow-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-75 transition-all disabled:bg-gray-400 dark:disabled:bg-gray-500 disabled:cursor-not-allowed"
                                >
                                    {isDownloading ? 'Generating...' : 'Download Filled PDF'}
                                </button>
                                 <button
                                    onClick={handleReset}
                                    className="px-6 py-3 bg-gray-200 text-gray-700 font-bold rounded-lg hover:bg-gray-300 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 focus:outline-none transition-colors"
                                >
                                    Reset
                                </button>
                            </div>
                            {downloadError && (
                                <p className="mt-3 text-sm text-center text-red-700 dark:text-red-300 bg-red-100 dark:bg-red-900/30 p-3 rounded-lg">{downloadError}</p>
                            )}
                        </div>
                    </div>
                );
            default:
                return (
                    <div className="flex flex-col items-center justify-center p-8 text-center bg-white dark:bg-gray-800 rounded-2xl shadow-md border border-gray-200 dark:border-gray-700">
                        <h3 className="text-xl font-bold text-gray-800 dark:text-white">View Your Results Here</h3>
                        <p className="mt-2 text-sm text-gray-500 dark:text-gray-400 max-w-sm">
                            After uploading your files and processing, the extracted data and a preview of your filled PDF will be displayed here.
                        </p>
                    </div>
                );
        }
    };
    
    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 p-4 sm:p-6 lg:p-8">
            <div className="max-w-7xl mx-auto">
                <header className="text-center mb-10">
                    <h1 className="text-4xl sm:text-5xl font-extrabold text-gray-900 dark:text-white tracking-tight">AI PDF Form Filler</h1>
                    <p className="mt-4 max-w-2xl mx-auto text-lg text-gray-500 dark:text-gray-400">
                        Automatically populate PDF forms from text files. Supports both <span className="font-cairo font-bold">English</span> and <span className="font-cairo font-bold">العربية</span>.
                    </p>
                </header>

                <main className="grid grid-cols-1 lg:grid-cols-12 gap-8">
                    <div className="lg:col-span-8 grid grid-cols-1 md:grid-cols-2 gap-8">
                        <FileUploader
                            title="1. Upload PDF Form"
                            acceptedFormats="PDF only"
                            icon={<FileTextIcon className="w-10 h-10" />}
                            onFileSelect={setPdfFile}
                            file={pdfFile}
                        />
                         <FileUploader
                            title="2. Upload Data File"
                            acceptedFormats=".txt, .csv, or plain text"
                            icon={<FileTextIcon className="w-10 h-10" />}
                            onFileSelect={setDataFile}
                            file={dataFile}
                        />
                    </div>

                    <div className="lg:col-span-4 flex flex-col justify-center">
                         <button
                            onClick={handleProcess}
                            disabled={!pdfFile || !dataFile || status === AppStatus.Processing}
                            className="w-full px-6 py-4 text-xl bg-indigo-600 text-white font-bold rounded-lg shadow-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-75 transition-all disabled:bg-gray-400 dark:disabled:bg-gray-600 disabled:cursor-not-allowed disabled:shadow-none"
                        >
                            {status === AppStatus.Processing ? 'Processing...' : 'Fill Form with AI'}
                        </button>
                    </div>

                    <div className="lg:col-span-6 min-h-[400px] lg:min-h-0">
                       {renderResults()}
                    </div>
                    <div className="lg:col-span-6 min-h-[400px] lg:min-h-0">
                         <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-md border border-gray-200 dark:border-gray-700 h-full">
                           <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-4">PDF Preview</h3>
                           <div className="w-full aspect-[4/5]">
                             <PdfPreview file={pdfFile} />
                           </div>
                         </div>
                    </div>
                </main>
                 <footer className="text-center mt-12 py-6 border-t border-gray-200 dark:border-gray-700">
                    <p className="text-sm text-gray-500 dark:text-gray-400">Powered by React, Tailwind CSS, and Google Gemini</p>
                </footer>
            </div>
        </div>
    );
};

export default App;