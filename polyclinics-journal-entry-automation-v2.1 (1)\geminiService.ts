
import { GoogleGenAI, Type } from "@google/genai";
import { ExtractedData } from '../types';

const ai = new GoogleGenAI({ apiKey: process.env.API_KEY as string });

const responseSchema = {
  type: Type.OBJECT,
  properties: {
    accountName: {
      type: Type.STRING,
      description: "The primary account holder's name or company name found in the statement.",
    },
    accountNumber: {
      type: Type.STRING,
      description: "The bank account number found in the statement. This can be a long numeric string or a shorter alphanumeric identifier (e.g., 'KIBXX-1234'). Extract whichever is present.",
    },
    transactions: {
      type: Type.ARRAY,
      description: "A list of all credit transactions (deposits) and debit transactions (withdrawals, payments).",
      items: {
        type: Type.OBJECT,
        properties: {
          date: {
            type: Type.STRING,
            description: "Transaction date in YYYY-MM-DD format.",
          },
          description: {
            type: Type.STRING,
            description: "The full, original transaction description.",
          },
          amount: {
            type: Type.NUMBER,
            description: "The numeric transaction amount.",
          },
          type: {
            type: Type.STRING,
            description: "The type of transaction, must be either 'credit' or 'debit'.",
            enum: ['credit', 'debit'],
          },
        },
        required: ["date", "description", "amount", "type"],
      },
    },
  },
  required: ["accountName", "accountNumber", "transactions"],
};

export async function extractTransactionsFromText(text: string): Promise<ExtractedData> {
  if (!process.env.API_KEY) {
    throw new Error("API_KEY environment variable not set.");
  }
  
  const prompt = `
    You are an expert financial data extraction API.
    Analyze the following bank statement text. Your task is to:
    1. Identify the primary account holder's name (e.g., "YARROW POLYCLINIC").
    2. Identify the bank Account Number for the statement (e.g., "KIBXX-1234" or "************").
    3. Extract ALL transactions, both CREDIT (deposits, incoming funds) and DEBIT (withdrawals, payments made, fees).
    4. For each transaction, you must identify its type as either 'credit' or 'debit'.
    5. Format the extracted data into a JSON object that strictly follows the provided schema.
    6. Ensure dates are standardized to YYYY-MM-DD format if possible. If year is ambiguous, assume the current year.

    Bank Statement Text:
    ---
    ${text}
    ---
  `;

  try {
    const response = await ai.models.generateContent({
      model: "gemini-2.5-flash",
      contents: prompt,
      config: {
        responseMimeType: "application/json",
        responseSchema: responseSchema,
      },
    });

    const rawText = response.text?.trim();

    if (!rawText) {
        throw new Error("The AI model returned an empty response. It might have failed to extract data from the document.");
    }

    let parsedData;
    try {
        // The model may wrap the JSON in markdown backticks. This removes them.
        const cleanedJsonText = rawText.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        parsedData = JSON.parse(cleanedJsonText);
    } catch (e) {
        console.error("Failed to parse AI response as JSON.", "Raw response:", rawText, "Error:", e);
        // Provide a more user-friendly error if the response seems to be a common failure case.
        if (rawText.toLowerCase() === 'undefined') {
             throw new Error("The AI failed to extract data. The document might not contain recognizable transactions or is not a valid bank statement.");
        }
        throw new Error("The AI returned data in an unexpected format that could not be read.");
    }
    
    if (!parsedData || typeof parsedData !== 'object' || !parsedData.accountName || !parsedData.accountNumber || !Array.isArray(parsedData.transactions)) {
        console.error("Parsed AI data is missing required fields.", "Parsed data:", parsedData);
        throw new Error("AI response was parsed but is missing required fields (e.g., accountName, accountNumber, transactions).");
    }
    
    return parsedData as ExtractedData;

  } catch (error) {
    console.error("Error calling Gemini API:", error);
    // Re-throw specific errors, otherwise wrap in a generic message.
    if (error instanceof Error && (error.message.startsWith("The AI") || error.message.startsWith("AI response"))) {
        throw error;
    }
    throw new Error("An unexpected error occurred while communicating with the AI. Please try again.");
  }
}