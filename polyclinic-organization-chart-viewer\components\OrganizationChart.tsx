
import React from 'react';
import { OrganizationData } from '../types';
import PolyclinicNode from './PolyclinicNode';

interface OrganizationChartProps {
  data: OrganizationData;
}

const OrganizationChart: React.FC<OrganizationChartProps> = ({ data }) => {
  if (!data || !data.polyclinics || data.polyclinics.length === 0) {
    return <p className="text-center text-slate-500">No organization data available.</p>;
  }

  return (
    <div className="space-y-6">
      {data.polyclinics.map((polyclinic) => (
        <PolyclinicNode key={polyclinic.id} polyclinic={polyclinic} />
      ))}
    </div>
  );
};

export default OrganizationChart;
