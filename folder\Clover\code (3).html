<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Polyclinic Directory</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .controls {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            align-items: end;
        }

        .control-group {
            display: flex;
            flex-direction: column;
        }

        .control-group label {
            margin-bottom: 8px;
            font-weight: bold;
            color: #495057;
            font-size: 0.9rem;
        }

        .control-group input,
        .control-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background-color: white;
        }
        
        .control-group input:focus,
        .control-group select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.25);
        }

        .stats {
            padding: 15px 30px;
            background: #e9ecef;
            border-bottom: 1px solid #dee2e6;
            text-align: right;
            font-weight: bold;
            color: #155724;
        }

        .content {
            padding: 30px;
        }
        
        .table-container {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.95rem;
        }

        th, td {
            padding: 12px 15px;
            border: 1px solid #dee2e6;
            text-align: left;
            vertical-align: middle;
        }

        thead {
            background-color: #007bff;
            color: white;
        }
        
        th {
            font-weight: 600;
        }

        tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        tbody tr:hover {
            background-color: #e9ecef;
        }
        
        .highlight {
            background-color: #fff3cd;
            padding: 1px 3px;
            border-radius: 3px;
            font-weight: bold;
        }

        #noResultsMessage {
            text-align: center;
            padding: 40px;
            color: #6c757d;
            font-size: 1.1rem;
            display: none; /* Hidden by default */
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 Polyclinic Directory</h1>
        </div>

        <div class="controls">
            <div class="control-group">
                <label for="searchInput">Search All Fields</label>
                <input type="text" id="searchInput" placeholder="e.g., Al Aseel, Dr. Ali, Floor 10...">
            </div>
            <div class="control-group">
                <label for="clinicFilter">Filter by Clinic Name</label>
                <select id="clinicFilter"></select>
            </div>
            <div class="control-group">
                <label for="floorFilter">Filter by Floor</label>
                <select id="floorFilter"></select>
            </div>
            <div class="control-group">
                <label for="typeFilter">Filter by Type</label>
                <select id="typeFilter"></select>
            </div>
        </div>
        
        <div class="stats">
            <span id="resultCount"></span>
        </div>

        <div class="content">
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Clinic Name</th>
                            <th>Floor</th>
                            <th>Clinic Code</th>
                            <th>Doctor/Company</th>
                            <th>Type</th>
                        </tr>
                    </thead>
                    <tbody id="clinicTableBody">
                        <!-- Rows will be inserted here by JavaScript -->
                    </tbody>
                </table>
            </div>
            <div id="noResultsMessage">
                <h3>No results found</h3>
                <p>Try adjusting your search or filter criteria.</p>
            </div>
        </div>
    </div>

    <div style="text-align: center; margin-top: 20px;">
        <p><a href="./my_projects/project_alpha/index.html">Open Project Alpha Folder</a></p>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // 1. DATA: The raw, nested data structure
            const clinicData = [
                // ... (full data from previous examples) ...
                { "name": "Iris - Polyclinic Mazaya 3", "floors": [ { "floor": 2, "codes": [{"code": "A", "doctor": "Abdullah Abdul Aziz Al Hajri"}, {"code": "B", "doctor": "Smart Health Co."}, {"code": "C", "doctor": "Walid Hamad Ashwi Raheel"}] }, { "floor": 3, "codes": [{"code": "A+B", "doctor": "Amr Nabil Qutb"}, {"code": "C", "doctor": "Oxcana Bogdanovic"}] }, { "floor": 4, "codes": [{"code": "A", "doctor": "Hassaan A Jaber & Obaid Metni"}, {"code": "B", "doctor": "Mohamed Youssef Al Eissa"}, {"code": "C", "doctor": "Dr. Mariam Abed Ali Al-Turki"}] } ] },
                { "name": "Al Aseel International", "floors": [ { "floor": 7, "codes": [{"code": "C", "doctor": "Daniel Alain"}] }, { "floor": 8, "codes": [{"code": "A", "doctor": "Abdullah Abdul Rahman Al Hassan"}, {"code": "B", "doctor": "Hossam Mohamed El Badri"}, {"code": "C", "doctor": "Mustafa Samy Al Kaddousy"}] }, { "floor": 9, "codes": [{"code": "A", "doctor": "Nasser Faisal Al Mutairy"}, {"code": "B", "doctor": "Andro George Mikha'eel"}, {"code": "C", "doctor": "Dr. Noor Aladdin Alomar & Dr. Aya Samara"}] }, { "floor": 10, "codes": [{"code": "A+B", "doctor": "Dr. Ali Al-Mukaimi & Nisreen Baeij"}, {"code": "C", "doctor": "Dr. Ali Al-Mukaimi"}] } ] },
                { "name": "Yarow - Polyclinic", "floors": [ { "floor": 11, "codes": [{"code": "A", "doctor": "Dr. Ahmed Abdulsamad Yehya Jassem"}, {"code": "B", "doctor": "Dr. Osamah J M Albaker"}, {"code": "C", "doctor": "Hossam Mohamed El Badri"}] }, { "floor": 12, "codes": [{"code": "A", "doctor": "Ahmed Mohamed Ahmed Ibrahim"}, {"code": "B", "doctor": "Sale Abdul Ghaffar Ma'arafie"}, {"code": "C", "doctor": "Adnan Ibrahim Ibrahim"}] } ] },
                { "name": "Fourth Medical Center", "floors": [ {"floor": 1, "codes": [{"code": "A", "doctor": "Salam Attar"}]}, {"floor": "2-3", "codes": [{"code": "A", "doctor": "One Day to Manage Projects Co."}]}, {"floor": "4-5", "codes": [{"code": "A", "doctor": "Athba Co."}]}, {"floor": 6, "codes": [{"code": "A", "doctor": "Health Care Co."}]}, {"floor": 7, "codes": [{"code": "A", "doctor": "Abdul Aziz Fahad Al Mezeiny"}]}, {"floor": 13, "codes": [{"code": "A", "doctor": "Revolution Medical Co."}]}, {"floor": 14, "codes": [{"code": "A", "doctor": "Dr. Farouk Alzoubani"}]}, {"floor": 15, "codes": [{"code": "A", "doctor": "Assem Drwesh Mostafa Abdulnabi"}]}, {"floor": 16, "codes": [{"code": "A", "doctor": "One Day to Manage Projects Co."}]}, {"floor": 17, "codes": [{"code": "A", "doctor": "Dr. Abdullah Sadad Sabri Al-Ozairi"}]}, {"floor": "18-19", "codes": [{"code": "A", "doctor": "Gulf Care Co."}]} ] },
                { "name": "Medical Harbour", "floors": [ {"floor": 1, "codes": [{"code": "C", "doctor": "Moaeyed Zaid Al Saq'abi"}]}, {"floor": 2, "codes": [{"code": "C", "doctor": "Mohamed Abdul Majid Hassan"}]}, {"floor": 3, "codes": [{"code": "C", "doctor": "Salah El Din Mohamed El Sherbini"}]}, {"floor": 4, "codes": [{"code": "C", "doctor": "Youssef Al Khleify/Rawan Al Khatib"}]}, {"floor": 8, "codes": [{"code": "C", "doctor": "Amir Eissa Attia Killa"}]}, {"floor": 9, "codes": [{"code": "C", "doctor": "Dr. Hesham Mohamed Yassin Ibrahim"}]}, {"floor": 10, "codes": [{"code": "C", "doctor": "Med Vision Medical Services"}]}, {"floor": 11, "codes": [{"code": "C", "doctor": "Fatmah Mohamed Badawy"}]}, {"floor": 12, "codes": [{"code": "C", "doctor": "Othman Youssef Al Mas'oud"}]}, {"floor": 13, "codes": [{"code": "C", "doctor": "Btissam Ibn Kiran"}]}, {"floor": 14, "codes": [{"code": "C", "doctor": "Misha'al Al Dahsh"}]}, {"floor": 15, "codes": [{"code": "C", "doctor": "Amal Al Shaiji / Faisal Al Terkeet"}]}, {"floor": 16, "codes": [{"code": "C", "doctor": "Signofa Co./Ahmed Eissa"}]}, {"floor": 17, "codes": [{"code": "C", "doctor": "Waleed Hamid Raheel"}]}, {"floor": 18, "codes": [{"code": "C", "doctor": "Eman Ghorab"}]}, {"floor": 19, "codes": [{"code": "C", "doctor": "Emad Morkos/Ahmed Youssef"}]}, {"floor": 20, "codes": [{"code": "C", "doctor": "Mohamed Al Kolk"}]}, {"floor": 21, "codes": [{"code": "C", "doctor": "Youssef Al Khleify"}]} ] },
                { "name": "Med Marine", "floors": [ {"floor": 5, "codes": [{"code": "A", "doctor": "Fatima Ne'ma Al Awadhi"}, {"code": "B", "doctor": "Mohamed As'ad Eid/Wael Bezrah"}]}, {"floor": 6, "codes": [{"code": "A+B", "doctor": "Mohamed Youssef Al Sabty"}]}, {"floor": 7, "codes": [{"code": "A+B", "doctor": "Mostafa Mohamed Tomsu"}]} ] },
                { "name": "JOYA - Polyclinic", "floors": [ {"floor": 8, "codes": [{"code": "A", "doctor": "Ihab Mohamed Younes Omar"}, {"code": "B", "doctor": "Huda Mahmoud Selim"}]}, {"floor": 9, "codes": [{"code": "A+B", "doctor": "Berlin Co./Mohamed Riyadh"}]}, {"floor": 10, "codes": [{"code": "A+B", "doctor": "Shehta Mostafa Ze'reb"}]} ] },
                { "name": "Med Grey", "floors": [ {"floor": 5, "codes": [{"code": "A", "doctor": "Dr. Amr Nabil Qutb"}]}, {"floor": "6-7", "codes": [{"code": "A", "doctor": "Dr. Shehta Mostafa Zurub"}]} ] },
                { "name": "Aram - Polyclinic", "floors": [ {"floor": 2, "codes": [{"code": "A+B", "doctor": "Dalia/Mina/Osama/Mahmoud"}]}, {"floor": 3, "codes": [{"code": "A+B", "doctor": "Ayman/Islam"}]}, {"floor": 4, "codes": [{"code": "A", "doctor": "Mohamed Al Sayyad"}, {"code": "B", "doctor": "Mohamed Al Sayyad"}]}, {"floor": 5, "codes": [{"code": "A", "doctor": "Bishoy/Mina/Zaher"}, {"code": "B", "doctor": "Nasser/Mohamed"}]}, {"floor": 6, "codes": [{"code": "A", "doctor": "Munira/Anjoud"}, {"code": "B", "doctor": "Munira/Anjoud"}]}, {"floor": 7, "codes": [{"code": "A+B", "doctor": "Sondos Ghaneim"}]}, {"floor": 8, "codes": [{"code": "A", "doctor": "Marina/Mary/Mariana"}, {"code": "B", "doctor": "Dr. Mohammed Salem"}]}, {"floor": 9, "codes": [{"code": "A", "doctor": "Rwda Ahmed"}, {"code": "B", "doctor": "Marawan Essam"}]} ] }
            ];

            // 2. DOM ELEMENT REFERENCES
            const searchInput = document.getElementById('searchInput');
            const clinicFilter = document.getElementById('clinicFilter');
            const floorFilter = document.getElementById('floorFilter');
            const typeFilter = document.getElementById('typeFilter');
            const tableBody = document.getElementById('clinicTableBody');
            const resultCount = document.getElementById('resultCount');
            const noResultsMessage = document.getElementById('noResultsMessage');

            // 3. DATA PROCESSING: Flatten the data and add the 'Type' column
            const flatData = clinicData.flatMap(clinic => 
                clinic.floors.flatMap(floor => 
                    floor.codes.map(code => ({
                        clinicName: clinic.name,
                        floor: String(floor.floor),
                        clinicCode: code.code,
                        doctorCompany: code.doctor,
                        type: (code.doctor.toLowerCase().includes('co.') || code.doctor.toLowerCase().includes('services')) ? 'Company' : 'Doctor'
                    }))
                )
            );

            // 4. DYNAMIC FILTER POPULATION
            function populateFilters() {
                const clinics = [...new Set(flatData.map(item => item.clinicName))].sort();
                const floors = [...new Set(flatData.map(item => item.floor))].sort((a, b) => {
                    const numA = parseInt(a.split('-')[0]);
                    const numB = parseInt(b.split('-')[0]);
                    return numA - numB;
                });
                const types = [...new Set(flatData.map(item => item.type))].sort();

                const createOptions = (select, options, defaultLabel) => {
                    select.innerHTML = `<option value="">${defaultLabel}</option>`;
                    options.forEach(opt => select.innerHTML += `<option value="${opt}">${opt}</option>`);
                };

                createOptions(clinicFilter, clinics, 'All Clinics');
                createOptions(floorFilter, floors, 'All Floors');
                createOptions(typeFilter, types, 'All Types');
            }

            // 5. RENDER TABLE FUNCTION
            function renderTable() {
                const searchTerm = searchInput.value.toLowerCase();
                const selectedClinic = clinicFilter.value;
                const selectedFloor = floorFilter.value;
                const selectedType = typeFilter.value;

                const filteredData = flatData.filter(item => {
                    const searchMatch = (
                        item.clinicName.toLowerCase().includes(searchTerm) ||
                        item.floor.toLowerCase().includes(searchTerm) ||
                        item.clinicCode.toLowerCase().includes(searchTerm) ||
                        item.doctorCompany.toLowerCase().includes(searchTerm)
                    );
                    const clinicMatch = !selectedClinic || item.clinicName === selectedClinic;
                    const floorMatch = !selectedFloor || item.floor === selectedFloor;
                    const typeMatch = !selectedType || item.type === selectedType;

                    return searchMatch && clinicMatch && floorMatch && typeMatch;
                });

                // Clear existing table body
                tableBody.innerHTML = '';
                
                // Update result count
                const count = filteredData.length;
                resultCount.textContent = `Showing ${count} of ${flatData.length} entries`;

                // Show/hide no results message
                if (count === 0) {
                    noResultsMessage.style.display = 'block';
                } else {
                    noResultsMessage.style.display = 'none';
                }

                // Populate table with filtered data
                filteredData.forEach(item => {
                    const row = document.createElement('tr');
                    
                    const highlight = (text) => {
                        if (!searchTerm) return text;
                        const regex = new RegExp(`(${searchTerm})`, 'gi');
                        return text.replace(regex, `<span class="highlight">$1</span>`);
                    };

                    row.innerHTML = `
                        <td>${highlight(item.clinicName)}</td>
                        <td>${highlight(item.floor)}</td>
                        <td>${highlight(item.clinicCode)}</td>
                        <td>${highlight(item.doctorCompany)}</td>
                        <td>${highlight(item.type)}</td>
                    `;
                    tableBody.appendChild(row);
                });
            }

            // 6. EVENT LISTENERS
            searchInput.addEventListener('input', renderTable);
            clinicFilter.addEventListener('change', renderTable);
            floorFilter.addEventListener('change', renderTable);
            typeFilter.addEventListener('change', renderTable);

            // 7. INITIALIZATION
            populateFilters();
            renderTable();
        });
    </script>
</body>
</html>
