body { font-family: 'Segoe UI', sans-serif; margin: 20px; background: #f0f2f5; }
.chart-container { display: flex; flex-direction: column; align-items: center; }
.top-level-nodes { display: flex; flex-direction: column; align-items: center; margin-bottom: 20px; }
.clinic-columns-container { display: flex; justify-content: center; flex-wrap: wrap; gap: 20px; }
.clinic-column { display: flex; flex-direction: column; align-items: center; }
.node {
    padding: 15px;
    margin: 10px;
    border-radius: 8px;
    text-align: center;
    min-width: 200px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: all 0.2s ease-in-out;
    -webkit-transition: all 0.2s ease-in-out;
    cursor: pointer;
}

.search-container {
    margin: 20px auto;
    text-align: center;
    max-width: 600px;
}

#searchInput {
    padding: 12px;
    width: 100%;
    border: 2px solid #1976d2;
    border-radius: 25px;
    font-size: 16px;
    transition: all 0.3s ease;
}

#searchInput:focus {
    outline: none;
    box-shadow: 0 0 8px rgba(25,118,210,0.3);
}

#resultCount {
    margin-top: 10px;
    font-weight: bold;
    color: #388e3c;
}

.highlight {
    background-color: #fff3e0;
    padding: 2px 4px;
    border-radius: 3px;
}

.error-message {
    color: #c62828;
    margin-top: 10px;
    font-weight: bold;
}

@media (max-width: 768px) {
    .node {
        min-width: 160px;
        padding: 10px;
        margin: 8px;
        font-size: 14px;
    }
    
    h2 {
        font-size: 1.5rem;
        margin: 15px 0;
    }
    
    .clinic-columns-container {
        flex-direction: column;
        gap: 15px;
    }
    
    .search-container {
        margin: 15px;
        padding: 0 10px;
    }
    
    #searchInput {
        font-size: 14px;
        padding: 10px;
    }
}

.medical-labs { background: #ffebee; border: 2px solid #c62828; }
.clover { background: #f3e5f5; border: 2px solid #7b1fa2; }
.clinic { background: #e3f2fd; border: 2px solid #1976d2; }
.floor { background: #f3e5f5; border: 2px solid #7b1fa2; }
.clinic-code { background: #e8f5e9; border: 2px solid #388e3c; }
.doctor { background: #fffde7; border: 2px solid #f9a825; }
.connector { width: 2px; height: 20px; background: #666; margin: 0 auto; }
h2 { color: #1a237e; text-align: center; }

.floors-container {
    display: flex;
    flex-direction: column; /* Default vertical */
    align-items: center;
}

.floors-container.horizontal {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    align-items: flex-start;
    margin-top: 10px; /* Space between clinic node and horizontal floors */
}
.floors-container.horizontal .floor-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 10px; /* Space between horizontal floor groups */
}
.floors-container.horizontal .floor-group .connector {
    height: 20px; /* Vertical connector within floor group */
    width: 2px;
}
.floors-container.horizontal .floor-group .floor + .connector {
    margin-top: 5px; /* Space between floor and first doctor */
}
.floors-container.horizontal .floor-group .clinic-code + .connector {
    margin-top: 5px; /* Space between doctor and next doctor */
}
.floors-container.horizontal .floor-group .connector + .floor {
    margin-top: 0; /* No extra margin for floor after connector */
}
