import pandas as pd
import argparse
import html
import sys
from datetime import time

def parse_attendance_history(html_file: str) -> pd.DataFrame | None:
    """
    Parses HTML attendance history tables using pandas and returns a cleaned DataFrame.

    Args:
        html_file (str): Path to the HTML file.

    Returns:
        pandas.DataFrame or None: DataFrame with attendance data, or None if parsing fails.
    """
    try:
        with open(html_file, 'r', encoding='windows-1256') as f:
            # The regex in the original script suggests the HTML is escaped (e.g., &lt; instead of <)
            # We need to unescape it first for pandas to parse it correctly.
            html_content = html.unescape(f.read())

        # Use pandas.read_html which is designed for this task.
        # It's more robust than regex.
        # 'header=1' uses the second row (index 1) as the header.
        # 'attrs' targets tables with the specific id 'result'.
        tables = pd.read_html(html_content, attrs={'id': 'result'}, header=1)

        if not tables:
            print("Error: Could not find any tables with id='result' in the HTML file.", file=sys.stderr)
            return None

        # Concatenate all found tables (for pagination) into a single DataFrame.
        df = pd.concat(tables, ignore_index=True)

        # --- Data Cleaning ---
        # Drop rows that are entirely empty
        df.dropna(how='all', inplace=True)

        # Filter out summary/total rows and rows without a valid work time.
        # Using .loc with boolean indexing is an efficient way to filter.
        is_not_total_row = df['Weekday'].str.strip().str.lower() != 'totals'
        has_work_time = df['Work Time'].str.strip() != ''
        df = df.loc[is_not_total_row & has_work_time].copy()

        if df.empty:
            print("No valid attendance data found after filtering out empty/summary rows.")
            return None

        return df

    except FileNotFoundError:
        print(f"Error: File not found: {html_file}", file=sys.stderr)
        return None
    except ValueError as e:
        # This can be raised by read_html if no tables are found
        print(f"Error: No tables found or parsing failed. Details: {e}", file=sys.stderr)
        return None
    except Exception as e:
        print(f"An unexpected error occurred during parsing: {e}", file=sys.stderr)
        return None


def convert_entry_time(df: pd.DataFrame) -> pd.DataFrame:
    """
    Converts 'Entry Time' to time objects and checks if it's after 8:30 AM.

    Args:
        df (pandas.DataFrame): DataFrame with attendance history.

    Returns:
        pandas.DataFrame: DataFrame with new 'Entry Time Converted' and 'After 8:30' columns.
    """
    if 'Entry Time' not in df.columns:
        print("Warning: 'Entry Time' column not found. Cannot perform time analysis.", file=sys.stderr)
        return df

    # Convert 'Entry Time' to datetime.time objects.
    # 'coerce' will turn any parsing errors into NaT (Not a Time).
    df['Entry Time Converted'] = pd.to_datetime(df['Entry Time'], format='%I:%M %p', errors='coerce').dt.time

    # Define the target time for comparison.
    target_time = time(8, 30)

    # Create the 'After 8:30' column. This comparison works correctly with time objects.
    # Fill NaN values with False, as a missing time is not a late entry.
    df['After 8:30'] = (df['Entry Time Converted'] > target_time).fillna(False)

    return df


def analyze_attendance(df: pd.DataFrame) -> int:
    """
    Counts the number of entries where 'After 8:30' is True.

    Args:
        df (pandas.DataFrame): DataFrame with 'After 8:30' column.

    Returns:
        int: Number of entries after 8:30 AM.
    """
    if 'After 8:30' not in df.columns:
        print("Error: 'After 8:30' column not found. Run convert_entry_time() first.", file=sys.stderr)
        return 0

    # .sum() on a boolean column counts the number of True values.
    return df['After 8:30'].sum()


def main():
    parser = argparse.ArgumentParser(description="Analyze attendance history from an HTML file.")
    parser.add_argument("input_html", help="Path to the input HTML file (e.g., 'attendance.html')")
    parser.add_argument("output_excel", help="Path for the output Excel file (e.g., 'analysis.xlsx')")
    args = parser.parse_args()

    df = parse_attendance_history(args.input_html)

    if df is None or df.empty:
        print("Processing finished: No valid data to analyze.")
        return

    df_processed = convert_entry_time(df)
    late_count = analyze_attendance(df_processed)

    print("--- Processed Attendance Data ---")
    print(df_processed.to_string())
    print(f"\nTotal number of entries after 8:30 AM: {late_count}")

    try:
        # Define desired column order for the output file.
        output_cols_order = [
            'Weekday', 'Attendance Day', 'Work Time', 'Entry Time', 'Entry Time Converted',
            'After 8:30', 'End Time', 'Exit Time', 'Early Out Permission', 'Actual Hours'
        ]
        # Filter to only include columns that actually exist in the DataFrame.
        final_cols = [col for col in output_cols_order if col in df_processed.columns]
        df_processed[final_cols].to_excel(args.output_excel, index=False)
        print(f"\nAttendance analysis saved to {args.output_excel}")
    except Exception as e:
        print(f"Error saving to Excel: {e}", file=sys.stderr)

if __name__ == "__main__":
    main()