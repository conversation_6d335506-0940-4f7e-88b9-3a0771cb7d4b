;=====================================================================
; Configuration file for uninstallation of the printer driver.
; Copyright CANON INC. 1999
; UNINST.INI
;=====================================================================

;---------------------------------------------------------------------
; [PDL] Specify the target PDLs to be uninstalled.
;---------------------------------------------------------------------
[PDL]
PDL=PCL
PDL_TYPE=PCL

;---------------------------------------------------------------------
; [Profiles] Other parameters to be used in the deleting process.
;---------------------------------------------------------------------
[Profiles]
PDL_NAME=Canon Generic Plus PCL6
PDL_NAME2=Canon Generic Plus PCL6
UninstallLevel=2
AUTHORITYCHECK=ON
CheckJobMonitor=ON
DelRestoreFile=OFF
CheckiWEMC_DRM=ON
UsePrintUI=0
SupportCategory=GPPCL
CategoryRegName=Driver Category

;---------------------------------------------------------------------
; [ModuleInfo] Resource Module File Name
;---------------------------------------------------------------------
[ModuleInfo]
ResourceModule=UninsUI*.dll

;---------------------------------------------------------------------
; [Registrys] List the registry keys to be deleted in the uninstallation.
;---------------------------------------------------------------------
[Registrys]
KEY_PRINTER=SOFTWARE\CANON\%s\
KEY_PRINTER2=SOFTWARE\CANON\%s
KEY_BEFORE=SOFTWARE\CANON\%s_Printer_Driver\
KEY_BEFORE2=SOFTWARE\CANON\%s_Printer_Driver
KEY_BEFORE3=SOFTWARE\CANON\%s_Printer_Driver\WA
KEY_PDRVCAP=SOFTWARE\CANON\%s_Printer_Driver\CAP\
KEY_PDRVCAP2=SOFTWARE\CANON\%s_Printer_Driver\CAP

;---------------------------------------------------------------------
; [Registrys Key] 
;---------------------------------------------------------------------
[Registrys key]
Non Printer

;===========================================================================
;
; [UninstallDriverStore] Mention information about driver package deletion.
; WaitDeleteDriverPackage  : Driver package deletion weight maximum time
;
;===========================================================================
[UninstallDriverStore]
WaitDeleteDriverPackage=10

;===========================================================================
;
; [DriverStoreInfo] Mention a key word and character string when I search for a driver package.
;
;===========================================================================
[DriverStoreInfo]
CNP60

;===========================================================================
;
; [DeleteMonitor] A mention of a deletion object monitor.
;
;===========================================================================
[DeleteMonitor]
CP Language Monitor=CPMONNT.DLL
PB2 Language Monitor=NBMONNT.DLL
CJL Language Monitor2=AUCJLMNT.DLL
CPCA Language Monitor2=AUCPLMNT.DLL
CPCA Language Monitor3=CNAS0MMK.DLL
CPCA Language Monitor3a=CnAS0MNK.DLL
CPCA Language Monitor3b=CNAS0MOK.DLL
CPCA Language Monitor4=CNAS0MPK.DLL

[DeletePrintProcessors]
CnXP0PP

[Environments]
Windows NT x86
Windows x64
