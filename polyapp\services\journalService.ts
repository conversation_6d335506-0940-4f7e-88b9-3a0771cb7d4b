
import { ExtractedData, JournalEntry } from '../types';
import { CLOVER_BANK_INFO, VENDOR_OFFSET_ACCOUNTS, OUTPUT_HEADER } from '../constants';
import * as XLSX from 'xlsx';

function normalizeName(name: string): string {
    return name.toLowerCase()
        .replace(/\s+(polyclinic|polyclinics|polyclinc|center|clinic)\s*/g, ' ')
        .replace(/[^a-z0-9\s]/g, '')
        .trim()
        .replace(/\s+/g, '-');
}

const normalizedVendorOffsetAccounts: { [key: string]: string } = Object.entries(VENDOR_OFFSET_ACCOUNTS)
    .reduce((acc, [key, value]) => {
        acc[normalizeName(key)] = value;
        return acc;
    }, {} as { [key:string]: string });


function formatDateToDDMMYYYY(isoDate: string): string {
    // Handles dates like '2024-07-29'
    try {
        const date = new Date(isoDate);
        if (isNaN(date.getTime())) return isoDate; // Return original if invalid

        // Use UTC methods to avoid timezone-related date shifts
        const day = String(date.getUTCDate()).padStart(2, '0');
        const month = String(date.getUTCMonth() + 1).padStart(2, '0'); // getUTCMonth is 0-indexed
        const year = date.getUTCFullYear();
        return `${day}-${month}-${year}`;
    } catch (e) {
        return isoDate; // Fallback
    }
}

export function generateJournalEntries(data: ExtractedData): JournalEntry[] {
    const { accountName, accountNumber, transactions } = data;

    if (!transactions || transactions.length === 0) {
        return [];
    }

    // --- Lookups ---
    // Find bank info by matching either the short account number or the old long account number.
    const bankInfo = CLOVER_BANK_INFO.find(info => 
        info.accountNo === accountNumber || (info.oldAccountNo && info.oldAccountNo === accountNumber)
    );
    
    // Use the official short account number (e.g., KIBFR-xxxx) for the journal entry.
    // Fallback to the extracted number if no match is found.
    const finalJournalAccountNo = bankInfo ? bankInfo.accountNo : accountNumber;
    
    // Vendor offset lookup should use the official name from our records for consistency.
    // Fallback to the AI-extracted name only if the account is not in our records.
    const officialAccountName = bankInfo ? bankInfo.accountName : accountName;
    const normalizedAccountName = normalizeName(officialAccountName);
    const offsetAccountKey = Object.keys(normalizedVendorOffsetAccounts)
        .sort((a,b) => b.length - a.length) // Prioritize longer, more specific keys
        .find(key => normalizedAccountName.includes(key) || key.includes(normalizedAccountName));
    const offsetAccount = normalizedVendorOffsetAccounts[normalizedAccountName] || (offsetAccountKey ? normalizedVendorOffsetAccounts[offsetAccountKey] : 'N/A');

    if (!bankInfo) {
        // Warning is still useful to know if we couldn't find details like Activities, Country etc.
        console.warn(`Could not find matching bank info for account number: ${accountNumber}. Some fields may be 'N/A'.`);
    }
    if (offsetAccount === 'N/A') {
         // Use the name we actually used for the lookup in the warning.
         console.warn(`Could not find matching offset account for: ${officialAccountName}`);
    }

    // 1. Map all transactions to a preliminary entry structure. Dates remain in YYYY-MM-DD for processing.
    const mappedEntries = transactions.map(transaction => {
        const postingDate = transaction.date;
        const isCredit = transaction.type === 'credit';
        
        return {
            journalNumber: isCredit ? 1 : 2,
            journalName: isCredit ? 'CRNOTE' : 'STVINV',
            postingDate: postingDate,
            accountType: 6,
            accountNo: finalJournalAccountNo,
            description: transaction.description,
            // Per user request: CRNOTE amount on Debit side, STVINV amount on Credit side
            debitAmount: isCredit ? transaction.amount : '',
            creditAmount: isCredit ? '' : transaction.amount,
            currencyCode: 'KWD',
            exchangeRate: 100,
            offsetAccountType: 2,
            offsetAccount: offsetAccount || 'N/A',
            documentNo: '',
            documentDate: postingDate,
            dueDate: postingDate,
            assetTransType: '',
            postingProfile: 'Vend Post',
            paymentMode: '',
            paymentReference: '',
            activities: bankInfo?.activities || 'N/A',
            country: bankInfo?.country || 'N/A',
            departments: bankInfo?.departments || 'N/A',
            projectId: bankInfo?.projectId || 'N/A',
            propertyId: bankInfo?.propertyId || 'N/A',
            // Placeholders to be replaced after sorting
            lineNum: 0,
            numberOfVoucher: 0,
            invoiceNo: '',
        };
    });
    
    // 2. Club all debit amounts (now in creditAmount) equal or less than 9
    const smallDebits = mappedEntries.filter(e => e.journalName === 'STVINV' && typeof e.creditAmount === 'number' && e.creditAmount <= 9);
    let otherEntries = mappedEntries.filter(e => !(e.journalName === 'STVINV' && typeof e.creditAmount === 'number' && e.creditAmount <= 9));

    if (smallDebits.length > 0) {
        const totalSmallDebitAmount = smallDebits.reduce((sum, e) => sum + (e.creditAmount as number), 0);
        const latestDate = new Date(Math.max(...smallDebits.map(t => new Date(t.postingDate).getTime())));
        const latestDateString = latestDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD for processing

        const aggregatedDebitEntry = {
            ...smallDebits[0], // Use first small debit as a template
            postingDate: latestDateString,
            documentDate: latestDateString,
            dueDate: latestDateString,
            description: 'Aggregated Bank Charges and Fees',
            debitAmount: '',
            creditAmount: totalSmallDebitAmount,
        };
        otherEntries.push(aggregatedDebitEntry);
    }


    // 3. Sort the entries: first by journal name (CRNOTE > STVINV), then by posting date.
    otherEntries.sort((a, b) => {
        if (a.journalName !== b.journalName) {
            return a.journalName.localeCompare(b.journalName);
        }
        return new Date(a.postingDate).getTime() - new Date(b.postingDate).getTime();
    });

    // 4. Finalize the entries by iterating over the sorted list to assign sequential numbers and format dates.
    const shortAccountName = officialAccountName.split(' ')[0].toUpperCase().substring(0, 10);
    let lineNumCounter = 0;
    let lastJournalNum = -1; // Initialize with a value that won't match any journal number

    return otherEntries.map((entry, index) => {
        // Reset line number if the journal number changes
        if (entry.journalNumber !== lastJournalNum) {
            lastJournalNum = entry.journalNumber;
            lineNumCounter = 1;
        } else {
            lineNumCounter++;
        }
        
        const invoiceCounter = index + 1; // Use the overall index for a single, continuous sequence.

        const finalEntry: JournalEntry = {
            ...entry,
            lineNum: lineNumCounter,
            numberOfVoucher: lineNumCounter,
            // Per user request: new Invoice No format with a single continuous sequence
            invoiceNo: `${shortAccountName}-sal-${invoiceCounter}`,
            // Format dates to DD-MM-YYYY for the final output
            postingDate: formatDateToDDMMYYYY(entry.postingDate),
            documentDate: formatDateToDDMMYYYY(entry.documentDate),
            dueDate: formatDateToDDMMYYYY(entry.dueDate),
        };
        return finalEntry;
    });
}

export function convertToXLSX(data: JournalEntry[]): ArrayBuffer {
    const header = OUTPUT_HEADER;
    const rows = data.map(entry => [
        entry.journalNumber,
        entry.journalName,
        entry.lineNum,
        entry.postingDate,
        entry.accountType,
        entry.accountNo,
        entry.description,
        entry.debitAmount,
        entry.creditAmount,
        entry.currencyCode,
        entry.exchangeRate,
        entry.offsetAccountType,
        entry.offsetAccount,
        entry.invoiceNo,
        entry.documentNo,
        entry.documentDate,
        entry.dueDate,
        entry.assetTransType,
        entry.postingProfile,
        entry.paymentMode,
        entry.paymentReference,
        entry.numberOfVoucher,
        entry.activities,
        entry.country,
        entry.departments,
        entry.projectId,
        entry.propertyId
    ]);

    const worksheetData = [header, ...rows];
    const ws = XLSX.utils.aoa_to_sheet(worksheetData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'JournalEntries');
    
    const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
    
    return wbout;
}