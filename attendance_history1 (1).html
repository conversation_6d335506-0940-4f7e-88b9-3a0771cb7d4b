   

      <script type="text/javascript" src="https://hr.mazayarealestate.com/MenaITech/application/hrms/DB/Include/Shared/Jquery/jquery.min.js?dontcash=3496855512"></script>

    <script type="text/javascript" src="https://hr.mazayarealestate.com/MenaITech/application/hrms/mename/lib/form_actions.js?dontcash=5245283268"></script>
<script type="text/javascript" src="https://hr.mazayarealestate.com/MenaITech/application/hrms/mename/lib/formcheck.js?dontcash=6993711024"></script>
<link href="https://hr.mazayarealestate.com/MenaITech/application/hrms/DB/Include/Shared/css/freemiucss/select2.min.css?var=652142026" rel="stylesheet" />
<script type="text/javascript" src="https://hr.mazayarealestate.com/MenaITech/application/hrms/DB/Include/Shared/js/shared.js?dontcash=15735849804" ></script>
<script src="https://hr.mazayarealestate.com/MenaITech/application/hrms/DB/Include/Shared/js/select2.min.js"></script>

<style type="text/css">
    @font-face {
  font-family: 'passwords';

    src: url('https://hr.mazayarealestate.com/MenaITech/application/hrms/DB/skins/light/font/text-security-disc_password.ttf')  format('truetype');
}
</style>
<script>
    $(document).ready(function(e) {
        if($('.myinputfields.passwords') != undefined){
            $('.myinputfields.passwords').attr('type', 'password');
        }
        if($('.coustom-input-ta.myinputfields.login1') != undefined){
            $('.coustom-input-ta.myinputfields.login1').attr('type', 'password');
        }
    });
    
    </script>
    
    





<!DOCTYPE HTML>
 <!DOCTYPE HTML>
<html   dir="ltr">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=windows-1256">
    <meta name="viewport" content="width=device-width, initial-scale=1">
             <title>ESS - Attendance History</title>
       
    <!-- Including CSS Files -->
    <!------------------------->  

    <!-- Datetime Picker -->
    <script src="https://hr.mazayarealestate.com/MenaITech/application/hrms/DB/Include/Shared/Jquery/clockpicker.js"></script>     
    <!-- moment.js -->
    <script src="https://hr.mazayarealestate.com/MenaITech/application/hrms/DB/Include/Shared/Jquery/moment.js"></script>
        <link type="text/css" rel="stylesheet" href="https://hr.mazayarealestate.com/MenaITech/application/hrms/DB/Include/Shared/css/bootstrap/bootstrap.min.css?dontcash=v7.8.2208.06">
    <link type="text/css" rel="stylesheet" href="https://hr.mazayarealestate.com/MenaITech/application/hrms/DB/Include/Shared/css/custom-me-style.css?dontcash=v7.8.2208.06">	
    <link type="text/css" rel="stylesheet" href="https://hr.mazayarealestate.com/MenaITech/application/hrms/DB/Include/Shared/css/clockpicker.css">

   
    
            <link type="text/css" rel="stylesheet" href="https://hr.mazayarealestate.com/MenaITech/application/hrms/DB/Include/Shared/css/foundation-datepicker.min.css">
    
    <!-- foundation DatePicker -->
    <script src="https://hr.mazayarealestate.com/MenaITech/application/hrms/DB/Include/Shared/Jquery/foundation-datepicker.min.js?dontcash=v7.8.2208.06"></script>
    

    <link type="text/css" rel="stylesheet" href="https://hr.mazayarealestate.com/MenaITech/application/hrms/DB/Include/Shared/css/jquery.mCustomScrollbar.css">	    
    <link type="text/css" rel="stylesheet" href="https://hr.mazayarealestate.com/MenaITech/application/hrms/DB/Include/Shared/css/bootstrap-duallistbox.css">	    
    
    <link type="text/css" rel="stylesheet" href="https://hr.mazayarealestate.com/MenaITech/application/hrms/DB/Include/Shared/css/main-style.css?dontcash=v7.8.2208.06">    
    <link rel="stylesheet" href="https://hr.mazayarealestate.com/MenaITech/application/hrms/DB/Include/Shared/css/fontawesome/css/font-awesome.min.css">    <link type="text/css" rel="stylesheet" href="https://hr.mazayarealestate.com/MenaITech/application/hrms/DB/Include/Shared/css/custom-style-profile.css">     
    <link type="text/css" rel="stylesheet" href="https://hr.mazayarealestate.com/MenaITech/application/hrms/DB/Include/Shared/css/toastr.min.css">	    	
    <link type="text/css" rel="stylesheet" href="https://hr.mazayarealestate.com/MenaITech/application/hrms/DB/Include/Shared/css/tippy.css">	    	
    <link type="text/css" rel="stylesheet" href="https://hr.mazayarealestate.com/MenaITech/application/hrms/DB/Include/Shared/css/swiper.css">


    <script src="https://hr.mazayarealestate.com/MenaITech/application/hrms/DB/Include/Shared/Jquery/toastr.min.js"></script>
    	
    <style type="text/css">
			/*
		.required {
			color : #FF0000!important;
		}
		*/
		.required::after {
		    content: " *";
		    /* color : #FF0000 !important; */
		}
	



</style>    <style type="text/css">
        .datepicker.dropdown-menu {
            max-width: 203px !important;
        }
    </style>
</head> 
<html    dir="ltr">
<head>

<meta http-equiv="Pragma" content="no-cache" />
<meta http-equiv="Expires" content="-1" />
<meta http-equiv="Cache-Control" content="no-store" />
<meta http-equiv="Cache-Control" content="no-cache" />
<meta http-equiv="Cache-Control" content="must-revalidate" />
<meta http-equiv="Cache-Control" content="max-age=0" />
<meta content=" MenaITech is the first software company in the Arab region specialized in the development and distribution of Human Resources solutions to top public and private organizations of various countries operating in the Middle East and North Africa." name="Description" />
<meta content=" MenaITech ,Mena, Middle East and North Africa, HR, HRMS ,Payroll, Human Resources, solutions, PHP, webinterface, webbased, IT professionals" name="Keywords" />
<meta http-equiv="Content-Type" content="text/html; charset=windows-1256">
<META http-equiv="Content-Type" content="text/html; charset=windows-1256">
<TITLE>Attendance History</TITLE>
<style>
td {
    white-space: normal !important;
}
.red_tag {
    color: red;
}
.green_tag {
    color: #090;
}

</style>
</HEAD>
<body style="background:#FFFFFF" Class="ReportBody" leftmargin="0" bottommargin="0" marginheight="0" marginwidth="0">
<div style="direction:ltr" class='MEbodyDiv'>
<FORM name="form">
<SCRIPT language="javascript"> 
 window.parent.document.title="Attendance History";
</SCRIPT>
<!-- To display  top menu -->
<TABLE width="100%"  border="0" cellpadding="10" cellspacing="0">
            <tr>
                <td valign="top"> 
<div class="row pb-4">
	<div class="col-sm-4">		
		<div class="row">
			<div class="col-sm-12">
				<h3 class= 'black-text'>&nbsp;</h3>
			</div>
		</div>				
		<div class="row">
			<div class="col-sm-12">
				<h6 class= 'gray-text'>Al Mazaya Holding Co.</h6>
			</div>
		</div>		
	</div>
	<div class="col-sm-4 pt-3">
		<div class="row pt-3">
			<div class="col-sm-12 text-center ">
				<p class= 'black-text slip-title'>ATTENDANCE HISTORY</p>
			</div>
		</div>	
	</div>
	<div class="col-sm-4 d-flex align-items-end justify-content-end">
		<img  class= "" src="https://hr.mazayarealestate.com/MenaITech/application/hrms/MenaImages/Branch_Logos/Mazaya_new3.jpg" style='max-height: 80px; max-width: 150px;'>
	</div>	
</div>

 </td>
            </tr>
                <TR> 
      <TD valign="top"> 
<!--       <TABLE width="100%" border="0" cellspacing="0" cellpadding="0">
          <TR> 
            <TD width="78%"><TABLE width="350px" border="0" cellspacing="0" cellpadding="1">
              <TR>
                <TD width="83px" nowrap>
                  Employee Code</TD>
                <TD width="263px" nowrap> 
                  1185</TD>
              </TR>
              <TR>
                <TD nowrap>
                  Employee Name</TD>
                <TD nowrap>
                  Haitham Abdou</TD>
              </TR>
              <TR>
                <TD nowrap>&nbsp; </FONT></TD>
                <TD nowrap>&nbsp; </FONT></TD>
              </TR>
            </TABLE></TD>
          </TR>
        </TABLE> -->
<table border="0" class="table table-hover  table n-bordered-dark" id= 'result'>
    <thead >
         <TR> 
            <TD scope="col" class='text-center' colspan="2">&nbsp;</TD>
            <TD scope="col" class='text-center' align="center" colspan="2">Shift Start</TD>
            <TD scope="col" class='text-center' align="center" colspan="4">Shift End</TD>
        </TR>
        <TR> 
              <TD scope="col" class='text-center' width="9%" >Weekday</TD>
              <TD scope="col" class='text-center' width="11%">Attendance Day</TD>

              <TD scope="col" class='text-center' width="9%" >Work Time</TD>
              <TD scope="col" class='text-center' width="9%" >Entry Time</TD>
                             
               

              <TD scope="col" class='text-center' width="9%" >End Time</TD>
              <TD scope="col" class='text-center' width="9%" >Exit Time</TD>
                                   
              <TD scope="col" class='text-center' width="9%" >Early Out Permission</TD>                                
                <TD scope="col" class='text-center' width="9%" >Actual Hours</TD>     
        </TR>
    </thead>  
       
      <TR>
        <TD nowrap align="center">
            Thursday        </TD>
        <TD nowrap align="center">
            01/05/2025        </TD>


        <TD nowrap align="center">
             8:00 AM        </TD>
        <TD nowrap align="center">
         8:33 AM        </TD>
                                    <td align="center"> 3:30 PM</td>
        <td align="center"> 4:41 PM</td>
                        <td align="center" class="">0:00</td>                         <td align="center">8:08</td>
    </TR>   
                       <tr>
                    <TD nowrap align="center">
                        Friday                    </TD>
                    <TD nowrap align="center">
                        02/05/2025                    </TD>
                    <td nowrap align="center"></td>
                    <td nowrap align="center">&nbsp;</td>

                                                                                <td align="center" ></td> 
                    <td align="center" ></td>
                    
                                                            <td align="center" >0:00</td>                           
                                            </td>
                    <td align="center" >0:00</td>
                </tr>   
                                <tr>
                    <TD nowrap align="center">
                        Saturday                    </TD>
                    <TD nowrap align="center">
                        03/05/2025                    </TD>
                    <td nowrap align="center"></td>
                    <td nowrap align="center">&nbsp;</td>

                                                                                <td align="center" ></td> 
                    <td align="center" ></td>
                    
                                                            <td align="center" >0:00</td>                           
                                            </td>
                    <td align="center" >0:00</td>
                </tr>   
                       
      <TR>
        <TD nowrap align="center">
            Sunday        </TD>
        <TD nowrap align="center">
            04/05/2025        </TD>


        <TD nowrap align="center">
             8:00 AM        </TD>
        <TD nowrap align="center">
         8:42 AM        </TD>
                                    <td align="center"> 3:30 PM</td>
        <td align="center"> 4:23 PM</td>
                        <td align="center" class="">0:00</td>                         <td align="center">7:41</td>
    </TR>   
              
      <TR>
        <TD nowrap align="center">
            Monday        </TD>
        <TD nowrap align="center">
            05/05/2025        </TD>


        <TD nowrap align="center">
             8:00 AM        </TD>
        <TD nowrap align="center">
         8:46 AM        </TD>
                                    <td align="center"> 3:30 PM</td>
        <td align="center"> 6:06 PM</td>
                        <td align="center" class="">0:00</td>                         <td align="center">9:20</td>
    </TR>   
              
      <TR>
        <TD nowrap align="center">
            Tuesday        </TD>
        <TD nowrap align="center">
            06/05/2025        </TD>


        <TD nowrap align="center">
             8:00 AM        </TD>
        <TD nowrap align="center">
         8:37 AM        </TD>
                                    <td align="center"> 3:30 PM</td>
        <td align="center"> 8:30 PM</td>
                        <td align="center" class="">0:00</td>                         <td align="center">11:53</td>
    </TR>   
              
      <TR>
        <TD nowrap align="center">
            Wednesday        </TD>
        <TD nowrap align="center">
            07/05/2025        </TD>


        <TD nowrap align="center">
             8:00 AM        </TD>
        <TD nowrap align="center">
         8:51 AM        </TD>
                                    <td align="center"> 3:30 PM</td>
        <td align="center"> 7:41 PM</td>
                        <td align="center" class="">0:00</td>                         <td align="center">10:50</td>
    </TR>   
              
      <TR>
        <TD nowrap align="center">
            Thursday        </TD>
        <TD nowrap align="center">
            08/05/2025        </TD>


        <TD nowrap align="center">
             8:00 AM        </TD>
        <TD nowrap align="center">
         8:50 AM        </TD>
                                    <td align="center"> 3:30 PM</td>
        <td align="center"> 5:37 PM</td>
                        <td align="center" class="">0:00</td>                         <td align="center">8:47</td>
    </TR>   
                       <tr>
                    <TD nowrap align="center">
                        Friday                    </TD>
                    <TD nowrap align="center">
                        09/05/2025                    </TD>
                    <td nowrap align="center"></td>
                    <td nowrap align="center">&nbsp;</td>

                                                                                <td align="center" ></td> 
                    <td align="center" ></td>
                    
                                                            <td align="center" >0:00</td>                           
                                            </td>
                    <td align="center" >0:00</td>
                </tr>   
                                <tr>
                    <TD nowrap align="center">
                        Saturday                    </TD>
                    <TD nowrap align="center">
                        10/05/2025                    </TD>
                    <td nowrap align="center"></td>
                    <td nowrap align="center">&nbsp;</td>

                                                                                <td align="center" ></td> 
                    <td align="center" ></td>
                    
                                                            <td align="center" >0:00</td>                           
                                            </td>
                    <td align="center" >0:00</td>
                </tr>   
                       
      <TR>
        <TD nowrap align="center">
            Sunday        </TD>
        <TD nowrap align="center">
            11/05/2025        </TD>


        <TD nowrap align="center">
             8:00 AM        </TD>
        <TD nowrap align="center">
         8:47 AM        </TD>
                                    <td align="center"> 3:30 PM</td>
        <td align="center"> 4:09 PM</td>
                        <td align="center" class="">0:00</td>                         <td align="center">7:22</td>
    </TR>   
              
      <TR>
        <TD nowrap align="center">
            Monday        </TD>
        <TD nowrap align="center">
            12/05/2025        </TD>


        <TD nowrap align="center">
             8:00 AM        </TD>
        <TD nowrap align="center">
         8:36 AM        </TD>
                                    <td align="center"> 3:30 PM</td>
        <td align="center"> 8:05 PM</td>
                        <td align="center" class="">0:00</td>                         <td align="center">11:29</td>
    </TR>   
              
      <TR>
        <TD nowrap align="center">
            Tuesday        </TD>
        <TD nowrap align="center">
            13/05/2025        </TD>


        <TD nowrap align="center">
             8:00 AM        </TD>
        <TD nowrap align="center">
         8:44 AM        </TD>
                                    <td align="center"> 3:30 PM</td>
        <td align="center"> 7:46 PM</td>
                        <td align="center" class="">0:00</td>                         <td align="center">11:02</td>
    </TR>   
              
      <TR>
        <TD nowrap align="center">
            Wednesday        </TD>
        <TD nowrap align="center">
            14/05/2025        </TD>


        <TD nowrap align="center">
             8:00 AM        </TD>
        <TD nowrap align="center">
         8:18 AM        </TD>
                                    <td align="center"> 3:30 PM</td>
        <td align="center"> 6:16 PM</td>
                        <td align="center" class="">0:00</td>                         <td align="center">9:58</td>
    </TR>   
              
      <TR>
        <TD nowrap align="center">
            Thursday        </TD>
        <TD nowrap align="center">
            15/05/2025        </TD>


        <TD nowrap align="center">
             8:00 AM        </TD>
        <TD nowrap align="center">
         8:11 AM        </TD>
                                    <td align="center"> 3:30 PM</td>
        <td align="center"> 1:48 PM</td>
                        <td align="center" class="red_tag">1:42</td>                         <td align="center">5:37</td>
    </TR>   
               </TABLE>
        
        <table class="table table-hover  table n-bordered-dark" id= 'result'>
            <thead >
            <TR><TD scope="col" class='text-center' height="20px">&nbsp;</TD></TR>
            <TR><TD scope="col" class='text-center' height="20px"> Page&nbsp;1</TD></TR>
            <TR><TD scope="col" class='text-center' height="20px">&nbsp;</TD></TR>
            </thead>
        </TABLE>
        <div style="page-break-after:right;">&nbsp;</div>
        <table border="0" class="table table-hover  table n-bordered-dark" id= 'result'>
    <thead >
         <TR> 
            <TD scope="col" class='text-center' colspan="2">&nbsp;</TD>
            <TD scope="col" class='text-center' align="center" colspan="2">Shift Start</TD>
            <TD scope="col" class='text-center' align="center" colspan="4">Shift End</TD>
        </TR>
        <TR> 
              <TD scope="col" class='text-center' width="9%" >Weekday</TD>
              <TD scope="col" class='text-center' width="11%">Attendance Day</TD>

              <TD scope="col" class='text-center' width="9%" >Work Time</TD>
              <TD scope="col" class='text-center' width="9%" >Entry Time</TD>
                             
               

              <TD scope="col" class='text-center' width="9%" >End Time</TD>
              <TD scope="col" class='text-center' width="9%" >Exit Time</TD>
                                   
              <TD scope="col" class='text-center' width="9%" >Early Out Permission</TD>                                
                <TD scope="col" class='text-center' width="9%" >Actual Hours</TD>     
        </TR>
    </thead>  
                <tr>
                    <TD nowrap align="center">
                        Friday                    </TD>
                    <TD nowrap align="center">
                        16/05/2025                    </TD>
                    <td nowrap align="center"></td>
                    <td nowrap align="center">&nbsp;</td>

                                                                                <td align="center" ></td> 
                    <td align="center" ></td>
                    
                                                            <td align="center" >0:00</td>                           
                                            </td>
                    <td align="center" >0:00</td>
                </tr>   
                                <tr>
                    <TD nowrap align="center">
                        Saturday                    </TD>
                    <TD nowrap align="center">
                        17/05/2025                    </TD>
                    <td nowrap align="center"></td>
                    <td nowrap align="center">&nbsp;</td>

                                                                                <td align="center" ></td> 
                    <td align="center" ></td>
                    
                                                            <td align="center" >0:00</td>                           
                                            </td>
                    <td align="center" >0:00</td>
                </tr>   
                       
      <TR>
        <TD nowrap align="center">
            Sunday        </TD>
        <TD nowrap align="center">
            18/05/2025        </TD>


        <TD nowrap align="center">
             8:00 AM        </TD>
        <TD nowrap align="center">
         8:16 AM        </TD>
                                    <td align="center"> 3:30 PM</td>
        <td align="center"> 6:57 PM</td>
                        <td align="center" class="">0:00</td>                         <td align="center">10:41</td>
    </TR>   
              
      <TR>
        <TD nowrap align="center">
            Monday        </TD>
        <TD nowrap align="center">
            19/05/2025        </TD>


        <TD nowrap align="center">
             8:00 AM        </TD>
        <TD nowrap align="center">
         8:17 AM        </TD>
                                    <td align="center"> 3:30 PM</td>
        <td align="center"> 7:08 PM</td>
                        <td align="center" class="">0:00</td>                         <td align="center">10:51</td>
    </TR>   
              
      <TR>
        <TD nowrap align="center">
            Tuesday        </TD>
        <TD nowrap align="center">
            20/05/2025        </TD>


        <TD nowrap align="center">
             8:00 AM        </TD>
        <TD nowrap align="center">
         8:19 AM        </TD>
                                    <td align="center"> 3:30 PM</td>
        <td align="center"> 4:10 PM</td>
                        <td align="center" class="">0:00</td>                         <td align="center">7:51</td>
    </TR>   
              
      <TR>
        <TD nowrap align="center">
            Wednesday        </TD>
        <TD nowrap align="center">
            21/05/2025        </TD>


        <TD nowrap align="center">
             8:00 AM        </TD>
        <TD nowrap align="center">
         8:17 AM        </TD>
                                    <td align="center"> 3:30 PM</td>
        <td align="center"> 4:41 PM</td>
                        <td align="center" class="">0:00</td>                         <td align="center">8:24</td>
    </TR>   
              
      <TR>
        <TD nowrap align="center">
            Thursday        </TD>
        <TD nowrap align="center">
            22/05/2025        </TD>


        <TD nowrap align="center">
             8:00 AM        </TD>
        <TD nowrap align="center">
         8:28 AM        </TD>
                                    <td align="center"> 3:30 PM</td>
        <td align="center"> 4:46 PM</td>
                        <td align="center" class="">0:00</td>                         <td align="center">8:18</td>
    </TR>   
                       <tr>
                    <TD nowrap align="center">
                        Friday                    </TD>
                    <TD nowrap align="center">
                        23/05/2025                    </TD>
                    <td nowrap align="center"></td>
                    <td nowrap align="center">&nbsp;</td>

                                                                                <td align="center" ></td> 
                    <td align="center" ></td>
                    
                                                            <td align="center" >0:00</td>                           
                                            </td>
                    <td align="center" >0:00</td>
                </tr>   
                                <tr>
                    <TD nowrap align="center">
                        Saturday                    </TD>
                    <TD nowrap align="center">
                        24/05/2025                    </TD>
                    <td nowrap align="center"></td>
                    <td nowrap align="center">&nbsp;</td>

                                                                                <td align="center" ></td> 
                    <td align="center" ></td>
                    
                                                            <td align="center" >0:00</td>                           
                                            </td>
                    <td align="center" >0:00</td>
                </tr>   
                       
      <TR>
        <TD nowrap align="center">
            Sunday        </TD>
        <TD nowrap align="center">
            25/05/2025        </TD>


        <TD nowrap align="center">
             8:00 AM        </TD>
        <TD nowrap align="center">
         8:14 AM        </TD>
                                    <td align="center"> 3:30 PM</td>
        <td align="center"> 4:30 PM</td>
                        <td align="center" class="">0:00</td>                         <td align="center">8:16</td>
    </TR>   
              
      <TR>
        <TD nowrap align="center">
            Monday        </TD>
        <TD nowrap align="center">
            26/05/2025        </TD>


        <TD nowrap align="center">
             8:00 AM        </TD>
        <TD nowrap align="center">
         8:10 AM        </TD>
                                    <td align="center"> 3:30 PM</td>
        <td align="center"> 4:26 PM</td>
                        <td align="center" class="">0:00</td>                         <td align="center">8:16</td>
    </TR>   
              
      <TR>
        <TD nowrap align="center">
            Tuesday        </TD>
        <TD nowrap align="center">
            27/05/2025        </TD>


        <TD nowrap align="center">
             8:00 AM        </TD>
        <TD nowrap align="center">
         8:21 AM        </TD>
                                    <td align="center"> 3:30 PM</td>
        <td align="center"> 4:09 PM</td>
                        <td align="center" class="">0:00</td>                         <td align="center">7:48</td>
    </TR>   
              
      <TR>
        <TD nowrap align="center">
            Wednesday        </TD>
        <TD nowrap align="center">
            28/05/2025        </TD>


        <TD nowrap align="center">
             8:00 AM        </TD>
        <TD nowrap align="center">
         8:19 AM        </TD>
                                    <td align="center"> 3:30 PM</td>
        <td align="center">&nbsp;</td>
                        <td align="center" class="">0:00</td>                         <td align="center">0:00</td>
    </TR>   
                       <tr>
                    <TD nowrap align="center">
                        Thursday                    </TD>
                    <TD nowrap align="center">
                        29/05/2025                    </TD>
                    <td nowrap align="center"> 8:00 AM</td>
                    <td nowrap align="center">&nbsp;</td>

                                                                                <td align="center" > 3:30 PM</td> 
                    <td align="center" ></td>
                    
                                                            <td align="center" >0:00</td>                           
                                            </td>
                    <td align="center" >0:00</td>
                </tr>   
                                <tr>
                    <TD nowrap align="center">
                        Friday                    </TD>
                    <TD nowrap align="center">
                        30/05/2025                    </TD>
                    <td nowrap align="center"></td>
                    <td nowrap align="center">&nbsp;</td>

                                                                                <td align="center" ></td> 
                    <td align="center" ></td>
                    
                                                            <td align="center" >0:00</td>                           
                                            </td>
                    <td align="center" >0:00</td>
                </tr>   
                                <tr>
                    <TD nowrap align="center">
                        Saturday                    </TD>
                    <TD nowrap align="center">
                        31/05/2025                    </TD>
                    <td nowrap align="center"></td>
                    <td nowrap align="center">&nbsp;</td>

                                                                                <td align="center" ></td> 
                    <td align="center" ></td>
                    
                                                            <td align="center" >0:00</td>                           
                                            </td>
                    <td align="center" >0:00</td>
                </tr>   
                <TR  bgcolor="#FFFFFF" height="22px">
    <TD scope="col" class='font-weight-bold' colspan="4" align="right" nowrap>Totals</TD>
            
    <TD scope="col" class='text-center' colspan="2">&nbsp;</TD>
     
        <TD scope="col" class='text-center blue_border_bottom  red_tag' >1:42</TD>     
            <td align="center" class="blue_border_bottom">172:32</td>

</TR>
</TABLE>
 </TD>
</TR>
</TABLE>
 <TABLE width="99%" align="center">
    <thead >
<TR><TD scope="col" class='text-center' height="20px">&nbsp;</TD></TR>
<TR><TD scope="col" class='text-center' height="20px"> Page&nbsp;2</TD></TR>
</thead>
</TABLE>

</FORM>
</div>
</body>
</HTML>


 

 <script type="text/javascript">
var my_dictinary = unserialize('a:276:{i:77;s:6:\"Delete\";i:421;s:6:\"Choose\";i:484;s:3:\"All\";i:489;s:4:\"Both\";i:547;s:8:\"Inactive\";i:562;s:16:\"No Records Found\";i:564;s:5:\"Print\";i:587;s:53:\"The Salary Calculation Has Already Been Posted Before\";i:595;s:39:\"The Salary Calculation Was Moved Before\";i:629;s:32:\"Please Choose An Employee First.\";i:1003;s:53:\"Are You Sure You Want To Delete The Selected Records?\";i:1004;s:37:\"You Didn`t Choose A Record To Delete!\";i:1006;s:50:\"Are you sure you want to move the selected records\";i:1007;s:41:\"You Didn`t Choose Any Record For Posting!\";i:1008;s:48:\"Are you sure of the Post Transactions operation?\";i:1010;s:30:\"A Number Must Be Entered Here!\";i:1011;s:45:\"No loans are selected to distribute the loan!\";i:1012;s:26:\"This Field Must Be Filled!\";i:1019;s:21:\"The e-mail is invalid\";i:1020;s:19:\"The date is invalid\";i:1023;s:2:\"OK\";i:1172;s:5:\"Dear \";i:1173;s:3:\"Mr.\";i:1313;s:76:\"The Two Passwords You Entered Didn`t Match! Please Re-Confirm Your Password.\";i:1324;s:19:\"Nothing Is Selected\";i:1334;s:45:\"Wrong Old Password! Password Was Not Changed.\";i:1449;s:2:\"AM\";i:1500;s:2:\"PM\";i:1888;s:19:\"Change Transactions\";i:1917;s:55:\"You Didn`t Choose A Position. Please, Select One First.\";i:2058;s:19:\"Terminated Employee\";i:2280;s:44:\"No Employees Returned For Salary Calculation\";i:2366;s:13:\"Are you sure?\";i:2518;s:6:\"Cancel\";i:2539;s:30:\"Please Enter A Positive Number\";i:2706;s:33:\"Zero Amounts Are Not Allowed Here\";i:2793;s:22:\"` Is Not Allowed Here!\";i:2794;s:23:\"\\\" Is Not Allowed Here!\";i:2876;s:15:\"Active Employee\";i:2903;s:14:\"Merit Increase\";i:2907;s:75:\"The End Date Can Not Be Before The Start Date! Please Re-enter The End Date\";i:3029;s:35:\"This Scholarship Has Been Approved!\";i:3192;s:20:\"Disciplinary Actions\";i:3229;s:18:\"Calculation Failed\";i:3230;s:22:\"Net Salary Is Negative\";i:3231;s:53:\"The Hiring Date For The Employee Is After This Month!\";i:3232;s:63:\"A Termination Transaction Has Been Performed For This Employee!\";i:3312;s:113:\"Some Raises Were Not Posted! The Employee Salary Has Conflicted With The Salary Range Limits In His Salary Scale!\";i:3340;s:54:\"Employee Has An Off-Cycle Leaves Payment In This Month\";i:3588;s:14:\"Service Period\";i:3596;s:6:\"Closed\";i:3597;s:10:\"In Process\";i:3762;s:67:\"The Employee Has An Offcycle Leave With No Resumption Date Entered!\";i:3763;s:12:\"Cut Off Date\";i:3805;s:65:\"Employee Has An Off-Cycle Leave In This Month With No Resumption!\";i:3845;s:71:\"The Hiring Date For The Employee Is After The Cut-Of-Date Of That Month\";i:3855;s:13:\"With Salaries\";i:3903;s:39:\"A Positive Number Must Be Entered Here!\";i:3904;s:39:\"5 Numbers Is The Maximum To Enter Here!\";i:3905;s:68:\"The Question Grade Can Not Be Greater Than The Question Upper Grade!\";i:4155;s:11:\"Career Path\";i:4235;s:68:\"The Appraisal Form Can Not Be Filled By The Same Evaluated Employee!\";i:4315;s:5:\"Times\";i:4365;s:94:\"The Net Salary Is Not Adequate For Some Bank/Check Transfers From Employee`s Other Banks Setup\";i:4541;s:31:\"The Employee Work Type is Daily\";i:4752;s:22:\"Financial Transactions\";i:4914;s:37:\"Please Wait To Complete The Operation\";i:5868;s:47:\"You Can Not Upload Any File With The Extension:\";i:6134;s:7:\"Confirm\";i:6836;s:5:\"Shift\";i:6894;s:51:\"Allowance Start Date Can Not Be Before Hiring Date!\";i:6895;s:47:\"Transaction Date Can Not Be Before Hiring Date!\";i:6897;s:10:\"In Advance\";i:7183;s:47:\"The Start-Time Cannot Be Equal To The End-Time!\";i:7184;s:44:\"The Start-Time Cannot Be After The End-Time!\";i:7248;s:4:\"Ext.\";i:7451;s:66:\"Salary Calculation For This Employee Was Stopped Before This Month\";i:7666;s:31:\"Please Enter An Integer Number!\";i:7717;s:40:\"You Can Not Delete A Posted Transaction!\";i:7718;s:52:\"Are You Sure You Want To Delete The Selected Record?\";i:7719;s:68:\"Some Transactions Are Posted And Will Not Be Deleted With The Batch!\";i:7720;s:51:\"Are You Sure you Want To Delete The Selected Items?\";i:7721;s:90:\"Are You Sure you Want To Delete The Selected Template And All Of Its Items (If Available)?\";i:7722;s:19:\"Please Choose Bank.\";i:7723;s:21:\"Please Choose Branch.\";i:7724;s:76:\"The End Date Can Not Be Before The Start Date! Please Re-enter The End Date.\";i:7725;s:122:\"You Cannot Save This Interview, Because The Chosen Interview Template Doesn`t Match The Questions List In The Detail Part!\";i:7726;s:62:\"You Have Updated Your Answers. Please Save Your Answers First!\";i:7727;s:47:\"Please Save Your Reference Check Results First!\";i:7728;s:45:\"This Voucher Is Already Posted, Are You Sure?\";i:7729;s:49:\"Loan Value And The Sum Of Payments Are Not Equal!\";i:7730;s:49:\"Net Salary And Distributed Amounts Are Not Equal!\";i:7731;s:54:\"Do You Want To Move To The Termination Checklist Page?\";i:7732;s:43:\"You Didn`t Choose Any Record For UnPosting!\";i:7733;s:78:\"The `Abroad Trip Details` Screen Can Only Be Viewed For International Courses.\";i:7734;s:86:\"The Resumption Date Can Not Be Before Start Date! Please Re-Enter The Resumption Date!\";i:7735;s:90:\"The Notification Date Can Not Be Before Start Date! Please Re-Enter The Notification Date!\";i:7736;s:39:\"Please Add Some Job Applications First!\";i:7737;s:34:\"This Number Must Be Larger Than 0!\";i:7738;s:59:\"The Maximum Number for the Allowed Characters Or Numbers Is\";i:7739;s:34:\"The Value Should Not Be More Than \";i:7740;s:34:\"The Value Should Not Be Less Than \";i:7741;s:64:\"The Employee Manager Can Not Be Determined As The Same Employee!\";i:7742;s:12:\"No Extension\";i:7743;s:42:\"This Date Should Not Be After Today`s Date\";i:7744;s:51:\"The Marriage Date Can Not Be Before The Birth Date!\";i:7745;s:55:\"The Insurance Date Can Not Be Before The Marriage Date!\";i:7746;s:61:\"The Allowance Start Date Can Not Be Before The Marriage Date!\";i:7747;s:57:\"The Child Birth Date Can Not Be Before The Marriage Date!\";i:7748;s:52:\"The Insurance Date Can Not Be Before The Birth Date!\";i:7749;s:58:\"The Allowance Start Date Can Not Be Before The Birth Date!\";i:7750;s:60:\"The Employee Marriage Date Can Not Be Before The Birth Date!\";i:7751;s:58:\"The Employee Hiring Date Can Not Be Before The Birth Date!\";i:7752;s:57:\"The Document Issue Date Can Not Be Before The Birth Date!\";i:7753;s:67:\"The Document Expiry Date Can Not Be Before The Document Issue Date!\";i:7754;s:63:\"The Document You Entered Has Expired, But Saving Will Complete!\";i:7755;s:68:\"The Previous Experience Start Date Can Not Be After The Hiring Date!\";i:7756;s:66:\"The Previous Experience End Date Can Not Be After The Hiring Date!\";i:7757;s:46:\"The End Date Can Not Be Before The Start Date!\";i:7758;s:50:\"The Drop Date Can Not Be Before The Delivery Date!\";i:7759;s:45:\"The Event Date Cannot Be Before Today`s Date!\";i:7760;s:60:\"You Can Not Hire This Employee! Employee`s Age Is Less Than \";i:7761;s:49:\"Years As You Setup In The System Parameters Page!\";i:7762;s:114:\"You Can Not Define This Employee As Married Or Add A Spouse Record For This Employee! Employee`s Age Is Less Than \";i:7763;s:61:\"You Can Not Add That Spouse Record! Spouse`s Age Is Less Than\";i:7764;s:50:\"This Field Must Be A Percentage And Less Than 100%\";i:7765;s:29:\"Please Select Marital Status!\";i:7766;s:21:\"Please Select Gender!\";i:7767;s:19:\"Please Select Site!\";i:7768;s:25:\"Please Select Department!\";i:7769;s:22:\"Please Select Section!\";i:7770;s:23:\"Please Select Position!\";i:7771;s:62:\"The Marriage Date Can Not Be Before The Employee`s Birth Date!\";i:7772;s:46:\"The `To Month` Can Not Be Before `From Month`!\";i:7773;s:85:\"The Furniture Compensation Date Can`t Be Before The Furniture Compensation Worth Date\";i:7774;s:82:\"The Furniture Compensation worth Date Can Not Be Before The Employee Hiring Date !\";i:7775;s:110:\"The Employee Salary Has Conflicted With The Salary Range Limits In His Salary Scale! The Record Was Not Saved!\";i:7776;s:66:\"Insurance Start Date Can Not Be After The Insurance Card End Date!\";i:7777;s:52:\"The Expire Date Can Not Be Before The Delivery Date!\";i:7778;s:70:\"The Employee Salary Has Exceeded The Budget! The Record Was Not Saved!\";i:7779;s:44:\"The Employee Salary Has Exceeded The Budget!\";i:7780;s:49:\"Notification Date Can Not Be After Current Date !\";i:7781;s:52:\"Notification Date Can Not Be Before Resumption Date!\";i:7782;s:45:\"This Date Can Not Be Before Transaction Date!\";i:7783;s:49:\"The Raise Date Cannot Be Before The Hiring Date !\";i:7784;s:68:\"The Allowance Raise Date Can Not Be Before The Allowance Start Date!\";i:7785;s:53:\"The Stop Date Can Not Be Before The Activation Date !\";i:7786;s:59:\"The Sum Of Employee And Company Contribution Should Be 100!\";i:7787;s:78:\"The Sum Of Employee, Company And Discount Should Be Equal To The Claim Amount!\";i:7788;s:87:\"Some Appraisals Were Sent To Managers Or Closed And Will Not Be Deleted With The Batch!\";i:7789;s:35:\"You can not delete unsaved records!\";i:7790;s:59:\"Are you Sure you want to delete selected Interview Request?\";i:7791;s:68:\"The Appraisal Form Can Not Be Edited By The Same Evaluated Employee!\";i:7792;s:39:\"The Weight Can Not Be Greater Than 100!\";i:7793;s:44:\"Factors Weight Total Should Be Equal To 100!\";i:7794;s:49:\"Weight Total Per Category Should Be Equal To 100!\";i:7795;s:54:\"Weight Total Per Category Should Be Equal To 100 Or 0!\";i:7796;s:101:\"Is Part Of An Un-Sent Evaluation, Do You Want To Update His Information In The Evaluation Definition?\";i:7797;s:37:\"Please Choose At Least One Item Type!\";i:7798;s:49:\"Are you sure of the Unpost Transaction operation?\";i:7799;s:76:\"You Should First Choose An Entry From The List To See Further Details Of It.\";i:7859;s:30:\"Can Not Complete This Process!\";i:7886;s:7:\"Answers\";i:8221;s:56:\"Some Transactions Are Posted, Batch Will Not Be Deleted \";i:8253;s:49:\"You Can Not Choose The Same Allowance Type Twice!\";i:8346;s:40:\"An Error Has Occurred! Please Try Again!\";i:8490;s:35:\"Entered Length Can Not Be Less Than\";i:8491;s:35:\"Entered Length Can Not Be More Than\";i:8492;s:48:\"Entered Length Should Match The Exact Length Of \";i:8493;s:36:\"Entered Value Should Be Greater Than\";i:8494;s:33:\"Entered Value Should Be Less Than\";i:8495;s:46:\"This Field Should Only Contain Numeric Values.\";i:8496;s:44:\"This Field Should Only Contain Alpha Values.\";i:8497;s:51:\"This Field Should Only Contain Alphanumeric Values.\";i:8498;s:74:\"This Field Must Only Contain Alphanumeric Values And The SyKPIsls (_ , -).\";i:8499;s:97:\"This Field Must Only Contain Alphanumeric Values And Special SyKPIsls (Except For The SyKPIsl `).\";i:8715;s:8:\"Settings\";i:8800;s:51:\"You Can Not Transfer The Task To The Same Employee!\";i:8801;s:55:\"The Deadline Date Can not Be Before The Delivery Date!!\";i:8802;s:61:\"You Can Not Add This Applicant! Applicant`s Age Is Less Than \";i:8803;s:117:\"You Can Not Define This Applicant As Married Or Add A Spouse Record For This Applicant! Applicant`s Age Is Less Than \";i:8804;s:58:\"Appraisal Period To Can`t Be Before Appraisal Period From!\";i:8805;s:25:\"Please Select Center Name\";i:8806;s:20:\"Please Select Course\";i:8807;s:37:\"You Have To Add Other Expenses First.\";i:8808;s:69:\"The Appraisal Edit End Date Can Not Be After The Appraisal End Date !\";i:8809;s:84:\"The Employee Salary Has Conflicted With The Salary Range Limits In His Salary Scale!\";i:8810;s:43:\"This Date Should Not Be Before Today`s Date\";i:8811;s:63:\"The Other Request From Date Can Not Be Before The Today`s Date!\";i:8812;s:44:\"The maximum length to enter in this field is\";i:8813;s:33:\"The Email Field Should be Filled!\";i:8814;s:53:\"You Can Not Permission The Customer Name field Empty!\";i:8815;s:86:\"You Can Not Enter A Minimum Number Of Raters That Exceeds The Actual Number Of Raters!\";i:8816;s:92:\"The Evaluation Start Date Can Not Be Before The Evaluation Date! Please Re-enter Start Date.\";i:8817;s:98:\"The Evaluation End Date Can Not Be Before The Evaluation Start Date! Please Re-enter The End Date.\";i:8818;s:140:\"You Can Not Define A First Warning Date To Be After The End Date! Please Decrease The Number Of Days Until You Send The First Warning Email.\";i:8819;s:142:\"You Can Not Define A Second Warning Date To Be After The End Date! Please Decrease The Number Of Days Until You Send The Second Warning Email.\";i:8820;s:138:\"You Can Not Define A Last Warning Date To Be After The End Date! Please Decrease The Number Of Days Until You Send The Last Warning Email.\";i:8821;s:4:\"This\";i:9022;s:55:\"The Employee Has Leave With No Resumption Date Entered!\";i:9225;s:98:\"You Can Not Calculate This Month`s Salary! There Are Some Unposted Transactions During This Month!\";i:9319;s:56:\"The Selected Course Is Not Available At Selected  Center\";i:9600;s:69:\"This Field Should Only Contain Percentage Values , Not Exceeding 100%\";i:9601;s:46:\"This Field Should Only Contain Decimal Values.\";i:9602;s:47:\"This Field Should Only Contain Positive Values.\";i:9603;s:56:\"This Field Should Only Contain Positive Non-Zero Values.\";i:9604;s:53:\"This Field Should Only Contain A Valid Email Address.\";i:9605;s:60:\"The Start-Time Cannot Be More Than OR Equal To The End-Time.\";i:9606;s:47:\"The Entered Time Should Be On The Format HH:MM.\";i:9607;s:46:\"This Field Should Only Contain Positive Values\";i:9822;s:10:\"Loading...\";i:9858;s:78:\"The Start Date Can Not Be Before The End Date! Please Re-enter The Start Date \";i:9875;s:38:\"Dates Periods Should Not Be Overlapped\";i:10316;s:26:\"Incorrect Transaction Code\";i:10513;s:16:\"In Notice Period\";i:10542;s:87:\"This Month Has A Cost Distribution Done, The Employee`s Salary Can Not Be Recalculated!\";i:10543;s:128:\"This Month Has An Accumulated Balance And The Next Month`s Salary Was Calculated, The Employee`s Salary Can Not Be Recalculated!\";i:10575;s:43:\"Salary Is Calculated For The Previous Month\";i:10578;s:59:\"Can Not Calculate Previous Salary, Leave already Calculated\";i:10645;s:52:\"Leave starting date can\'t be before the current date\";i:10796;s:4:\"Like\";i:10798;s:6:\"Unlike\";i:10855;s:49:\"Are You Sure You Want To Exit Discarding Changes?\";i:10999;s:13:\"Notifications\";i:11001;s:12:\"Mark As Read\";i:11002;s:14:\"Mark As Unread\";i:11010;s:9:\"Follow-up\";i:11011;s:16:\"Do Not Follow-up\";i:11286;s:72:\"Are You Sure That You Want To Stop Receiving Answers For This Question ?\";i:11398;s:75:\"The To Grade Can Not Be Less Than From Grade! Please Re-enter The To Grade.\";i:11529;s:42:\"Entered Value Should Be Less Than Or Equal\";i:11530;s:45:\"Entered Value Should Be Greater Than Or Equal\";i:11777;s:140:\"The Salaries Are Already Posted And Locked, This Transaction Will Affect The Salaries Reports, Do You Want To Continue Calculation Process ?\";i:11865;s:73:\"You Can Not Recalculate Salaries, This Month Salaries Were Already Locked\";i:11969;s:47:\"Retroactive Date Cant Not Be Before Hiring Date\";i:12334;s:31:\"Please Fill The Required Fields\";i:12379;s:51:\"The Employee Has Not Resumed From The Vacation Yet!\";i:12423;s:18:\"Connection Timeout\";i:12640;s:35:\"Date Period Must Be A Month Or Less\";i:12858;s:5:\"Alert\";i:13110;s:14:\"Overtime Plans\";i:13111;s:22:\"Analyze Overtime Plans\";i:13188;s:85:\"Total Overtime Hours Exceeds Requested Total Overtime Hours,Do You Want To Continue ?\";i:13252;s:29:\"Duplicated - Already Exported\";i:13253;s:22:\"Exceeded Allowed Limit\";i:13260;s:16:\"Exporting Failed\";i:13270;s:23:\"Please Insert Batch No.\";i:50091;s:12:\"Full Profile\";i:50163;s:62:\"Are You Sure You Want To Delete All Records Within This Batch?\";i:50165;s:110:\" Some of employees skills are already updated. Are you sure you want to delete the skills from employees file?\";i:50819;s:66:\"Employee Feedback Is Mandatory Before Posting Disciplinary Actions\";i:50860;s:32:\"Non-Payroll Benefit Transactions\";i:50889;s:25:\"Non-Payroll Benefit Raise\";i:51175;s:11:\"In progress\";i:51517;s:59:\"Sum Of Base And Additional S.S Should Be Less Than Or Equal\";i:52220;s:24:\"The Employee Age Exceeds\";i:52221;s:28:\"(The Maximum Age For Hiring)\";i:52260;s:26:\"The Employee Age Less Than\";i:52261;s:28:\"(The Minimum Age For Hiring)\";i:52448;s:65:\"Wrong Format Inserted. Please Enter The Period In Format YY:MM:DD\";i:52486;s:4:\"ID #\";i:52487;s:9:\"Appraisal\";i:52617;s:37:\"You Can Not Add More Than Five Items.\";i:53002;s:50:\"Are You Sure You Want To Update Business Details ?\";i:53568;s:28:\"Please select a record first\";i:53903;s:12:\"Done, Close!\";i:53905;s:12:\"Under the ID\";i:54004;s:47:\"This Field Should Not Contain Single Quotation.\";i:54269;s:121:\"This Month\'s Salary Has Been Converted To Other Income Or Other Deduction, The Employee\'s Salary Can Not Be Recalculated!\";i:54739;s:6:\"Alert!\";i:55072;s:133:\"The Employee Salary Has Conflicted With The Salary Range Limits In His Salary Scale! Nevertheless The Operation Was Done Successfully\";i:55716;s:121:\"This Month\'s Salary Has Been Converted To Other Income Or Other Deduction, The Employee\'s Salary Can Not Be Recalculated!\";i:56691;s:19:\"Direct Subordinates\";i:56692;s:21:\"InDirect Subordinates\";i:58081;s:63:\"The Employee Has Vacation With No Resumption Transaction Posted\";i:58194;s:39:\"The Employee Has an Initial Termination\";}');
</script>
    <style>
        /*
        .required {
            color : #FF0000!important;
        }
        */
        .required::after {
            content: " *";
            /* color : #FF0000 !important; */
        }
    </style>
        
<style>
    .mandatoryfield input[validation*='required'],.mandatoryfield select[validation*='required'],.mandatoryfield textarea[validation*='required']
    {
        color:#FF0000 !important;
    }
    .mandatoryHeader
    {
        color:#FF0000 !important;
    }
    .modal {
        z-index: 99999 !important;
    }
</style>
        <link type="text/css" rel="stylesheet" href="https://hr.mazayarealestate.com/MenaITech/application/hrms/DB/Include/Shared/css/print.css?dontcash=v7.8.2208.06">  
        
  



<script type="text/javascript">

// this script to add a new class on the mandatory fields
$(document).ready(function(){
    $('FONT[color*="#FF0000"]').addClass('mandatoryfields');
    $('FONT[style*="#FF0000"]').addClass('mandatoryfields');    
});

    function open_attach(file_path1) {
        window.open("https://hr.mazayarealestate.com/MenaITech/application/hrms/MenaImages/Attachments/"+file_path1,'_blank',"width="+(screen.width-8)+", height="+(screen.height-60)+", scrollbars=yes; noresize=no");
    }

   

    $(document).ready(function() {
        if($('#RSIFrame').html()==null )
       {
            try {opener.parent.Remove_LoadingBox();}
            catch (e)
            {
                try {top.Remove_LoadingBox();} catch (e){}
            }
            try {parent.Remove_LoadingBox();} catch (e)
            {
                try {top.Remove_LoadingBox();} catch (e){}
            }
        }
        $("body").click(function(a){try {parent.hide_sub_menu_box(a); } catch (e) {}})

        $(document).keydown(function (){
            try {zero_time();}
            catch (e)
            {
                try {window.parent.zero_time();}
                catch (e)
                {
                    try {window.opener.zero_time();}
                    catch (e)
                    {
                        try {parent.zero_time();}
                        catch (e)
                        {
                            try {parent.parent.zero_time();}
                            catch (e)
                            {
                                try {opener.parent.zero_time();}
                                catch (e)
                                {
                                    try {opener.parent.parent.zero_time();}
                                    catch (e)
                                    {
                                        try {opener.zero_time();} catch (e){}
                                    }
                                }
                            }
                        }
                    }
                }
            }
        });
      
        $(document).click(function (){
            try {zero_time();}
            catch (e)
            {
                try {window.parent.zero_time();}
                catch (e)
                {
                    try {window.opener.zero_time();}
                    catch (e)
                    {
                        try {parent.zero_time();}
                        catch (e)
                        {
                            try {parent.parent.zero_time();}
                            catch (e)
                            {
                                try {opener.parent.zero_time();}
                                catch (e)
                                {
                                    try {opener.parent.parent.zero_time();}
                                    catch (e)
                                    {
                                        try {opener.zero_time();} catch (e){}
                                    }
                                }
                            }
                        }
                    }
                }
            }
        });

      
        $(document).keydown(function (){
            try {zero_time();}
            catch (e)
            {
                try {window.parent.zero_time();}
                catch (e)
                {
                    try {window.opener.zero_time();}
                    catch (e)
                    {
                        try {parent.zero_time();}
                        catch (e)
                        {
                            try {opener.zero_time();} catch (e){}
                        }
                    }
                }
            }
        });

        $(document).click(function (){
            try {zero_time();}
            catch (e)
            {
                try {window.parent.zero_time();}
                catch (e)
                {
                    try {window.opener.zero_time();}
                    catch (e)
                    {
                        try {parent.zero_time();}
                        catch (e)
                        {
                            try {opener.zero_time();} catch (e){}
                        }
                    }
                }
            }
        });


        if(0!=1)
        {
            parent.$('#actionMenu').css('display','block');
        }



        
       $('input[type=text],textarea').each(function(){
            var CurrVal = $(this).val();
            var inputCurrVal = CurrVal.replace(/\'\'/g,"'");
            $(this).val(inputCurrVal)
        });


    });

    function add(name,type) {
        var forms=document.getElementsByTagName('form');
        if(type==null) type='hidden'
        for(var i in forms){
            if(typeof forms[i].name=='undefined') continue;
            var element = document.createElement("input");
            element.setAttribute("type", type);
            element.setAttribute("name", name);
            element.setAttribute("id", name);
            if(typeof forms[i].appendChild!='undefined')
                forms[i].appendChild(element);

        }
    }

    /*-------------------- Check The Browser Type if MSIE 6 Will Not Call The Function --------------------*/
    var browser_type;
    browser_type=navigator.appVersion;


    /*---------------------------------/ Function Resize All Select list /-----------------------------------*/
    /*---------------------------------- ------------------------------- ------------------------------------*/

    


    function reportPrintFN() {
        $(document).ready(function(e)
        {
            const printStyles = `
                @media print {
                 @page {
                margin: 0; /* Set default margins */
                padding: 0;
                    }
                }
            `;
          
            const styleSheet = document.createElement("style");
            styleSheet.type = "text/css";
            styleSheet.media = "print";
            styleSheet.textContent = printStyles;
            document.head.appendChild(styleSheet);
            window.print();
        });
    }

</script>
<script type='text/javascript'> function show_org_chart(){ return false };</script>
    <script  type="text/JavaScript">
        function detectKey(obj)
        {
            if ((event.keyCode == 78 || event.keyCode == 83) && (event.ctrlKey)) {  // ignore ctrl+n and ctrl+s in all reports
                event.cancelBubble = true;
                event.returnValue = false;
                event.keyCode = false; return false;
            }

            return ;
        }
        document.onkeydown = detectKey;
    </script>

<script type="text/javascript">
    var EnableOnAllPages=true;
    $(document).ready(function () {

        if(typeof ( parent.HideIframe)==typeof (Function)){
            if(EnableOnAllPages==false) {
                parent.HideIframe();
            }else{
                parent.ShowIframe();
            }};

        //check for any inputChangeBeforeSave value



    });
</script>


<script type="text/javascript">
    
    function IeVersion() {
    //Set defaults
    var value = {
        IsIE: false,
        TrueVersion: 0,
        ActingVersion: 0,
        CompatibilityMode: false
    };

    //Try to find the Trident version number
    var trident = navigator.userAgent.match(/Trident\/(\d+)/);
    if (trident) {
        value.IsIE = true;
        //Convert from the Trident version number to the IE version number
        value.TrueVersion = parseInt(trident[1], 10) + 4;
    }

    //Try to find the MSIE number
    var msie = navigator.userAgent.match(/MSIE (\d+)/);
    if (msie) {
        value.IsIE = true;
        //Find the IE version number from the user agent string
        value.ActingVersion = parseInt(msie[1]);
    } else {
        //Must be IE 11 in "edge" mode
        value.ActingVersion = value.TrueVersion;
    }

    //If we have both a Trident and MSIE version number, see if they're different
    if (value.IsIE && value.TrueVersion > 0 && value.ActingVersion > 0) {
        //In compatibility mode if the trident number doesn't match up with the MSIE number
    
        value.CompatibilityMode = value.TrueVersion != value.ActingVersion;
    }
    return value;
}
var ie = IeVersion();
if(ie.CompatibilityMode==true){
    // alert(site_host_path);
    // window.location.href('a');
   document.body.style.margin='0px';
    document.body.innerHTML='<div style="WIDTH: 100%; BACKGROUND: black; COLOR: red; text-align: center;padding-top:20%;height:400px;font-family:tahoma"><h2>You Should  Turn Off Compatibility View <br><br> Please Turn Off Compatibility View';
   
}

// alert(1);
</script>

<script type="text/javascript">
  

    function save_filters(){

        //dataString = $('form[name="form"]').serialize();
        var report_form = $('#report_form').val();
        var number_of_rows = $('#number_of_rows').val();
        var signature_template = $('#signature_template').val();
        var font_size = $('#font-size').val();
        if ($('#repeat_signiture').is(':checked')) {
            var repeat_signiture = 1;
        }else{
            var repeat_signiture = 0;
        }
        
        $.ajax({
          type: "POST",
          url: site_host_path+"/" +system_name+'ajax_proxi.php?ajax_URL=18&page_cat='+"save.php",
          data: {'report_form':report_form,'number_of_rows':number_of_rows,'signature_template':signature_template,'font_size':font_size,'repeat_signiture':repeat_signiture},
          success: function () {
                  parent.Remove_LoadingBox()
                  //submitForm(document.form)
              }
        });


    }
    $('input[name=onservice]').removeAttr('checked');
    $('input[name=onservice]:first').attr('checked',true);


      function handle_change_emp_when_active_adv_search() {
        // this function made by abuhamdeh to handle issue appear when change the employee code & is_advance_search in HRMS_config table is 1
        // enter button will active to submit the forms so all data in the form will submitted to another undeserved employee  
        $("employee_form, form").submit(function(event) {
                             if ("1185" != this.employee_code.value) {
                event.preventDefault();
                $("#creation").prop("disabled", true);
                $("[name=move_trans1]").val(0);
                }
                    });
    }
</script>
 

 
 


 



