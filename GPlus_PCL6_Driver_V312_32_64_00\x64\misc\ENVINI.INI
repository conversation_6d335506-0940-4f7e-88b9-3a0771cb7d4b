;=====================================================================
; Environment setup file for installation of the printer driver.
; Copyright CANON INC. 1999
; ENVINI.INI
;=====================================================================

[CommonIniData_w32]
Data Type=RAW
PrintProcessor=WinPrint
Priority=1
TransmissionRetryTimeout=
DeviceNotSelectedTimeout=
DefaultPort=

[CommonIniData_x64]
Data Type=RAW
PrintProcessor=WinPrint
Priority=1
TransmissionRetryTimeout=
DeviceNotSelectedTimeout=
DefaultPort=

[TCPIPPortMIBDefaultSetting]
ProtocolType=RAW
DoubleSpool=OFF
Queue=lp
PortNumber=9100
SNMPCommunity=public
SNMPEnabled=ON
SNMPDevIndex=1

[TCPIPPortMIBProtocolSetting]
FilterProtocols=ON
AcceptedProtocols=8,11,37,38

[TCPIPPortMIBDefaultSetting_Canon iR-ADV 6055/6065-U1 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iR-ADV 6255/6265-U2 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iR-ADV 6075-U1 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iR-ADV 6275-U2 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iR-ADV 6500s-Y1 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iR-ADV 6500s-Y2 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iR-ADV 8000s-U1 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iR-ADV 8200s-U2 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iR-ADV 8500s-Y1 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iR-ADV 8500s-Y2 PCL6  ]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iR-ADV C5000s-B1 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iR-ADV C5200s-B2 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iR-ADV C5500s-P1 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iR-ADV C7000s-A1 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iR-ADV C7500s-N1 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iR-ADV C9000s-A1 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iR-ADV C7200s-A2 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iR-ADV C9200s-A2 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iPR Server J100-200 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iPR Server K100-200 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iPR C60-G100 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iPR C65-G100 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iPR C65-G200 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iPR C600-G100 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iPR C650-G100 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iPR C650-G200 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iPR C700/800-G100 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iPR C750/850-G100 PCL6]
Queue=PRINT

[TCPIPPortMIBDefaultSetting_Canon iPR C750/850-G200 PCL6]
Queue=PRINT
