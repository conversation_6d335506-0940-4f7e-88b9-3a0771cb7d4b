﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xml:lang="zh" xmlns="http://www.w3.org/1999/xhtml" xmlns:hta="urn:HTMLApplication">
	<head>
		<meta name="Generator" content="CAESAR" />
		<meta http-equiv="Content-Type" content="application/xhtml+xml; charset=UTF-8" />
		<meta http-equiv="content-style-type" content="text/css" />
		<meta http-equiv="content-script-type" content="text/javascript" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="format-detection" content="telephone=no" />
		<meta name="viewport" content="width=device-width, maximum-scale=1.0, minimum-scale=1.0, initial-scale=1.0, user-scalable=no, shrink-to-fit=no" />
		<title>Microsoft Windows 版的 Canon Generic Plus UFR II/LIPSLX/PCL6/PS3 打印机驱动程序 3.12 版本</title>
		<hta:application id="id_canon_main_window" applicationname="canon_main_window" border="thick" borderstyle="normal" caption="yes" maximizebutton="yes" minimizebutton="yes" showintaskbar="yes" singleinstance="no" sysmenu="yes" version="1.0" windowstate="normal" contextmenu="yes" navigable="yes" />
		<style type="text/css">thead.table_important td.table_important_col_0,thead.table_note td.table_note_col_0{padding:2px 5px 2px 5px!important;}div.step_number{position: relative!important;top: 0.6em!important;left: -9px!important;width: 50px!important;text-align: center!important;}div.step_text{margin-top: -0.6em!important;margin-bottom: 15px!important;padding-bottom: 0!important;}div.step_path{margin-top: -12px!important;margin-left: 50px!important;margin-bottom: 15px!important;padding-left: 0!important;padding-bottom: 0!important;}div.link_to_top a{background:transparent!important;}td.outline_col_2 div.list{background:transparent!important;}thead.table_important td.table_important{background:transparent!important;}thead.table_note td.table_note{background:transparent!important;}a.open_all{background:transparent!important;}a.open_next_sibling{background:transparent!important;}a.close_all,a.close_next_sibling{background:transparent!important;}div.invisible_begin{background:transparent!important;}div.invisible_end{background:transparent!important;}*{background-image:none!important;}div.text:before{content:''!important;}div.list_link{position: relative!important;vertical-align: middle!important;margin-left: 31px!important;margin-right: 10px!important;margin-bottom: 0px!important;padding-left: 20px!important;padding-top: 1px!important;font-weight: bold!important;font-size: 80%!important;color: #353535!important;line-height: 150%!important;}div.list_link::before,div.list_link::after{position: absolute!important;top: 0!important;bottom: 0!important;left: 3px!important;margin: 4px 0 0 0!important;content: ''!important;vertical-align: middle!important;}div.list_link::before{width: 12px!important;height: 12px!important;border-radius: 50%!important;background: #e07667!important;}div.list_link::after{left: 7px!important;top: 2px!important;box-sizing: border-box!important;width: 3px!important;height: 3px!important;border: 4px solid transparent!important;border-left: 5px solid #fff!important;}table div.list {margin-left: 0!important;}div.list{background:Transparent!important;position: relative!important;vertical-align: middle!important;margin-left:30px!important;margin-right:10px!important;margin-top:2px!important;margin-bottom:2px!important;padding-left:20px!important;padding-top:0px!important;font-weight:Normal!important;font-size:80%!important;color:#000000!important;line-height: 150%!important;}div.list::before{position: absolute!important;top: 0!important;bottom: 0!important;left: 5px!important;margin: 6px 0 0 0!important;content: ''!important;vertical-align: middle!important;}div.list::before{width: 8px!important;height: 8px!important;border-radius: 50%!important;background: #555555!important;}div.table_note div.list::before{width: 8px!important;height: 8px!important;border-radius: 50%!important;background: #679c38!important;}div.table_important div.list::before{width: 8px!important;height: 8px!important;border-radius: 50%!important;background: #60b5cc!important;}table div.dash {margin-left: 12px!important;}div.dash{background:Transparent!important;position: relative!important;vertical-align: middle!important;margin-left:50px!important;margin-right:10px!important;margin-top:2px!important;margin-bottom:2px!important;font-weight:Normal!important;font-size:80%!important;padding:0px 0px 0px 20px!important;color:#000000!important;line-height: 150%!important;}div.dash::before{position: absolute!important;top: 0!important;bottom: 0!important;left: 11px!important;margin: 9px 0 0 0!important;content: ''!important;vertical-align: middle!important;}div.dash::before{width: 6px!important;height: 1px!important;background: #000000!important;}div.step_sub{position: relative!important;vertical-align: middle!important;font-size:80%!important;font-weight:Bold!important;margin:10px 10px 5px 22px!important;padding-left:15px!important;background:Transparent!important;color:#000000!important;line-height: 150%!important;}div.step_sub::before{position: absolute!important;top: 0!important;bottom: 0!important;left: 2px!important;margin: 5px 0 0 0!important;content: ''!important;vertical-align: middle!important;}div.step_sub::after{position: absolute!important;top: 0!important;bottom: 0!important;left: 1px!important;margin: 4px 0 0 0!important;content: ''!important;vertical-align: middle!important;}div.step_sub::before{width: 10px!important;height: 10px!important;background: #e07667!important;}div.step_sub::after{width: 8px!important;height: 8px!important;background: #ffffff!important;border: solid 1px #e07667!important;}</style>
		<style type="text/css" >
			<!--
*{
	font-family:"SimSun","Arial",Sans-serif;
	/*margin:0;*/
	/*padding:0;*/
	/*border:0;*/
	/*background-color:Transparent;*/
	font-size:100%;
	/*font-style:Normal;*/
	text-decoration:None;
}
html{
	border:0;
}
body{
	margin:0;
	padding:0;
	background:#FFFFFF;
}
span{
	color:Inherit;
}
div#id_content a{
	text-decoration:None;
}
div#id_content a:link{
	color:#3366A5;
	text-decoration:None;
}
div#id_content a:visited{
	color:#3366A5;
	text-decoration:None;
}
div#id_content a:hover{
	color:#EC6700;
	background-color:#FFFFCC;
	text-decoration:Underline;
}
/*-----------------------------------------------------------------------------------------------*/
div.chapter_title{
	background:#4682B4;
	padding:2px 5px 2px 5px;
	border-bottom:Solid 1px #4682B4;
	font-weight:Bold;
	font-size:80%;
	margin-bottom:16px;
	text-align:Right;
	color:#FFFFFF;
}
/*-----------------------------------------------------------------------------------------------*/
div.h1{
	padding:32px 0 2px 8px;
	border-bottom:Solid 1px #4682B4;
	background:#F3F8FA;
	font-weight:Bold;
	font-size:120%;
	margin-bottom:16px;
	vertical-align:Bottom;
	text-align:Left;
	color:#000080;
	position:Relative;
}
div.h1 a{
	position:Absolute;
	*position:Static!important;
}
/*-----------------------------------------------------------------------------------------------*/
div.h2,
div.h2_2line,
div.h2_3line{
	border:Solid 1px #4682B4;
	padding:5px;
	margin:30px 10px 5px 10px;
	font-size:90%;
	font-weight:Bold;
	background:#F3F8FA;
	color:#000080;
}
div.h2 a,
div.h2_2line a,
div.h2_3line a{
	font-weight:Bold;
	text-decoration:None;
	color:#0000FF;
}
div.h2 img,
div.h2_2line img,
div.h2_3line img{
	vertical-align:Middle;
	margin-right:2px;
	margin-left:0px;
}
/*-----------------------------------------------------------------------------------------------*/
div.h3{
	padding:5px;
	margin:25px 10px 5px 10px;
	font-size:90%;
	font-weight:Bold;
	background:#F3F8FA;
	border-bottom:Solid 1px #87CEEB;
	color:#000080;
}
div.h3 a{
	font-weight:Bold;
	text-decoration:None;
	color:#0000FF;
}
div.h3 img{
	vertical-align:Middle;
	margin-right:2px;
	margin-left:0px;
}
/*-----------------------------------------------------------------------------------------------*/
div.h4{
	margin:20px 10px 5px 10px;
	font-weight:Bold;
	font-size:90%;
	border-Bottom:Solid 1px #87CEEB;
	background:#FFFFFF;
	padding:0px 5px;
	color:#000080;
}
div.h4 img{
	vertical-align:Middle;
	margin-right:2px;
	margin-left:0px;
}
/*-----------------------------------------------------------------------------------------------*/
div.h5{
	margin:20px 10px 5px 15px;
	font-weight:Bold;
	font-size:80%;
	color:#000080;
}
div.h5 img{
	vertical-align:Middle;
	margin-right:5px;
	margin-left:4px;
}
/*-----------------------------------------------------------------------------------------------*/
div.h6{
	margin:15px 10px 5px 30px;
	font-weight:Bold;
	font-size:80%;
	color:#000080;
}
div.h6 img{
	vertical-align:Middle;
	margin-right:2px;
	margin-left:0px;
}
table div.h6{
	margin-left:0px;
	margin-top:10px;
}
/*-----------------------------------------------------------------------------------------------*/
div.text{
	font-size:80%;
	margin:5px 10px 5px 30px;
	color:#000000;
}
table div.text{
	margin:3px 0 0 0;
}
div.text img{
	vertical-align:Middle;
}
table div.text img{
	vertical-align:Middle;
}
/*-----------------------------------------------------------------------------------------------*/
div.text_level1{
	font-size:80%;
	margin:5px 10px 5px 40px;
	color:#000000;
}
table div.text_level1{
	margin:3px 0 0 10px;
}
/*-----------------------------------------------------------------------------------------------*/
div.text_level2{
	font-size:80%;
	margin:5px 10px 5px 50px;
	color:#000000;
}
table div.text_level2{
	margin:3px 0 0 20px;
}
/*-----------------------------------------------------------------------------------------------*/
div.text_level3{
	font-size:80%;
	margin:5px 10px 5px 60px;
	color:#000000;
}
table div.text_level3{
	margin:3px 0 0 30px;
}
/*-----------------------------------------------------------------------------------------------*/
div.text_center{
	text-align:Center;
	font-size:80%;
	color:#000000;
}
table div.text_center{
	margin:3px 0 0 0;
}
/*-----------------------------------------------------------------------------------------------*/
div.text_right{
	text-align:Right;
	font-size:80%;
	color:#000000;
	margin-right:10px;
}
table div.text_right{
	margin-right:0px;
}
/*-----------------------------------------------------------------------------------------------*/
div.annotation{
	font-size:75%;
	margin:0px 10px 5px 55px;
	text-indent:-1.5em;
	color:#000000;
}
/*-----------------------------------------------------------------------------------------------*/
div.list{
	background:Transparent;
	_background:Transparent;
	margin-left:30px;
	margin-right:10px;
	margin-top:2px;
	margin-bottom:2px;
	padding-left:20px;
	padding-top:0px;
	font-weight:Normal;
	font-size:80%;
	color:#000000;
}
table div.list{
	margin-left:0px;
}
div.list img{
	vertical-align:Top;
}
table div.list img{
	vertical-align:Top;
}

/*-----------------------------------------------------------------------------------------------*/
div.dash{
	margin-left:50px;
	margin-right:10px;
	margin-top:2px;
	margin-bottom:2px;
	font-weight:Normal;
	font-size:80%;
	background:Transparent;
	_background:Transparent;
	padding:0px 0px 0px 20px;
	color:#000000;
}
*:first-child+html div.dash{
	background:Transparent;
}
table div.dash{
	margin-left:12px;
}
div.dash img{
	vertical-align:Top;
}
/*-----------------------------------------------------------------------------------------------*/
div.example{
	font-size:80%;
	font-family:Monospace;
	margin:5px 10px 5px 30px;
	clear:Both;
	background:#707080;
	padding:5px;
	color:#FFFFFF;
}
div#id_content div.example a:link{
	color:#0000FF;
	text-decoration:None;
}
div#id_content div.example a:visited{
	color:#0000FF;
	text-decoration:None;
}
/*-----------------------------------------------------------------------------------------------*/
div.path{
	padding:3px 10px 3px 20px;
	font-size:80%;
	margin:0px 30px;
	background:Transparent;
	_background:Transparent;
	color:#666666;
}
div.path img{
	vertical-align:Middle;
}
table div.path{
	margin-left:0px;
}
/*-----------------------------------------------------------------------------------------------*/
div.step_number{
	background:#F8F8F8;
	font:Bold 200% "Arial";
	color:#4682B4;
	padding:0px 5px;
	margin-left:10px;
	margin-right:10px;
	margin-top:20px;
	margin-bottom:0px;
}
div.step_number span{
	font:Bold 100% "Arial";
	color:#4682B4;
}
/*-----------------------------------------------------------------------------------------------*/
div.step_path{
	padding:3px 10px 3px 30px;
	font-size:80%;
	margin:0px 10px;
	background:#F8F8F8;
	_background:#F8F8F8;
	color:#666666;
}
/*-----------------------------------------------------------------------------------------------*/
div.step_text{
	font-weight:Bold;
	padding:0px 10px 15px 5px;
	font-size:80%;
	margin:0px 10px 10px 10px;
	background:#F8F8F8;
	color:#000000;
}
div.step_text img{
	vertical-align:Middle;
}
@media print{div.step_text{
	padding:0px 10px 20px 5px;
}}
/*-----------------------------------------------------------------------------------------------*/
div.step_sub{
	font-size:80%;
	font-weight:Bold;
	margin:10px 10px 5px 15px;
	padding-left:15px;
	background:Transparent;
	_background:Transparent;
	color:#000000;
}
div.step_sub img{
	vertical-align:Top;
}
/*-----------------------------------------------------------------------------------------------*/
div.figure{
	text-align:Left;
	margin-left:30px;
	margin-bottom:10px;
}
table div.figure{
	margin-left:0;
}
/*-----------------------------------------------------------------------------------------------*/
div.link{
	margin:10px;
	font-size:80%;
	color:#3366A5;
}
/*-----------------------------------------------------------------------------------------------*/
div.link a{
	text-decoration:None;
	padding-left:10px;
	background:Transparent;
	_background:Transparent;
}
/*-----------------------------------------------------------------------------------------------*/
div.list_link{
	background:Transparent;
	_background:Transparent;
	margin-left:30px;
	margin-right:10px;
	margin-bottom:0px;
	padding-left:20px;
	padding-top:1px;
	font-weight:Normal;
	font-size:80%;
	color:#3366A5;
}
table div.list_link{
	margin-left:0px;
}
/*-----------------------------------------------------------------------------------------------*/
div.link_to_top{
	text-align:Right;
	padding:5px 10px 30px 10px;
	font-size:80%;
	color:#3366A5;
	border-top:Dotted 1px #87CEEB;
	margin-top:30px;
}
@media print{div.link_to_top{
	display:None;
}}
div.link_to_top a{
	text-decoration:None;
	padding-left:12px;
	background:Transparent;
	_background:Transparent;
}
/*-----------------------------------------------------------------------------------------------*/
div.trouble_shooting{
	margin:10px 10px 10px 30px;
}
table.trouble_shooting{
	border-collapse:Collapse;
	table-layout:Fixed;
}
td.trouble_shooting{
	border:Solid 1px #FFFFFF;
}
td.trouble_shooting_col_0{
	padding:5px;
	vertical-align:Middle;
	font-size:100%;
	width:80px;
	background:#C0C0C0;
	padding-bottom:10px;
}
td.trouble_shooting_col_1{
	background:#EFEFEF;
	padding-top:5px;
	padding-bottom:10px;
	padding-left:5px;
}
/*-----------------------------------------------------------------------------------------------*/
div.outline{
	margin:10px 10px 10px 30px;
}
table.outline{
	border-collapse:Collapse;
	font-size:100%;
}
td.outline{
	padding:10px 5px 10px 5px;
}
td.outline_col_0{
	vertical-align:Middle;
	text-align:Center;
	border:Solid 1px #87CEEB;
	background:#F5F5F5;
	width:30px;
	font-size:130%;
}
td.outline_col_0 div.h6{
	color:#4682B4;
	margin:0px;
}
td.outline_col_1{
	vertical-align:Middle;
	border:Solid 1px #87CEEB;
	border-left:None;
	background:#F5F5F5;
	width:150px;
}
td.outline_col_1 div.h6{
	margin:0px;
	color:#000000;
}
td.outline_col_2{
	vertical-align:Middle;
	background:#FFFFFF;
	border:Solid 1px #87CEEB;
	padding:3px 3px 3px 10px;
}
td.outline_col_2 div.list{
	background:Transparent;
	_background:Transparent;
}
/*-----------------------------------------------------------------------------------------------*/
div.table_ui{
	margin:5px 10px 10px 30px;
}
table.table_ui{
	table-layout:Auto;
	border-collapse:Collapse;
	border:None;
	border-bottom:Solid 1px #E0E0E0;
}
td.table_ui_col_0{
	padding:0px;
	vertical-align:Top;
	font-size:100%;
	width:150px;
	color:#000000;
	padding:0px 0px 5px 0px;
	border-right:None;
}
td.table_ui_col_1{
	background:#FFFFFF;
	padding:3px;
	padding-left:10px;
	border:Solid 1px #E0E0E0;
	border-left:Solid 1px #E0E0E0;
	border-right:None;
	border-bottom:None;
	vertical-align:Top;
}
td.table_ui_col_0 div.ui_level0{
	background:#FFFFFF;
	font-size:80%;
	padding:6px 3px 1px 3px;
	border-top:Solid 1px #E0E0E0;
	border-right:None;
	border-left:Solid 10px #FFFFFF;
	margin-left:0px;
}
td.table_ui_col_0 div.ui_level1{
	background:#FFFFFF;
	font-size:80%;
	padding:6px 3px 10px 3px;
	border-top:Solid 1px #E0E0E0;
	border-right:None;
	border-left:Solid 10px #87BEDB;
	margin-left:0px;
}
td.table_ui_col_0 div.ui_level2{
	background:#FFFFFF;
	font-size:80%;
	padding:6px 3px 10px 3px;
	margin-left:10px;
	border-top:Solid 1px #E0E0E0;
	border-right:None;
	border-left:Solid 10px #A0D0EA;
}
td.table_ui_col_0 div.ui_level3{
	background:#FFFFFF;
	font-size:80%;
	padding:6px 3px 10px 3px;
	margin-left:20px;
	border-top:Solid 1px #E0E0E0;
	border-right:None;
	border-left:Solid 10px #C6E5F6;
}
td.table_ui_col_0 div.ui_level1 img,
td.table_ui_col_0 div.ui_level2 img,
td.table_ui_col_0 div.ui_level3 img{
	vertical-align:Middle;
}
/*-----------------------------------------------------------------------------------------------*/
table div.table_important,
table div.table_caution,
table div.table_warning,
table div.table_note{
	margin-left:20px;
}
div.table_important,
div.table_caution,
div.table_warning,
div.table_note{
	margin:10px 10px 10px 30px;
}
*:first-child+html table.table_important,
*:first-child+html table.table_caution,
*:first-child+html table.table_warning,
*:first-child+html table.table_note{
	width:Auto;
	table-layout:Fixed;
}
table.table_important,
table.table_caution,
table.table_warning,
table.table_note{
	background:#FFFFFF;
	border-collapse:Collapse;
	font-size:100%;
	width:100%;
	_width:Auto;
	_table-layout:Fixed;
}
*:first-child+html table td.table_important_col_0,
*:first-child+html table td.table_caution_col_0,
*:first-child+html table td.table_warning_col_0,
*:first-child+html table td.table_note_col_0{
	width:Auto;
}
table td.table_important_col_0,
table td.table_caution_col_0,
table td.table_warning_col_0,
table td.table_note_col_0{
	width:100%;
	_width:Auto;
	vertical-align:Top;
}
*:first-child+html tbody.table_important td.table_important_col_0,
*:first-child+html tbody.table_caution td.table_caution_col_0,
*:first-child+html tbody.table_warning td.table_warning_col_0,
*:first-child+html tbody.table_note td.table_note_col_0{
	width:Auto;
}
tbody.table_important td.table_important_col_0,
tbody.table_caution td.table_caution_col_0,
tbody.table_warning td.table_warning_col_0,
tbody.table_note td.table_note_col_0{
	padding-left:10px;
	width:100%;
	_width:Auto;
}
@media print{
thead.table_important td.table_important,
thead.table_caution td.table_caution,
thead.table_warning td.table_warning,
thead.table_note td.table_note{
	padding-bottom:5px;
}}
tbody.table_important td.table_important,
tbody.table_caution td.table_caution,
tbody.table_warning td.table_warning,
tbody.table_note td.table_note{
	padding-left:10px;
	padding-top:2px;
	border-bottom:None;
	padding-bottom:5px;
	border-top:None;
}
thead.table_important td.table_important_col_0,
thead.table_note td.table_note_col_0{
	padding:2px 5px 2px 30px;
	text-align:Left;
	border-bottom:None;
}
thead.table_caution td.table_caution_col_0,
thead.table_warning td.table_warning_col_0{
	padding:2px 23px 5px 5px;
	text-align:Center;
	border-bottom:Solid 1px #808080;
}
thead.table_caution td.table_caution_col_0 div img,
thead.table_warning td.table_warning_col_0 div img{
	margin-right:3px;
}
thead.table_important td.table_important div,
thead.table_caution td.table_caution div,
thead.table_warning td.table_warning div,
thead.table_note td.table_note div{
	font-weight:Bold;
}
table.table_important{
	border:Solid 1px #808080;
}
table.table_caution,
table.table_warning{
	border:Solid 1px #808080;
}
table.table_note{
	border:Solid 1px #808080;
}
thead.table_important td.table_important{
	background:Transparent;
	_background:Transparent;
}
thead.table_caution td.table_caution div,
thead.table_warning td.table_warning div{
	font-size:120%;
}

thead.table_note td.table_note{
	background:Transparent;
	_background:Transparent;
}
thead.table_important td.table_important div{
	color:#4682B4;
}
thead.table_caution td.table_caution div,
thead.table_warning td.table_warning div{
	color:#000000;
}
thead.table_note td.table_note div{
	color:#808080;
}
thead.table_important td.table_important_col_0,
tbody.table_important td.table_important_col_0{
	border-right:Solid 1px #808080;
}
thead.table_caution td.table_caution_col_0,
tbody.table_caution td.table_caution_col_0,
thead.table_warning td.table_warning_col_0,
tbody.table_warning td.table_warning_col_0{
	border-right:Solid 1px #808080;
}
thead.table_note td.table_note_col_0,
tbody.table_note td.table_note_col_0{
	border-right:Solid 1px #808080;
}
/*-----------------------------------------------------------------------------------------------*/
div.table_no_border{
	margin:10px 10px 10px 30px;	
}
table div.table_no_border{
	margin-left:10px;
}
table.table_no_border{
	border:None;
	width:Auto;
}
thead.table_no_border td.table_no_border{
	padding:0px 5px 0px 0px;
	border:None;
	font-size:100%;
	text-align:Left;
	font-weight:Bold;
	vertical-align:Middle;
}
tbody.table_no_border td.table_no_border{
	padding:0px 5px 0px 0px;
	vertical-align:Top;
	border:None;
	font-size:100%;
	padding-bottom:5px;
}
tbody.table_no_border td.table_no_border div{
	margin-top:0px;
	margin-bottom:0px;
}
/*-----------------------------------------------------------------------------------------------*/
div.table_matrix{
	margin:10px 10px 10px 30px;
}
table div.table_matrix{
	margin-left:20px;
}
table.table_matrix{
	border-collapse:Collapse;
	border:Solid 1px #808080;
}
thead.table_matrix td.table_matrix{
	padding:5px;
	background-color:#DDDDDD;
	border:Solid 1px #808080;
	font-size:100%;
	text-align:Center;
	font-weight:Normal;
	vertical-align:Middle;
}
tbody.table_matrix td.table_matrix_col_0{
	padding:5px 0px;
	padding-left:5px;
	width:120px;
	vertical-align:Middle;
	background-color:#EFEFEF;
	border:Solid 1px #808080;
	font-size:100%;
	font-weight:Normal;
}
tbody.table_matrix td.table_matrix{
	padding:3px 10px;
	vertical-align:Middle;
	border:Solid 1px #808080;
	font-size:100%;
}
thead.table_matrix td.table_matrix_col_1{
	background-color:#DDDDDD;
	border:Solid 1px #808080;
	padding:5px;
}
tbody.table_matrix td.table_matrix_col_1{
	vertical-align:Middle;
	border:Solid 1px #808080;
}
thead.table_matrix td.table_matrix_col_0{
	border:Solid 1px #808080;
	background-color:#DDDDDD;
	padding:5px;
}
thead.table_matrix td.table_matrix div{
	color:#000000;
}
/*-----------------------------------------------------------------------------------------------*/
div.table_general{
	margin:10px 10px 10px 30px;
}
table div.table_general{
	margin-left:20px;
}
table.table_general{
	border-collapse:Collapse;
	border:Solid 1px #808080;
}
thead.table_general td.table_general{
	padding:5px;
	border:Solid 1px #808080;
	font-size:100%;
	text-align:Center;
	font-weight:Normal;
	vertical-align:Middle;
}
tbody.table_general td.table_general_col_0{
	padding:5px 0px;
	padding-left:5px;
	width:120px;
	vertical-align:Middle;
	border:Solid 1px #808080;
	font-size:100%;
	font-weight:Normal;
}
tbody.table_general td.table_general{
	padding:3px 10px;
	vertical-align:Middle;
	border:Solid 1px #808080;
	font-size:100%;
}
thead.table_general td.table_general_col_1{
	border:Solid 1px #808080;
	padding:5px;
}
tbody.table_general td.table_general_col_1{
	vertical-align:Middle;
	border:Solid 1px #808080;
}
thead.table_general td.table_general_col_0{
	border:Solid 1px #808080;
	padding:5px;
}
thead.table_general td.table_general div{
	color:#000000;
}
/*-----------------------------------------------------------------------------------------------*/
div.table_number{
	margin:10px 10px 10px 35px;	
}
table div.table_number{
	margin-left:25px;
}
table.table_number{
	border-collapse:Collapse;
	border:None;
	width:Auto;
}
tbody.table_number td.table_number{
	padding:0px 5px 0px 0px;
	border:None;
	font-size:100%;
	text-align:Left;
	vertical-align:Top;
}
tbody.table_number td.table_number_col_0{
	width:1.5em;
	border:None;
}
tbody.table_number td.table_number_col_0 div{
	margin-left:0px;
	margin-right:0px;
	font-weight:Normal;
	text-align:Right;
}
tbody.table_number td.table_number_col_1{
	width:Auto;
}
/*-----------------------------------------------------------------------------------------------*/
span.bold{
	font-weight:Bold;
}
span.italic{
	font-style:Italic;
}
span.no_break{
	white-space:Nowrap;
}
span.superscript{
	vertical-align:Super;
	font-size:80%;
}
span.subscript{
	vertical-align:Sub;
	font-size:80%;
}
span.underline{
	text-decoration:Underline;
}
span.color_white{
	color:#FFFFFF;
}
span.color_blue{
	color:#0000FF;
}
span.color_red{
	color:#FF0000;
}
span.color_orange{
	color:#FFA500;
}
span.color_green{
	color:#008000;
}
span.bold\+color_red{
	color:#FF0000;
	font-weight:Bold;
}
span.bold\+italic{
	font-style:Italic;
	font-weight:Bold;
}
span.bold\+superscript{
	vertical-align:Super;
	font-size:80%;
	font-weight:Bold;
}
span.bold\+underline{
	text-decoration:Underline;
	font-weight:Bold;
}
span.italic\+superscript{
	vertical-align:Super;
	font-size:80%;
	font-style:Italic;
}
span.yenmark{
	/*font-family:"MS PGothic","Osaka","Arial","Helvetica",Sans-serif;*/
}
span.yenmark\+italic{
	/*font-family:"MS PGothic","Osaka","Arial","Helvetica",Sans-serif;*/
	font-style:Italic;
}
span.bold\+color_orange{
	color:#FFA500;
	font-weight:Bold;
}
span.bold\+superscript\+color_red{
	color:#FF0000;
	vertical-align:Super;
	font-size:80%;
	font-weight:Bold;
}
span.bold\+superscript\+color_orange{
	color:#FFA500;
	vertical-align:Super;
	font-size:80%;
	font-weight:Bold;
}
span.bold\+no_break{
	white-space:Nowrap;
	font-weight:Bold;
}
span.bold\+no_break\+superscript{
	white-space:Nowrap;
	font-weight:Bold;
	vertical-align:Super;
	font-size:80%;
}
span.no_break\+superscript{
	white-space:Nowrap;
	vertical-align:Super;
	font-size:80%;
}
span.superscript\+color_red{
	color:#FF0000;
	vertical-align:Super;
	font-size:80%;
}
span.superscript\+color_orange{
	color:#FFA500;
	vertical-align:Super;
	font-size:80%;
}
span.ui_text{
}
span.ui_bold{
	font-weight:Bold;
}

/*-----------------------------------------------------------------------------------------------*/
div#id_breadcrumbs{
	font-size:70%;
	position:Absolute;
	left:5px;
	top:10px;
	z-index:1;
	cursor:Default;
	display:None;
}
div#id_breadcrumbs a{
	margin:0px 5px;
}
div#id_breadcrumbs a:link{
	color:#0000FF;
}
div#id_breadcrumbs a:visited{
	color:#0000FF;
}
span.style_name{
	font:Normal 8pt "Tahoma";
	color:#FF0000;
	margin:0px 5px;
}
div.open_close_all{
	font-size:80%;
	margin:5px 10px 5px 30px;
	color:#3366A5;
	text-align:Right;
}
div.open_close_next_sibling{
	font-size:80%;
	margin:5px 10px 5px 30px;
	color:#3366A5;
	text-align:Right;
}
div#id_content div.open_close_all a:hover,
div#id_content div.open_close_next_sibling a:hover{
	text-decoration:None;
}
a.open_all:hover,
a.close_all:hover,
a.open_next_sibling:hover,
a.close_next_sibling:hover{
	border-bottom:Solid 1px #3366A5;
}
a.open_all,
a.close_all,
a.open_next_sibling,
a.close_next_sibling{
	border-bottom:Dotted 1px #3366A5;
	padding-right:15px;
}
a.open_all{
	margin-right:5px;
}
a.open_next_sibling{
}
a.close_all,
a.close_next_sibling{
}
div.invisible_begin{
	color:#FFFFFF;
	font-size:80%;
	text-align:Right;
}
div.invisible_end{
	color:#FFFFFF;
	font-size:80%;
	text-align:Right;
}
div.invisible{
	display:None;
	border-right:Dotted 1px #808080;
	margin-right:10px;
}
@media print{
div.open_close_all{
	display:None;
}
div.open_close_next_sibling{
	display:None;
}
div.invisible{
	display:Block;
	border-right:None;
	margin-right:0px;
}}
div.hr{
	border-top:Solid 2px #808080;
	margin:25px 10px 0px 10px;
	font-size:0pt;
	color:#FFFFFF;
}
hr{
	color:#808080;
	background-color:#808080;
	margin:25px 10px 0px 10px;
	height:2px;
	border:0;
}
/*-----------------------------------------------------------------------------------------------*/
@supports (-ms-accelerator:true) { @media print {
	div./*[style]*/list {
		background: none!important;
		position: relative;
	}
	div./*[style]*/list:before {
		content: url(style_list.gif);
		line-height: 100%;
		position: absolute;
		top: 0px;
		left: 4px;
	}
	div./*[style]*/dash {
		background: none!important;
		position: relative;
	}
	div./*[style]*/dash:before {
		content: url(style_dash.gif);
		line-height: 100%;
		position: absolute;
		top: 0px;
		left: 4px;
	}
	div./*[style]*/path {
		background: none!important;
		position: relative;
	}
	div./*[style]*/path:before {
		content: url(style_path.gif);
		line-height: 100%;
		position: absolute;
		top: 0px;
		left: 1px;
	}
	div./*[style]*/step_path {
		background: #F8F8F8!important;
		position: relative;
	}
	div./*[style]*/step_path:before {
		content: url(style_path.gif);
		line-height: 100%;
		position: absolute;
		top: 0px;
		left: 8px;
	}
	div./*[style]*/step_sub {
		background: none!important;
		position: relative;
	}
	div./*[style]*/step_sub:before {
		content: url(style_step_sub.gif);
		line-height: 100%;
		position: absolute;
		top: 0px;
		left: 8px;
	}
	div./*[style]*/step_sub {
		background: none!important;
		position: relative;
	}
	div./*[style]*/step_sub:before {
		content: url(style_step_sub.gif);
		line-height: 100%;
		position: absolute;
		top: 0px;
		left: 8px;
	}
	div./*[style]*/link a {
		background: none!important;
		position: relative;
	}
	div./*[style]*/link a:before {
		content: url(style_link.gif);
		line-height: 100%;
		position: absolute;
		top: 0px;
		left: 0px;
	}
	div./*[style]*/list_link {
		background: none!important;
		position: relative;
	}
	div./*[style]*/list_link:before {
		content: url(style_link.gif);
		line-height: 100%;
		position: absolute;
		top: 0px;
		left: 0px;
	}
	thead./*[style]*/table_important td./*[style]*/table_important {
		background: none!important;
		position: relative;
	}
	thead./*[style]*/table_important td./*[style]*/table_important:before {
		content: url(style_important.gif);
		line-height: 100%;
		position: absolute;
		top: 0px;
		left: 0px;
	}
	thead./*[style]*/table_note td./*[style]*/table_note {
		background: none!important;
		position: relative;
	}
	thead./*[style]*/table_note td./*[style]*/table_note:before {
		content: url(style_note.gif);
		line-height: 100%;
		position: absolute;
		top: 0px;
		left: 0px;
	}
}}
-->
		</style>
	</head>
	<body>
		<!--CONTENT_START-->
		<div id="id_content">
			<div class="h1">Microsoft Windows 版的 Canon Generic Plus UFR II/LIPSLX/PCL6/PS3 打印机驱动程序 3.12 版本</div>
			<div class="text">在使用本软件前，请仔细阅读安装过程中显示的佳能软件许可协议。也可查看随附的CD-ROM或下载的驱动程序文件夹中[misc]文件夹中存储的佳能软件许可协议（EULAzh-CN.rtf）。</div>
			<div class="text">本文件中包括帮助用户如何在Microsoft Windows系统下安装并使用打印机驱动程序的辅助信息。</div>
			<div class="text">与大多数软件和硬件产品一样，某些信息是在手册制作之后公开的。本文件中提供了关于此打印机驱动程序的更新信息。如果需要其他帮助，请与当地的 Canon 公司联系。</div>
			<div class="h2"><a id="0c970eb48f6aeb22f5fe56ddcefd64fa"></a>目录</div>
			<div class="list_link"><a href="#636d58b85e05af14fba26f23ec13e2c6">概述</a></div>
			<div class="list_link"><a href="#cfbfe503012e612edb9d60eefc68d5af">系统要求</a></div>
			<div class="list_link"><a href="#ff162bfa526082b732442a0925874960">安装驱动程序</a></div>
			<div class="list_link"><a href="#b6b8cb34c536624321aae23eb2118d27">从驱动程序配置打印设置</a></div>
			<div class="list_link"><a href="#a9472a69374d08acb201ac44575378ed">安装时的警告、限制和约束</a></div>
			<div class="list_link"><a href="#74722ef065ccaf1fa5e99bf717f10223">打印时的警告、限制和约束</a></div>
			<div class="list_link"><a href="#9024e78bf21c831c5b42c4569778fc7f">使用打印处理器功能时的注意和限制</a></div>
			<div class="list_link"><a href="#4a6bdb2eb8fe4b63a3a88976d8016900">特定打印机型号的限制</a></div>
			<div class="h4"><a id="636d58b85e05af14fba26f23ec13e2c6"></a>概述</div>
			<div class="text">本打印机驱动程序允许从 Windows 应用程序打印文档 。它还支持以下功能。</div>
			<div class="text">- Access Management System<br />- 高级保密打印<br />- Canon Driver Configuration Tool</div>
			<div class="text_level2">您可以使用Canon Driver Configuration Tool来更改驱动程序的出厂默认设置，从而创建自定义的驱动程序集。有关该实用程序的详细信息，请参考Canon Driver Configuration Tool的用户指南。</div>
			<div class="text_level2">当根据本驱动程序创建自定义的驱动程序集时，请使用 4.20 或更高版本的 Canon Driver Configuration Tool。</div>
			<div class="h4"><a id="cfbfe503012e612edb9d60eefc68d5af"></a>系统要求</div>
			<div class="text">本驱动程序可以在下列系统环境中使用。</div>
			<div class="h5">操作系统</div>
			<div class="text">Microsoft Windows 10/11</div>
			<div class="text">Microsoft Windows Server 2016/2019/2022/2025</div>
			<div class="h5">对于基于服务器的运算环境：</div>
			<div class="h6">在服务器环境中的操作系统：</div>
			<div class="text">Microsoft Windows Server 2016/2019/2022/2025</div>
			<div class="h6">服务器组件：</div>
			<div class="text">Citrix Virtual Apps and Desktops 7 1912(LTSR)/2203(LTSR)/2303/2305/2308/2311</div>
			<div class="text">VMware vSphere 7.0/8.0</div>
			<div class="text">Microsoft Application Virtualization (App-V)</div>
			<div class="text">Remote Desktop Service on Windows Server 2016/2019/2022/2025</div>
			<div class="h6">用于集群服务器的操作系统：</div>
			<div class="text">Microsoft Windows Server 2016/2019/2022/2025</div>
			<div class="h5">计算机</div>
			<div class="text">正确运行上述操作系统软件的计算机</div>
			<div class="text">但不支持 ARM 架构。</div>
			<div class="h4"><a id="ff162bfa526082b732442a0925874960"></a>安装驱动程序</div>
			<div class="text">我们建议使用安装程序更新或添加驱动程序。也可从 Windows 的“添加打印机向导”更新或添加驱动程序。</div>
			<div class="text">有关详细的安装步骤，请参考 Canon 门户网站上的安装指南 (https://oip.manual.canon/)。</div>
			<div class="text">有关安装的注意事项，请参考“<a href="#a9472a69374d08acb201ac44575378ed">安装时的警告、限制和约束</a>”。</div>
			<div class="h4"><a id="b6b8cb34c536624321aae23eb2118d27"></a>从驱动程序配置打印设置</div>
			<div class="text">要使用驱动程序配置打印设置，请执行下面的步骤。</div>
			<div class="h6"><a id="f8822f7fe39803d6f87bdd066f3ba2cd"></a>更改默认设置</div>
			<div class="text">1. 从开始菜单的 [设置] 中显示 [蓝牙和其他设备]（或 [设备]）。</div>
			<div class="text">2. 在 [打印机和扫描仪] 中，针对要设置的设备显示管理屏幕。</div>
			<div class="text_level1">要显示管理屏幕，请单击设备或单击相应设备的 [管理]。</div>
			<div class="text">3. 针对要设置的设备单击 [打印首选项]。</div>
			<div class="h6">在打印时设置</div>
			<div class="text">1. 选择应用程序的打印菜单。</div>
			<div class="text">2. 在显示的打印屏幕中，选择所使用的设备 → 单击 [首选项]。</div>
			<div class="text_level1">根据应用程序的不同，[首选项] 可能被称作“打印机属性”或“属性”。</div>
			<div class="h4"><a id="a9472a69374d08acb201ac44575378ed"></a>安装时的警告、限制和约束</div>
			<div class="list">如果更改所用计算机的用户权限并运行安装程序，可能无法正确进行安装。在这种情况下，以Administrator身份登录，然后重新进行安装。</div>
			<div class="list">如果在基于服务器的计算环境下使用本驱动程序，请在客户端计算机上使用支持驱动程序的操作系统。</div>
			<div class="list">如果在服务器 PC 上安装驱动程序时指定了用作共享打印机的 SMB（NetBIOS 网络）打印机，请使用“添加打印机向导”将其安装为网络打印机。</div>
			<div class="list">在将 Server Core 安装到 Windows Server 操作系统的环境中，无法使用本驱动程序的安装程序。请使用操作系统的安装功能来安装驱动程序。</div>
			<div class="list">如果使用默认设置安装本驱动程序，所有已经安装的Generic Plus打印机驱动程序将更新到最新版本。如需在不更新已安装的驱动程序前提下添加最新版本的驱动程序，请按照以下其中一个步骤执行操作。</div>
			<div class="dash">在安装过程中显示的[要安装驱动程序的打印机列表]检查屏幕中，从[选择要更新的打印机]中选择并删除不想更新的设备，然后继续进行安装。</div>
			<div class="dash">使用 Windows 的添加打印机功能指定 inf 文件。</div>
			<div class="list">如果要安装的驱动程序版本比已安装的驱动程序更旧，则会显示对话框询问您是否要使用当前的文件，请允许使用这些当前的文件。</div>
			<div class="list">如果英语操作系统环境中的设备名称包含双字节字符，将不能从 [设备设置] 页 &gt; [获取设备状态信息] 获取设备状态。如果发生这种情况，请将打印机名称改为不包含双字节字符。</div>
			<div class="list">如果在打印服务器（指向并打印）环境中使用共享打印机，必须在服务器计算机中安装Canon Driver Information Assist Service，以便使用获取设备信息功能。</div>
			<div class="text_level2">另外，如果您使用 Microsoft BiDi Communication，而不是使用 Canon Driver Information Assist Service，则可以在 [设备设置] 页的 [详细设置] &gt; [其他设置] 中进行切换。</div>
			<div class="list">根据所使用的环境，[配置文件]中的名称和自定义纸张尺寸名称可能无法理解。如果发生这种情况，请安装适合的语言包。</div>
			<div class="list">插件软件单独出售，无法安装到本驱动程序中。</div>
			<div class="h4"><a id="74722ef065ccaf1fa5e99bf717f10223"></a>打印时的警告、限制和约束</div>
			<div class="list">如果您通过应用程序将奇数页数的文档打印为双面文档，则应用程序可能会自动插入空白页以将页数增加为偶数。当发生这种情形时，应用程序插入的空白页将以打印页的计数方式来计算。 </div>
			<div class="list">要使用该打印机，必须首先在打印机属性对话框的[设备设置]页中设置所用打印机的类型和打印机中安装的选项。如果选择了所用打印机中不可用的打印机驱动程序中的功能，可能会导致打印结果出错。</div>
			<div class="dash">如果设置的纸张来源未在打印机中安装，则将按照打印机的优先纸张来源提供纸张。</div>
			<div class="dash">如果指定的纸张来源和纸张尺寸不匹配，则会发生错误。</div>
			<div class="dash">如果在不支持双面打印的打印机中指定双面打印，则文档将打印在纸张的单面。</div>
			<div class="dash">如果在不支持装订的打印机中指定装订，则文档将以非装订方式进行打印。</div>
			<div class="dash">如果在不支持鞍式装订的打印机中指定鞍式装订，则文档将以非鞍式装订进行打印。</div>
			<div class="dash">如果在不支持存储箱存储的打印机上进行打印时选择[输出方法]&gt;[存储]，则文档将正常打印。如果在触摸面板显示屏上显示错误且打印机脱机，则当打印机联网时文档会再次打印。</div>
			<div class="dash">如果为不支持部门ID管理的打印机指定部门ID管理，则会发生错误。</div>
			<div class="list">根据所用打印机的选项组合，打印机驱动程序可能不会识别[设备设置] 选项卡 &gt; [获取设备状态信息]中安装的选项。为了使用不能识别的功能，在[设备设置]选项卡手动进行设置。</div>
			<div class="list">如果在所用设备的控制面板上设置了本驱动程序不支持的功能，则在打印时会启用在设备的控制面板上设置的功能。</div>
			<div class="list">据所使用的设备，无法提供的纸张类型和纸张尺寸以及无法使用的功能信息可从 [设备设置] 页 &gt; [获取设备状态信息] 中获取。</div>
			<div class="list">根据打印机的不同，在[设备设置]选项卡 &gt;[完成方式设置]中设置自定义纸张尺寸的功能（双面打印、装订等）也会有所不同。有关详细信息，请参阅打印机随附的手册。</div>
			<div class="list">通过 USB 或 WSD 连接设备时，可能无法使用获取设备信息功能、部门 ID 管理功能和用户验证功能。</div>
			<div class="list">如果打印倾斜细线，可能无法正确打印此类线条（线条没有出现在页面上或看上去断裂）。</div>
			<div class="list">如果在 Windows Server 操作系统中使用 Server Core，某些打印选项在本地环境或 Windows 远程桌面环境下不可用。</div>
			<div class="list">如果在 [设备设置] 页中单击 [获取设备状态信息]，可获取设备的配置信息且 [设备设置] 页中的很多设置均会自动配置。但是，[设备设置] 页中的 [纸张来源设置] 和 [详细设置] 对话框中的某些设置必须手动设置。</div>
			<div class="list">可在驱动程序的打印设置屏幕中设置的项目取决于 [设备设置] 页中的设置。如果使用 Canon Driver Configuration Tool 自定义驱动程序，请在设置 [设备设置] 页后设置打印设置屏幕中的各页。</div>
			<div class="list">若在 [基本设置]/[质量] 页的 [色彩模式] 中选择 [自动 [彩色/黑白]]，则根据数据、应用程序或驱动程序设置，可能会将黑白页面计为彩色页面。如要将文档打印成黑白文件，可在 [色彩模式] 中选择 [黑白]。</div>
			<div class="list">如果将黑白打印机指定彩色打印，则文档将以黑白输出，但打印质量可能变差且耗时较长。</div>
			<div class="list">可在“<a href="#f8822f7fe39803d6f87bdd066f3ba2cd">更改默认设置</a>”中显示的打印设置屏幕上注册自定义纸张尺寸。使用 Administrator 权限登录后，从打印设置屏幕的 [页面设置] &gt; [自定义纸张尺寸] 中注册自定义纸张尺寸。</div>
			<div class="list">在某些情况下，可以使用驱动程序指定自定义纸张尺寸范围并且打印机可以打印的纸张尺寸也不同。关于可以打印的自定义纸张尺寸信息，请参阅打印机随附的手册。</div>
			<div class="list">如果在纸张来源中装入的纸张尺寸与默认纸张尺寸不同，可能会在设备的控制面板上显示一条表示没有纸张的错误消息。如果发生这种情况，请检索设备状态信息，或在驱动程序中选择与设备匹配的配置文件。</div>
			<div class="list">如果在通过 USB 进行打印时从后台打印程序中取消作业，则可能无法通过网络打印。如果要通过网络进行打印，请从设备的控制面板取消打印作业。</div>
			<div class="list">如果驱动程序和设备的显示语言不符，可能无法在设备上正确显示作业名称、用户名称、存储的数据名称等。</div>
			<div class="list">如果设置了以下两项，可能无法执行打印。</div>
			<div class="dash">[设备设置]页 &gt; [详细设置] &gt; [完成方式] 页 &gt; [分套 [通过设备]] &gt; [关]</div>
			<div class="dash">[完成方式] &gt; [分套]</div>
			<div class="list">如果在设备中存储安全打印作业，可能无法打印其他作业。如果在输出安全打印作业后仍无法打印其他打印作业，请从计算机中重新打印。</div>
			<div class="list">如果指定要打印两份或更多份数，可能会出现以下情形。</div>
			<div class="dash">如果指定[输出方法] &gt; [安全打印]，将在您每次输入[PIN]时打印一份。</div>
			<div class="dash">如果指定[输出方法] &gt; [存储]，将保存要打印的份数和在设备上指定的打印作业，并且每次的份数为一。</div>
			<div class="text_level2">在这种情况下，如果您设置了以下其中一项，则将正确执行打印。</div>
			<div class="dash">[在主机后台打印] &gt; [禁用]</div>
			<div class="dash">[从应用程序 PS 输出] &gt; [关]</div>
			<div class="list">如果您在日本使用本产品，则会应用以下限制。</div>
			<div class="dash">如果在 [设备设置] 页上将 [纸盒2] 设置为信封输稿器的纸张来源，则无法正确送入以下尺寸的信封。</div>
			<div class="text_level3">[Envelope Monarch]/[Envelope No. 10 (COM10)]/[Envelope DL]/[Envelope C5]/[Envelope B5]</div>
			<div class="dash">即使在 [设备设置] 页 &gt; [完成方式设置] &gt; [打孔] 中选择了 [2/3 孔]/[2/4 孔]/[4 孔 [瑞典式]]，也无法进行 [3 孔] 或 [4 孔 [瑞典式]] 的打孔，因为设备不支持这些打孔类型。</div>
			<div class="dash">如果在 [纸张来源] 页的 [插入页设置] 对话框中选择 [标签纸] 作为 [要插入的页]，并选择 [纸盒2] 作为 [纸张来源]，将无法从不支持此设置组合的设备中正确输出标签纸。</div>
			<div class="list">根据所使用的打印机，在以下情况下打印可能比平时耗时长。</div>
			<div class="dash">如果在使用A4（横向）纸张来源打印时指定纵向。</div>
			<div class="dash">如果在使用A4R（纵向）纸张来源打印时指定横向。</div>
			<div class="dash">如果将A3纸张指定为横向打印。</div>
			<div class="text_level2">在这种情况下，如果在 [设备设置] 页 &gt; [详细设置] &gt; [纸张来源] 页中适当设置了 [数据旋转的驱动程序处理]，则可能可以提高打印速度。</div>
			<div class="list">如果拥有支持该功能的分页装订处理机，请手动设置[[鞍式装订设置] 的 [折叠 + 裁边] 功能]。</div>
			<div class="list">如果拥有支持该功能的分页装订处理机，请手动设置[指定装订时的偏移]。</div>
			<div class="list">如果使用堆纸器，请手动设置[[指定要堆叠的份数] 功能]。</div>
			<div class="list">如果更改设备的名称，将会删除某些在驱动程序中注册的配置文件设置（从文档属性中添加的配置文件）。如果要保存这些配置文件，请在更改设备名称之前将配置文件设置保存为文件，然后在更改设备名称之后可导入保存的文件。</div>
			<div class="h6">对于 UFR II/LIPSLX 驱动程序</div>
			<div class="list">如果长尺寸纸张设为自定义纸张尺寸，且打印方向设为横向，则打印结果可能不正确，具体取决于所使用的设备。在这种情况下，请获取设备信息或设置 [设备设置] 页 &gt; [详细设置] &gt; [页面设置] 页 &gt; [长尺寸纸张打印时旋转数据] &gt; [开]。</div>
			<div class="h6">对于 PCL6 驱动程序</div>
			<div class="list">如果长尺寸纸张设为自定义纸张尺寸，且打印方向设为横向，则打印结果可能不正确，具体取决于所使用的设备。在这种情况下，请获取设备信息或设置 [设备设置] 页 &gt; [详细设置] &gt; [页面设置] 页 &gt; [长尺寸纸张打印时旋转数据] &gt; [开]。</div>
			<div class="list">在一台计算机上创建重叠格式文件时，您不能将新的格式文件保存在网络上的其他计算机中。</div>
			<div class="list">即使在 [传送图像的压缩处理] 中启用速度优先功能，需要的打印时间也可能比启用图像优先功能时更长，具体取决于使用的环境和打印数据。</div>
			<div class="list">该 TrueType 字体可能无法正确打印。若出现这种情况，应禁用[高级设置]对话框中的[使用打印机字体]设置，以确保与文档一并下载所有字体，而非仅使用打印机中原有的字体。在[质量]页中单击[高级设置]按钮，可显示[高级设置]对话框。</div>
			<div class="list">当[TrueType 模式]在[高级设置]对话框中被设置为[以 TrueType 下载]或[以位图图像下载]时，某些应用程序将无法正确打印旋转文本。通过在[质量]页中单击[高级设置]按钮，可以显示[高级设置]对话框。</div>
			<div class="list">当使用折叠功能时，可能存在这样的情况，即纸张未折叠或未按指定的方向折叠。</div>
			<div class="h6">对于 PS3 驱动程序</div>
			<div class="list">PostScript 不支持 ROP (Raster Operation)。因此，如果从应用程序指定了 ROP 处理，可能无法正确执行打印。</div>
			<div class="list">从 Illustrator CS 进行打印时，将显示一条消息，说明您指定的 PPD 文件无效。但是，单击消息框中的[确定]可继续打印。</div>
			<div class="list">使用 PostScript 打印机驱动程序时，某些应用程序直接向打印机发送 PostScript 命令。对于这些应用程序，您可能无法使用以下功能。</div>
			<div class="dash">从[基本设置]/[页面设置]页： [页面布局]、[手动缩放]</div>
			<div class="dash">从[页面设置]页： [水印]</div>
			<div class="dash">在[基本设置]、[完成方式]页中：[分套]、[装订 + 分套]（应用于未安装可选硬盘的打印机）</div>
			<div class="dash">从[纸张来源]页： [纸张选择]</div>
			<div class="dash">从[质量]页： [作为负片图像打印]、[作为镜像打印]</div>
			<div class="annotation">*** 当[设备设置]页中的[从应用程序 PS 输出]设置为[关]时，您可以使用上述功能。</div>
			<div class="h4"><a id="9024e78bf21c831c5b42c4569778fc7f"></a>使用打印处理器功能时的注意和限制</div>
			<div class="list">要使用打印处理器功能，请在 [设备设置] 页 &gt; [详细设置] &gt; [其他设置] 页中启用 [使用打印处理器执行处理]。但通常不需要使用打印处理器功能。</div>
			<div class="list">启用打印处理器时，请执行以下步骤检查打印处理器的名称是否设为“CnXP0PP”。如果没有设置为“CnXP0PP”，某些功能可能会受到限制。</div>
			<div class="text_level2">1. 在 Windows 打印机列表中选择要设置的设备 → 显示打印机属性屏幕。</div>
			<div class="text_level3">(1) 从开始菜单的 [设置] 中显示 [蓝牙和其他设备]（或 [设备]）。</div>
			<div class="text_level3">(2) 在 [打印机和扫描仪] 中，针对要设置的设备显示管理屏幕。</div>
			<div class="text_level3">要显示管理屏幕，请单击设备或单击相应设备的 [管理]。</div>
			<div class="text_level3">(3) 针对要设置的设备单击 [打印机属性]。</div>
			<div class="text_level2">2. 显示 [高级] 页 → 单击 [打印处理器]。</div>
			<div class="text_level2">3. 在显示的对话框中，检查所选的打印处理器。</div>
			<div class="list">如果启用打印处理器功能，则会应用以下限制。</div>
			<div class="dash">使用以下功能时，需要在打印前设置必要信息。</div>
			<div class="text_level3">[安全打印]/[部门 ID 管理]/[用户验证]/[高级保密打印]/[Access Management System]</div>
			<div class="dash">无法使用 [编辑预览] 功能。</div>
			<div class="dash">操作系统显示作业的屏幕中显示的页数可能与实际打印的页数不同。</div>
			<div class="dash">使用重叠打印时，无法在主机上选择格式文件。</div>
			<div class="dash">如果在打印前没有设置所需的信息，可能会在执行打印时取消打印作业。</div>
			<div class="dash">执行采用鞍式装订的手册打印时，如果超出设备可以进行鞍式装订的页数，将不会正确执行打印。请事先参阅设备的手册，以设置可以进行鞍式装订的页数。</div>
			<div class="h4"><a id="4a6bdb2eb8fe4b63a3a88976d8016900"></a>特定打印机型号的限制</div>
			<div class="list">使用iR-ADV C2020/2030或没有安装可选硬盘驱动器的iR-ADV C2020L/2030L时，无法使用下列功能：</div>
			<div class="dash">[安全打印]</div>
			<div class="dash">[优先打印]</div>
			<div class="list">在某些iR系列打印机的存储箱中保存打印数据时，打印样式（单面打印/双面打印）设置、纸张来源设置以及某些页面布局和缩放设置等将不会正确显示在打印机控制面板上。但是，打印将按已设置的执行。</div>
			<div class="list">在某些iR系列打印机中，如果无法在指定的装订位置进行装订，则文档输出时不会进行装订。</div>
			<div class="list">在某些iR系列打印机中，如果在打印机控制面板中指定保存在存储箱中的横向打印数据的装订位置，则实际装订位置可能在打印结果的垂直方向。</div>
			<div class="list">在某些iR系列打印机的存储箱中保存打印数据时，如果为[完成方式]选择[关]，设定的份数可能无法正确显示在打印机的控制面板上。在存储箱中保存数据时，为[完成方式]选择[关]之外的设置。</div>
			<div class="list">如果使用的是imageRUNNER ADVANCE C2020/C2020F/C2030/C2030F，只有选择[设备设置]选项卡 &gt; [详细设置] &gt; [完成方式] 选项卡 &gt; [分套 [通过设备]] &gt; [开]时才能使用[旋转]。</div>
			<div class="list">如果使用imageRUNNER ADVANCE C2020/C2020F/C2030/C2030F，自定义纸张尺寸的长条纸张和设备的安全打印功能将由 2.0或更高的功能版本支持。</div>
			<div class="list">使用iR-ADV C2020/2030、C2020i/2030i、C2020L/2030L或C2025时，如果在[质量]页的[高级设置]对话框中将[分辨率]设置为[1200 dpi] ，则会应用以下限制：</div>
			<div class="dash">仅可以将[装订位置]设置为 A4 页的长边。</div>
			<div class="dash">无法使用[完成方式]页中的[旋转]和[将不同尺寸/方向的纸张混合打印]设置。</div>
			<div class="dash">根据所使用的打印机的功能版本，可能无法使用[完成方式]页的[高级设置]对话框中的[复制集编号]设置。</div>
			<div class="list">如果使用的是imageRUNNER ADVANCE C350F，则不能使用[[旋转] 功能]。</div>
			<div class="list">如果使用的是imageRUNNER ADVANCE C351，确保在[设备设置]选项卡的[纸张来源]对话框中将[可选纸盒]设置为[纸盒x3]。</div>
			<div class="list">如果在日本使用imageRUNNER ADVANCE C7065/C7055，将无法在打印时选择[输出方法]中的[保留]。</div>
			<div class="list">如果使用imageRUNNER ADVANCE 8085/8095/8105且选装分页装订处理器-F1或手册分页装订处理器-F1，则无法输出明信片或自定义纸张尺寸（高度为148.0至181.9毫米）。</div>
			<div class="list">如果使用的是imagePRESS C6010/C6011或imagePRESS C7010VP/C7011VP，请手动设置[为 13x19 纸张指定裁边设置时启用的设置项目]。</div>
			<div class="list">在某些 imagePRESS 系列打印机中,如果在无法指定的位置指定了不使用装订针进行装订,则将会打印文档,而不进行不使用装订针的装订。</div>
			<div class="list">如果[配置文件]设为[基本配置]或[基本配置 (兼容)]，可能会在使用 Canon LBP 打印机进行打印时出现以下情形。</div>
			<div class="dash">即使设置了[安全打印]，也可以在没有[PIN]验证的情况下打印打印作业。</div>
			<div class="dash">打印作业和用户名称可能会在打印机的控制面板上显示为“--”。</div>
			<hr />
			<div class="text">本文档中出现的所有品牌名称和产品名称均为其各自所有者的注册商标或商标。</div>
			<div class="text">All other trademarks are the property of their respective owners.</div>
			<div class="h6">Json.NET</div>
			<div class="text"><span class="bold">The MIT License (MIT)</span></div>
			<div class="text">Copyright (c) 2007 James Newton-King</div>
			<div class="text">Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the &quot;Software&quot;), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:<br />The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.</div>
			<div class="text">THE SOFTWARE IS PROVIDED &quot;AS IS&quot;, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.</div>
			<hr />
			<div class="text_right">Copyright CANON INC. 2016</div>
		</div>
		<!--CONTENT_END-->
	</body>
</html>