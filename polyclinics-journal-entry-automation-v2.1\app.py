import os
from flask import Flask, render_template, jsonify

app = Flask(__name__)

# --- Configuration ---
# This is the directory where the files to be renamed are located.
DIRECTORY = r"C:\Users\<USER>\Downloads\KIB as of july 21"

FILES_TO_RENAME = {
    "Report 03D39308-3366-F011-8612-005056ABE6BF.pdf": "IRIS POLYCLINIC CLINIC - 304.pdf",
    "Report 081D2170-3466-F011-8612-005056ABE6BF.pdf": "Al Aseel International Polyclinic - 380.pdf",
    "Report 212E6767-3366-F011-8612-005056ABE6BF.pdf": "MED GRAY POLYCLINIC - 320.pdf",
    "Report 2876C90F-3566-F011-8612-005056ABE6BF.pdf": "MED WELL POLYCLINIC - 601.pdf",
    "Report 3A59C531-3066-F011-8612-005056ABE6BF.pdf": "FOURTH MEDICAL CENTER - 602.pdf",
    "Report 3C983CB3-3266-F011-8612-005056ABE6BF.pdf": "Medical Harbour Center - 240.pdf",
    "Report 4DC43717-3566-F011-8612-005056ABE6BF.pdf": "MED WELL POLYCLINIC - 610.pdf",
    "Report 5643B301-3366-F011-8612-005056ABE6BF.pdf": "IRIS POLYCLINIC CLINIC - 282.pdf",
    "Report 58317739-3266-F011-8612-005056ABE6BF.pdf": "Med Marine Medical Polyclinic - 207.pdf",
    "Report 66689E37-3466-F011-8612-005056ABE6BF.pdf": "Joya Medical Polyclinic - 266.pdf",
    "Report 74AF28DE-3466-F011-8612-005056ABE6BF.pdf": "Yarrow Polyclinic center - 773.pdf",
    "Report 96FA1D31-3466-F011-8612-005056ABE6BF.pdf": "Joya Medical Polyclinic - 258.pdf",
    "Report A2A38893-3366-F011-8612-005056ABE6BF.pdf": "FOURTH MEDICAL CENTER - 770.pdf",
    "Report C299808D-3366-F011-8612-005056ABE6BF.pdf": "FOURTH MEDICAL CENTER - 602.pdf",
    "Report C7ECC486-3266-F011-8612-005056ABE6BF.pdf": "Medical Harbour Center - 231.pdf",
    "Report D87628FB-3266-F011-8612-005056ABE6BF.pdf": "IRIS POLYCLINIC CLINIC - 645.pdf",
    "Report DA5F129A-3366-F011-8612-005056ABE6BF.pdf": "FOURTH MEDICAL CENTER - 789.pdf",
    "Report DF22ACDD-3366-F011-8612-005056ABE6BF.pdf": "ARAM MEDICAL POLYCLINIC - 577.pdf",
    "Report F590F1D5-3466-F011-8612-005056ABE6BF.pdf": "Yarrow Polyclinic center - 765.pdf"
}
# --- End Configuration ---

@app.route('/')
def index():
    """Renders the main page."""
    return render_template('index.html', directory=DIRECTORY)

@app.route('/rename-files', methods=['POST'])
def rename_files():
    """Handles the file renaming logic."""
    logs = []
    for old_name, new_name in FILES_TO_RENAME.items():
        old_path = os.path.join(DIRECTORY, old_name)
        new_path = os.path.join(DIRECTORY, new_name)
        try:
            os.rename(old_path, new_path)
            logs.append(f"SUCCESS: Renamed '{old_name}' to '{new_name}'")
        except FileNotFoundError:
            logs.append(f"SKIPPED: Source file not found: '{old_name}'")
        except FileExistsError:
            logs.append(f"SKIPPED: Destination file already exists: '{new_name}'")
        except Exception as e:
            logs.append(f"ERROR: Failed to rename '{old_name}': {e}")
    
    return jsonify({'logs': logs})

if __name__ == '__main__':
    app.run(debug=True, port=5001)