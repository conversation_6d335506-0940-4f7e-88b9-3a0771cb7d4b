
import React, { useMemo } from 'react';

interface PdfPreviewProps {
    file: File | null;
}

const PdfPreview: React.FC<PdfPreviewProps> = ({ file }) => {
    const objectUrl = useMemo(() => {
        if (file) {
            return URL.createObjectURL(file);
        }
        return null;
    }, [file]);

    if (!objectUrl) {
        return (
            <div className="flex items-center justify-center w-full h-full bg-gray-100 dark:bg-gray-800 rounded-lg">
                <p className="text-gray-500 dark:text-gray-400">PDF Preview will appear here</p>
            </div>
        );
    }

    return (
        <div className="w-full h-full">
            <iframe
                src={objectUrl}
                title="PDF Preview"
                className="w-full h-full border border-gray-300 dark:border-gray-700 rounded-lg"
            />
        </div>
    );
};

export default PdfPreview;
