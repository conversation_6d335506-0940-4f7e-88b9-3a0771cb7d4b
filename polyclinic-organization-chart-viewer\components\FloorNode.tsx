
import React from 'react';
import { Floor } from '../types';
import ClinicNode from './ClinicNode';

interface FloorNodeProps {
  floor: Floor;
}

const FloorNode: React.FC<FloorNodeProps> = ({ floor }) => {
  return (
    <div className="mb-2 last:mb-0">
      <div className="flex items-start">
        <div className="px-3 py-1.5 bg-green-100 hover:bg-green-200 text-green-800 border border-green-400 rounded-md shadow-sm font-medium text-xs transition-colors duration-150 ease-in-out min-w-[120px] text-center sm:text-left">
          {floor.name}
          {floor.occupant && <span className="block text-xs italic text-green-700">({floor.occupant})</span>}
        </div>
        {floor.clinics && floor.clinics.length > 0 && (
          <div className="pt-0.5"> {/* Align connecting line */}
            <div className="ml-3 pl-3 border-l-2 border-gray-300 space-y-1.5">
                {floor.clinics.map((clinic) => (
                <ClinicNode key={clinic.id} clinic={clinic} />
                ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FloorNode;
