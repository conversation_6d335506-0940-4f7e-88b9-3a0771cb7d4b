
import { GoogleGenAI, GenerateContentResponse } from "@google/genai";

const API_KEY = process.env.API_KEY;

if (!API_KEY) {
    throw new Error("API_KEY environment variable not set");
}

const ai = new GoogleGenAI({ apiKey: API_KEY });

const getExtractionPrompt = (content: string): string => `
You are an expert data extraction assistant. Your task is to analyze the provided text and convert it into a structured JSON object.
The text contains data meant for filling a PDF form. The data can be in various formats, such as 'key: value', key-value pairs on separate lines, or tabular data.
The text can be in English or Arabic.

The JSON output should be a single object where keys are the form field names and values are the corresponding data to be filled in.
Please ensure the keys and values in the JSON are correctly extracted, preserving the original language and content.

Example Input 1 (English):
First Name: John
Last Name: Doe
Email: <EMAIL>

Example Output 1:
{
  "First Name": "<PERSON>",
  "Last Name": "Do<PERSON>",
  "Email": "<EMAIL>"
}

Example Input 2 (Arabic):
الاسم الأول: جون
اسم العائلة: دو
البريد الإلكتروني: <EMAIL>

Example Output 2:
{
  "الاسم الأول": "جون",
  "اسم العائلة": "دو",
  "البريد الإلكتروني": "<EMAIL>"
}

Now, parse the following content and provide ONLY the JSON object as a response. Do not include any other text or explanations.

---
${content}
---
`;

export const extractDataFromText = async (fileContent: string): Promise<Record<string, string>> => {
    try {
        const prompt = getExtractionPrompt(fileContent);

        const response: GenerateContentResponse = await ai.models.generateContent({
            model: 'gemini-2.5-flash-preview-04-17',
            contents: prompt,
            config: {
                responseMimeType: 'application/json',
                temperature: 0,
            }
        });

        let jsonStr = response.text.trim();
        
        const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
        const match = jsonStr.match(fenceRegex);
        if (match && match[2]) {
            jsonStr = match[2].trim();
        }

        const parsedData = JSON.parse(jsonStr);
        return parsedData;

    } catch (error) {
        console.error("Error extracting data with Gemini:", error);
        if (error instanceof Error) {
             throw new Error(`Failed to process data with AI. Details: ${error.message}`);
        }
        throw new Error("An unknown error occurred during AI data extraction.");
    }
};
