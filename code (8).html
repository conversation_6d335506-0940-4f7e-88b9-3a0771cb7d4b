
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Attendance Delay Analyzer</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Simple fade-in animation */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .animate-fade-in {
            animation: fadeIn 0.5s ease-out forwards;
        }
        /* Simple spin animation */
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .animate-spin {
            animation: spin 1s linear infinite;
        }
    </style>
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "@google/genai": "https://esm.sh/@google/genai@^1.10.0"
  }
}
</script>
</head>
<body class="bg-gray-900 text-gray-200">
    <div id="root"></div>

    <script type="module">
        // --- STATE MANAGEMENT ---
        let state = {
            file: null,
            isLoading: false,
            error: null,
            attendanceData: null,
            totalDelay: 0,
            adjustment: '0',
        };

        // --- ICONS ---
        const UploadIcon = () => `<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" /></svg>`;
        const ProcessIcon = () => `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M4 4v5h5M4 12a8 8 0 108-8" /></svg>`;

        // --- CORE LOGIC ---
        const parseTimeToMinutes = (timeStr) => {
            if (!timeStr || !timeStr.includes(':')) return null;
            const [time, modifier] = timeStr.trim().split(' ');
            if (!modifier) return null;
            let [hours, minutes] = time.split(':').map(Number);
            if (isNaN(hours) || isNaN(minutes)) return null;

            if (modifier.toUpperCase() === 'PM' && hours < 12) hours += 12;
            if (modifier.toUpperCase() === 'AM' && hours === 12) hours = 0;
            
            return hours * 60 + minutes;
        };

        // --- RENDERING ---
        const root = document.getElementById('root');

        function render() {
            root.innerHTML = `
                <div class="min-h-screen p-4 sm:p-6 lg:p-8">
                    <div class="max-w-7xl mx-auto">
                        <header class="text-center mb-8">
                            <h1 class="text-4xl sm:text-5xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-teal-300 to-purple-400">
                                Attendance Delay Analyzer
                            </h1>
                            <p class="mt-2 text-lg text-gray-400">Upload your HTML attendance report to get started.</p>
                        </header>

                        <main>
                            <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 mb-8 max-w-2xl mx-auto shadow-xl">
                                <div class="flex flex-col sm:flex-row items-center gap-4">
                                    <label for="attendanceFile" class="flex-grow w-full cursor-pointer bg-gray-700 hover:bg-gray-600 text-white font-bold py-3 px-4 rounded-lg transition duration-200 flex items-center justify-center">
                                        ${UploadIcon()}
                                        <span>${state.file ? 'Change File' : 'Choose HTML File'}</span>
                                    </label>
                                    <input type="file" id="attendanceFile" accept=".html,.htm" class="hidden" />
                                    <button
                                        id="processBtn"
                                        ${!state.file ? 'disabled' : ''}
                                        class="w-full sm:w-auto bg-teal-500 hover:bg-teal-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-bold py-3 px-6 rounded-lg transition duration-200 flex items-center justify-center"
                                    >
                                        ${state.isLoading ? `<div class="animate-spin">${ProcessIcon()}</div>` : ''}
                                        ${state.isLoading ? 'Processing...' : 'Process File'}
                                    </button>
                                </div>
                                ${state.file ? `<p class="text-center text-sm text-gray-400 mt-3">Selected: ${state.file.name}</p>` : ''}
                            </div>

                            <div class="mt-8" id="results-area">
                                ${renderResultsArea()}
                            </div>
                        </main>
                        
                        <footer class="text-center mt-12 pt-8 border-t border-gray-800">
                            <p class="text-sm text-gray-500">This application was prepared by Haytham Soliman Abdou.</p>
                        </footer>
                    </div>
                </div>
            `;
            attachEventListeners();
        }

        function renderResultsArea() {
            if (state.isLoading) {
                return `<div class="text-center text-teal-400 flex justify-center items-center gap-2"><div class="animate-spin">${ProcessIcon()}</div><p>Analyzing your file...</p></div>`;
            }
            if (state.error) {
                return `<div class="bg-red-900/50 border border-red-700 text-red-300 p-4 rounded-lg max-w-2xl mx-auto text-center"><strong>Error:</strong> ${state.error}</div>`;
            }
            if (state.attendanceData) {
                return renderResultsDisplay();
            }
            return `
                <div class="text-center py-16 px-6 border-2 border-dashed border-gray-700 rounded-xl max-w-2xl mx-auto">
                    <h3 class="text-xl font-medium text-gray-400">Ready to Analyze</h3>
                    <p class="text-gray-500 mt-2">Your processed attendance report will appear here.</p>
                </div>
            `;
        }
        
        function renderResultsDisplay() {
            const newTotal = state.totalDelay + (parseInt(state.adjustment, 10) || 0);
            return `
            <div class="space-y-10 animate-fade-in">
              <div>
                <h2 class="text-3xl font-bold text-white mb-4 text-center">Attendance Report</h2>
                <div class="overflow-x-auto rounded-lg shadow-2xl bg-gray-800">
                  <table class="w-full text-sm text-left text-gray-300">
                    <thead class="text-xs text-teal-300 uppercase bg-gray-700">
                      <tr>
                        <th scope="col" class="px-6 py-3">Date</th>
                        <th scope="col" class="px-6 py-3">Day</th>
                        <th scope="col" class="px-6 py-3">Work Time</th>
                        <th scope="col" class="px-6 py-3">Entry Time</th>
                        <th scope="col" class="px-6 py-3 text-center text-red-300">Delay (min)</th>
                        <th scope="col" class="px-6 py-3">Exit Time</th>
                        <th scope="col" class="px-6 py-3">Work Hours</th>
                      </tr>
                    </thead>
                    <tbody>
                      ${state.attendanceData.map((record, index) => `
                        <tr class="border-b border-gray-700 ${index % 2 === 0 ? 'bg-gray-800' : 'bg-gray-800/50'} hover:bg-gray-700/50 transition-colors duration-200">
                          <td class="px-6 py-4">${record.date}</td>
                          <td class="px-6 py-4">${record.day}</td>
                          <td class="px-6 py-4">${record.workTime}</td>
                          <td class="px-6 py-4 font-medium ${record.delay > 0 ? 'text-red-400' : 'text-gray-300'}">${record.entryTime}</td>
                          <td class="px-6 py-4 text-center font-bold ${record.delay > 0 ? 'text-red-400 bg-red-900/40' : 'text-gray-400'}">
                            ${record.workTime ? record.delay : 'N/A'}
                          </td>
                          <td class="px-6 py-4">${record.exitTime}</td>
                          <td class="px-6 py-4">${record.workHours}</td>
                        </tr>
                      `).join('')}
                    </tbody>
                  </table>
                </div>
              </div>

              <div class="max-w-md mx-auto">
                <div class="bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-700">
                  <h3 class="text-xl font-semibold text-teal-400 mb-4">Delay Calculation</h3>
                  <div class="space-y-3">
                    <div class="flex justify-between items-center"><span class="text-gray-400">Total Calculated Delay:</span><strong class="text-2xl text-red-400">${state.totalDelay} minutes</strong></div>
                    <div class="flex justify-between items-center">
                      <label for="adjustmentInput" class="text-gray-400">Adjustment (min):</label>
                      <input type="number" id="adjustmentInput" value="${state.adjustment}" class="w-28 bg-gray-900 border border-gray-600 rounded-md p-2 text-white text-right focus:ring-2 focus:ring-teal-500 focus:outline-none" />
                    </div>
                    <div class="border-t border-gray-600 my-2"></div>
                    <div class="flex justify-between items-center"><span class="text-gray-400 font-bold">New Total Delay:</span><strong id="newTotalSpan" class="text-3xl text-green-400">${newTotal} minutes</strong></div>
                  </div>
                </div>
              </div>
            </div>`;
        }

        // --- EVENT HANDLING ---
        function attachEventListeners() {
            document.getElementById('attendanceFile')?.addEventListener('change', e => {
                const selectedFile = e.target.files?.[0];
                if (selectedFile) {
                    if (selectedFile.type === 'text/html' || selectedFile.name.endsWith('.htm') || selectedFile.name.endsWith('.html')) {
                        state.file = selectedFile;
                        state.error = null;
                        state.attendanceData = null;
                        state.adjustment = '0';
                    } else {
                        state.error = 'Invalid file type. Please upload an HTML file.';
                        state.file = null;
                    }
                    render();
                }
            });

            document.getElementById('processBtn')?.addEventListener('click', processFile);

            document.getElementById('adjustmentInput')?.addEventListener('input', e => {
                state.adjustment = e.target.value;
                const newTotal = state.totalDelay + (parseInt(state.adjustment, 10) || 0);
                const newTotalEl = document.getElementById('newTotalSpan');
                if (newTotalEl) {
                    newTotalEl.textContent = `${newTotal} minutes`;
                }
            });
        }

        function processFile() {
            if (!state.file) return;

            state.isLoading = true;
            state.error = null;
            state.attendanceData = null;
            render();

            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const htmlContent = e.target?.result;
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(htmlContent, 'text/html');
                    const tables = doc.querySelectorAll('table#result');

                    if (tables.length === 0) throw new Error('Could not find any valid attendance tables (with id="result") in the file.');

                    let calculatedTotalDelay = 0;
                    const processedRecords = [];
                    const gracePeriodMinutes = (8 * 60) + 30; // 8:30 AM

                    tables.forEach(table => {
                        const rows = Array.from(table.querySelectorAll('tbody > tr'));
                        rows.forEach(row => {
                            if (row.textContent?.includes('Totals')) return;
                            const cells = Array.from(row.querySelectorAll('td'));
                            if (cells.length < 6) return;

                            const entryTimeStr = cells[3]?.textContent?.trim() ?? '';
                            const workTimeStr = cells[2]?.textContent?.trim() ?? '';
                            
                            let delay = 0;
                            if (workTimeStr) {
                                const entryMinutes = parseTimeToMinutes(entryTimeStr);
                                if (entryMinutes !== null && entryMinutes > gracePeriodMinutes) {
                                    delay = entryMinutes - gracePeriodMinutes;
                                    calculatedTotalDelay += delay;
                                }
                            }

                            processedRecords.push({
                                date: cells[0]?.textContent?.trim() ?? '',
                                day: cells[1]?.textContent?.trim() ?? '',
                                workTime: workTimeStr,
                                entryTime: entryTimeStr,
                                exitTime: cells[4]?.textContent?.trim() ?? '',
                                workHours: cells[5]?.textContent?.trim() ?? '',
                                delay: workTimeStr ? delay : 0,
                            });
                        });
                    });
                    
                    state.attendanceData = processedRecords;
                    state.totalDelay = calculatedTotalDelay;
                    state.adjustment = '0';
                } catch (err) {
                    state.error = `Processing failed: ${err.message}`;
                    state.attendanceData = null;
                } finally {
                    state.isLoading = false;
                    render();
                }
            };
            reader.onerror = () => {
                state.error = 'Failed to read the file.';
                state.isLoading = false;
                render();
            }
            reader.readAsText(state.file);
        }

        // --- INITIAL RENDER ---
        render();

    </script>
</body>
</html>
