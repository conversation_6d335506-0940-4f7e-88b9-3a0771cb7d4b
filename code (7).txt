<change>
  <file>App.tsx</file>
  <description>Fixed a  critical error where the PDF worker script failed to load. I've updated the configuration to use the standard '.js' worker  file instead of the ES module ('.mjs')  version, which resolves the cross-origin fetch error. This ensures that  PDF files can be processed reliably.</description>
  <content>< ![CDATA[import React, { useState, useCallback, useRef  } from 'react';
import { JournalEntry, ExtractedData } from './types';
import { extractTransactions FromText } from './services/geminiService';
import { generateJournalEntries, convertToXLSX }  from './services/journalService';
import { SpinnerIcon, ProcessIcon, DownloadIcon } from './components/icons ';
import { OUTPUT_HEADER } from './constants';
import * as pdfjs from 'pdfjs-dist ';

// Set worker path to the non-module (.js)  version to avoid cross-origin module loading issues.
pdf js.GlobalWorkerOptions.workerSrc = `https://esm.sh/pdfjs-dist@4.5 .136/build/pdf.worker.js`;


const Header: React.FC = () => (
     <header className="w-full bg-gray-900 shadow-md p-4 flex flex-col sm :flex-row items-center justify-between">
        <div className="flex items-center">
             <ProcessIcon className="w-8 h-8 text-teal-400 mr-3" />
             <h1 className="text-2xl font-bold text-gray-200">Journal Entry Automation </h1>
        </div>
        <p className="text-sm text-gray-400 mt-2 sm :mt-0">Prepared by Haitham Soliman Abdou</p>
    </header>
);

 const ResultsTable: React.FC<{ entries: JournalEntry[] }> = ({ entries }) => (
    <div  className="bg-gray-900 p-4 rounded-lg shadow-inner mt-6">
         <h3 className="text-xl font-semibold text-gray-200 mb-4">Generated Journal Entries</h3>
         <div className="overflow-x-auto">
            <table className="min-w-full text- sm text-left text-gray-300">
                <thead