<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Polyclinic Directory</title>
    <style>
        /* General Styles */
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); min-height: 100vh; padding: 20px; color: #333; }
        .main-container { max-width: 1400px; margin: 0 auto; background: white; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); overflow: hidden; }
        .header { background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; padding: 30px; text-align: center; }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .header-actions { margin-top: 20px; }
        .action-btn { background-color: #ffc107; color: #212529; border: none; padding: 10px 20px; border-radius: 25px; font-size: 1rem; font-weight: bold; cursor: pointer; transition: all 0.3s ease; margin: 0 10px; }
        .action-btn:hover { background-color: #ffca2c; transform: translateY(-2px); box-shadow: 0 4px 10px rgba(0,0,0,0.2); }
        .action-btn.save-btn { background-color: #28a745; color: white; }
        .action-btn.save-btn:hover { background-color: #218838; }
        .tab-navigation { display: flex; background-color: #f8f9fa; border-bottom: 1px solid #dee2e6; }
        .tab-btn { padding: 15px 25px; cursor: pointer; border: none; background-color: transparent; font-size: 1rem; font-weight: 600; color: #495057; transition: all 0.3s ease; border-bottom: 3px solid transparent; }
        .tab-btn.active { color: #007bff; border-bottom: 3px solid #007bff; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        #cardView .controls { padding: 20px 30px; background: #f8f9fa; border-bottom: 1px solid #e9ecef; display: flex; flex-wrap: wrap; gap: 15px; align-items: center; }
        #cardView .search-container { flex: 1; min-width: 300px; position: relative; }
        #cardView #cardSearchInput { width: 100%; padding: 12px 45px 12px 15px; border: 2px solid #dee2e6; border-radius: 25px; font-size: 16px; }
        #cardView .search-icon { position: absolute; right: 15px; top: 50%; transform: translateY(-50%); color: #6c757d; }
        #cardView .filter-buttons { display: flex; gap: 10px; flex-wrap: wrap; }
        #cardView .filter-btn { padding: 8px 16px; border: 2px solid #007bff; background: white; color: #007bff; border-radius: 20px; cursor: pointer; font-size: 14px; }
        #cardView .filter-btn.active { background: #007bff; color: white; }
        #cardView .stats { display: flex; gap: 20px; margin-left: auto; }
        #cardView .stat-item { text-align: center; padding: 10px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); min-width: 80px; }
        #cardView .stat-number { font-size: 1.5rem; font-weight: bold; color: #007bff; }
        #cardView .stat-label { font-size: 0.8rem; color: #6c757d; margin-top: 2px; }
        #cardView .content { padding: 30px; }
        #cardView .clinic-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 25px; margin-top: 20px; }
        #cardView .clinic-card { background: white; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); overflow: hidden; border: 1px solid #e9ecef; }
        #cardView .clinic-header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 20px; cursor: pointer; position: relative; }
        #cardView .clinic-header h3 { font-size: 1.3rem; margin-bottom: 5px; }
        #cardView .clinic-meta { font-size: 0.9rem; opacity: 0.9; }
        #cardView .expand-icon { position: absolute; right: 20px; top: 50%; transform: translateY(-50%); font-size: 1.2rem; transition: transform 0.3s ease; }
        #cardView .clinic-card.expanded .expand-icon { transform: translateY(-50%) rotate(180deg); }
        #cardView .clinic-content { max-height: 0; overflow: hidden; transition: max-height 0.5s ease-out; }
        #cardView .clinic-card.expanded .clinic-content { max-height: 1000px; }
        #cardView .floor-section { padding: 15px 20px; border-bottom: 1px solid #f1f3f4; }
        #cardView .floor-section:last-child { border-bottom: none; }
        #cardView .floor-header { font-weight: bold; color: #495057; margin-bottom: 10px; font-size: 1.1rem; }
        #cardView .codes-container { display: flex; flex-wrap: wrap; gap: 10px; }
        #cardView .code-item { position: relative; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 10px 12px; flex: 1; min-width: 200px; cursor: pointer; }
        #cardView .code-label { font-weight: bold; color: #007bff; font-size: 0.9rem; }
        #cardView .doctor-name { color: #495057; font-size: 0.85rem; margin-top: 3px; line-height: 1.3; }
        #cardView .highlight { background-color: #fff3cd; padding: 2px 4px; border-radius: 3px; font-weight: bold; }
        #cardView .no-results, #tableView #noResultsMessage { text-align: center; padding: 40px; color: #6c757d; font-size: 1.1rem; display: none; }
        #cardView .result-count { margin: 20px 0; padding: 10px 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; color: #155724; font-weight: bold; }
        #cardView .card-actions, #cardView .item-actions { display: none; position: absolute; gap: 15px; }
        .edit-mode #cardView .card-actions, .edit-mode #cardView .item-actions { display: flex; }
        #cardView .card-actions { top: 20px; right: 50px; }
        #cardView .item-actions { top: 10px; right: 10px; gap: 8px; }
        #cardView .edit-icon, #cardView .delete-icon { cursor: pointer; font-size: 1.1rem; transition: transform 0.2s; }
        #cardView .edit-icon:hover, #cardView .delete-icon:hover { transform: scale(1.2); }
        #tableView .controls { padding: 20px 30px; background: #f8f9fa; border-bottom: 1px solid #e9ecef; display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; align-items: end; }
        #tableView .control-group { display: flex; flex-direction: column; }
        #tableView .control-group label { margin-bottom: 8px; font-weight: bold; color: #495057; font-size: 0.9rem; }
        #tableView .control-group input, #tableView .control-group select { width: 100%; padding: 12px 15px; border: 2px solid #dee2e6; border-radius: 8px; font-size: 16px; }
        #tableView .stats { padding: 15px 30px; background: #e9ecef; border-bottom: 1px solid #dee2e6; text-align: right; font-weight: bold; color: #155724; }
        #tableView .table-container { overflow-x: auto; }
        #tableView table { width: 100%; border-collapse: collapse; font-size: 0.95rem; }
        #tableView th, #tableView td { padding: 12px 15px; border: 1px solid #dee2e6; text-align: left; vertical-align: middle; }
        #tableView thead { background-color: #007bff; color: white; }
        #tableView tbody tr:nth-child(even) { background-color: #f8f9fa; }
        #tableView tbody tr:hover { background-color: #e9ecef; }
        #tableView .actions-cell { text-align: center; display: none; }
        .edit-mode #tableView .actions-cell { display: table-cell; }
        #tableView .edit-btn, #tableView .delete-btn { border: none; background: none; cursor: pointer; font-size: 0.9rem; padding: 5px 10px; border-radius: 5px; }
        #tableView .edit-btn { color: #007bff; }
        #tableView .delete-btn { color: #dc3545; }
        .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.6); }
        .modal-content { background-color: #fefefe; margin: 10% auto; padding: 30px; border-radius: 12px; width: 90%; max-width: 600px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); position: relative; }
        .close { color: #aaa; position: absolute; top: 15px; right: 25px; font-size: 28px; font-weight: bold; cursor: pointer; }
        #editForm .form-group { margin-bottom: 15px; }
        #editForm label { display: block; margin-bottom: 5px; font-weight: bold; }
        #editForm input, #editForm select { width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 4px; }
        #editForm button { background-color: #28a745; color: white; padding: 12px 20px; border: none; border-radius: 4px; cursor: pointer; font-size: 1rem; width: 100%; }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="header">
            <h1>🏥 Polyclinic Directory</h1>
            <div class="header-actions">
                <button id="editModeBtn" class="action-btn">✏️ Enter Edit Mode</button>
                <button id="saveAndExitBtn" class="action-btn save-btn" style="display: none;">💾 Save & Exit Edit Mode</button>
                <button id="addClinicBtn" class="action-btn" style="display: none;">➕ Add New Entry</button>
                <button id="exportHtmlBtn" class="action-btn" style="display: none;">📄 Export Updated HTML</button>
            </div>
        </div>
        <div class="tab-navigation">
            <button class="tab-btn active" data-tab="cardView">Card View</button>
            <button class="tab-btn" data-tab="tableView">Table View</button>
        </div>
        <div id="cardView" class="tab-content active">
            <div class="controls">
                <div class="search-container"><input type="text" id="cardSearchInput" placeholder="Search..."><span class="search-icon">🔍</span></div>
                <div class="filter-buttons">
                    <button class="filter-btn active" data-filter="all">All</button>
                    <button class="filter-btn" data-filter="doctors">Doctors</button>
                    <button class="filter-btn" data-filter="companies">Companies</button>
                </div>
                <div class="stats">
                    <div class="stat-item"><div class="stat-number" id="totalClinics">0</div><div class="stat-label">Clinics</div></div>
                    <div class="stat-item"><div class="stat-number" id="totalFloors">0</div><div class="stat-label">Floors</div></div>
                    <div class="stat-item"><div class="stat-number" id="totalEntries">0</div><div class="stat-label">Entries</div></div>
                </div>
            </div>
            <div class="content">
                <div id="resultCount" class="result-count" style="display: none;"></div>
                <div id="clinicGrid" class="clinic-grid"></div>
                <div id="noResults" class="no-results"><h3>No results found</h3><p>Try adjusting your search</p></div>
            </div>
        </div>
        <div id="tableView" class="tab-content">
            <div class="controls">
                <div class="control-group"><label for="tableSearchInput">Search All</label><input type="text" id="tableSearchInput" placeholder="e.g., Al Aseel, Dr. Ali..."></div>
                <div class="control-group"><label for="clinicFilter">Filter by Clinic</label><select id="clinicFilter"></select></div>
                <div class="control-group"><label for="floorFilter">Filter by Floor</label><select id="floorFilter"></select></div>
                <div class="control-group"><label for="typeFilter">Filter by Type</label><select id="typeFilter"></select></div>
            </div>
            <div class="stats"><span id="tableResultCount"></span></div>
            <div class="content">
                <div class="table-container">
                    <table>
                        <thead><tr><th>Clinic Name</th><th>Activities</th><th>Floor</th><th>Clinic Code</th><th>Doctor/Company</th><th>Type</th><th>Actions</th></tr></thead>
                        <tbody id="clinicTableBody"></tbody>
                    </table>
                </div>
                <div id="noResultsMessage"><h3>No results found</h3><p>Try adjusting criteria.</p></div>
            </div>
        </div>
    </div>
    <div id="detailModal" class="modal"><div class="modal-content"><span class="close">&times;</span><div id="modalContent"></div></div></div>
    <div id="editModal" class="modal">
        <div class="modal-content">
            <span class="close" id="editModalClose">&times;</span>
            <h2 id="editModalTitle">Add/Edit</h2>
            <form id="editForm">
                <input type="hidden" id="edit-type"><input type="hidden" id="original-clinic-name"><input type="hidden" id="original-floor"><input type="hidden" id="original-code">
                <div class="form-group"><label for="clinicNameInput">Clinic Name</label><input type="text" id="clinicNameInput" required><select id="clinicNameSelect"></select></div>
                <div class="form-group" id="activity-form-group"><label for="activityInput">Activity Code</label><input type="text" id="activityInput"></div>
                <div class="form-group"><label for="floorInput">Floor</label><input type="text" id="floorInput" required></div>
                <div class="form-group"><label for="codeInput">Clinic Code</label><input type="text" id="codeInput" required></div>
                <div class="form-group"><label for="doctorInput">Doctor/Company</label><input type="text" id="doctorInput" required></div>
                <button type="submit" id="saveChangesBtn">Save Changes</button>
            </form>
        </div>
    </div>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        let clinicData = [
            { "name": "IRIS POLYCLINIC", "activity": 1193, "floors": [ { "floor": 2, "codes": [{"code": "A", "doctor": "Abdullah Abdul Aziz Al Hajri"}, {"code": "B", "doctor": "Smart Health Co."}, {"code": "C", "doctor": "Walid Hamad Ashwi Raheel"}] }, { "floor": 3, "codes": [{"code": "A+B", "doctor": "Amr Nabil Qutb"}, {"code": "C", "doctor": "Oxcana Bogdanovic"}] }, { "floor": 4, "codes": [{"code": "A", "doctor": "Hassaan A Jaber & Obaid Metni"}, {"code": "B", "doctor": "Mohamed Youssef Al Eissa"}, {"code": "C", "doctor": "Dr. Mariam Abed Ali Al-Turki"}] } ] },
            { "name": "ALASEEL POLYCLINIC", "activity": 1194, "floors": [ { "floor": 7, "codes": [{"code": "C", "doctor": "Daniel Alain"}] }, { "floor": 8, "codes": [{"code": "A", "doctor": "Abdullah Abdul Rahman Al Hassan"}, {"code": "B", "doctor": "Hossam Mohamed El Badri"}, {"code": "C", "doctor": "Mustafa Samy Al Kaddousy"}] }, { "floor": 9, "codes": [{"code": "A", "doctor": "Nasser Faisal Al Mutairy"}, {"code": "B", "doctor": "Andro George Mikha'eel"}, {"code": "C", "doctor": "Dr. Noor Aladdin Alomar & Dr. Aya Samara"}] }, { "floor": 10, "codes": [{"code": "A+B", "doctor": "Dr. Ali Al-Mukaimi & Nisreen Baeij"}, {"code": "C", "doctor": "Dr. Ali Al-Mukaimi"}] } ] },
            { "name": "YARROW POLYCLINIC", "activity": 1198, "floors": [ { "floor": 11, "codes": [{"code": "A", "doctor": "Dr. Ahmed Abdulsamad Yehya Jassem"}, {"code": "B", "doctor": "Dr. Osamah J M Albaker"}, {"code": "C", "doctor": "Hossam Mohamed El Badri"}] }, { "floor": 12, "codes": [{"code": "A", "doctor": "Ahmed Mohamed Ahmed Ibrahim"}, {"code": "B", "doctor": "Sale Abdul Ghaffar Ma'arafie"}, {"code": "C", "doctor": "Adnan Ibrahim Ibrahim"}] } ] },
            { "name": "FOURTH MEDICAL CENTER", "activity": 1195, "floors": [ {"floor": 1, "codes": [{"code": "A", "doctor": "Salam Attar"}]}, {"floor": "2-3", "codes": [{"code": "A", "doctor": "One Day to Manage Projects Co."}]}, {"floor": "4-5", "codes": [{"code": "A", "doctor": "Athba Co."}]}, {"floor": 6, "codes": [{"code": "A", "doctor": "Health Care Co."}]}, {"floor": 7, "codes": [{"code": "A", "doctor": "Abdul Aziz Fahad Al Mezeiny"}]}, {"floor": 13, "codes": [{"code": "A", "doctor": "Revolution Medical Co."}]}, {"floor": 14, "codes": [{"code": "A", "doctor": "Dr. Farouk Alzoubani"}]}, {"floor": 15, "codes": [{"code": "A", "doctor": "Assem Drwesh Mostafa Abdulnabi"}]}, {"floor": 16, "codes": [{"code": "A", "doctor": "One Day to Manage Projects Co."}]}, {"floor": 17, "codes": [{"code": "A", "doctor": "Dr. Abdullah Sadad Sabri Al-Ozairi"}]}, {"floor": "18-19", "codes": [{"code": "A", "doctor": "Gulf Care Co."}]} ] },
            { "name": "MEDICAL HARBOUR CENTER", "activity": 1196, "floors": [ {"floor": 1, "codes": [{"code": "C", "doctor": "Moaeyed Zaid Al Saq'abi"}]}, {"floor": 2, "codes": [{"code": "C", "doctor": "Mohamed Abdul Majid Hassan"}]}, {"floor": 3, "codes": [{"code": "C", "doctor": "Salah El Din Mohamed El Sherbini"}]}, {"floor": 4, "codes": [{"code": "C", "doctor": "Youssef Al Khleify/Rawan Al Khatib"}]}, {"floor": 8, "codes": [{"code": "C", "doctor": "Amir Eissa Attia Killa"}]}, {"floor": 9, "codes": [{"code": "C", "doctor": "Dr. Hesham Mohamed Yassin Ibrahim"}]}, {"floor": 10, "codes": [{"code": "C", "doctor": "Med Vision Medical Services"}]}, {"floor": 11, "codes": [{"code": "C", "doctor": "Fatmah Mohamed Badawy"}]}, {"floor": 12, "codes": [{"code": "C", "doctor": "Othman Youssef Al Mas'oud"}]}, {"floor": 13, "codes": [{"code": "C", "doctor": "Btissam Ibn Kiran"}]}, {"floor": 14, "codes": [{"code": "C", "doctor": "Misha'al Al Dahsh"}]}, {"floor": 15, "codes": [{"code": "C", "doctor": "Amal Al Shaiji / Faisal Al Terkeet"}]}, {"floor": 16, "codes": [{"code": "C", "doctor": "Signofa Co./Ahmed Eissa"}]}, {"floor": 17, "codes": [{"code": "C", "doctor": "Waleed Hamid Raheel"}]}, {"floor": 18, "codes": [{"code": "C", "doctor": "Eman Ghorab"}]}, {"floor": 19, "codes": [{"code": "C", "doctor": "Emad Morkos/Ahmed Youssef"}]}, {"floor": 20, "codes": [{"code": "C", "doctor": "Mohamed Al Kolk"}]}, {"floor": 21, "codes": [{"code": "C", "doctor": "Youssef Al Khleify"}]} ] },
            { "name": "MED MARINE POLYCLINIC", "activity": 1191, "floors": [ {"floor": 5, "codes": [{"code": "A", "doctor": "Fatima Ne'ma Al Awadhi"}, {"code": "B", "doctor": "Mohamed As'ad Eid/Wael Bezrah"}]}, {"floor": 6, "codes": [{"code": "A+B", "doctor": "Mohamed Youssef Al Sabty"}]}, {"floor": 7, "codes": [{"code": "A+B", "doctor": "Mostafa Mohamed Tomsu"}]} ] },
            { "name": "JOYA POLYCLINIC", "activity": 1197, "floors": [ {"floor": 8, "codes": [{"code": "A", "doctor": "Ihab Mohamed Younes Omar"}, {"code": "B", "doctor": "Huda Mahmoud Selim"}]}, {"floor": 9, "codes": [{"code": "A+B", "doctor": "Berlin Co./Mohamed Riyadh"}]}, {"floor": 10, "codes": [{"code": "A+B", "doctor": "Shehta Mostafa Ze'reb"}]} ] },
            { "name": "Med Gray", "activity": 1192, "floors": [ {"floor": 5, "codes": [{"code": "A", "doctor": "Dr. Amr Nabil Qutb"}]}, {"floor": "6-7", "codes": [{"code": "A", "doctor": "Dr. Shehta Mostafa Zurub"}]} ] },
            { "name": "ARAM MEDICAL POLYCLINIC", "activity": 1199, "floors": [ {"floor": 2, "codes": [{"code": "A+B", "doctor": "Dalia/Mina/Osama/Mahmoud"}]}, {"floor": 3, "codes": [{"code": "A+B", "doctor": "Ayman/Islam"}]}, {"floor": 4, "codes": [{"code": "A", "doctor": "Mohamed Al Sayyad"}, {"code": "B", "doctor": "Mohamed Al Sayyad"}]}, {"floor": 5, "codes": [{"code": "A", "doctor": "Bishoy/Mina/Zaher"}, {"code": "B", "doctor": "Nasser/Mohamed"}]}, {"floor": 6, "codes": [{"code": "A", "doctor": "Munira/Anjoud"}, {"code": "B", "doctor": "Munira/Anjoud"}]}, {"floor": 7, "codes": [{"code": "A+B", "doctor": "Sondos Ghaneim"}]}, {"floor": 8, "codes": [{"code": "A", "doctor": "Marina/Mary/Mariana"}, {"code": "B", "doctor": "Dr. Mohammed Salem"}]}, {"floor": 9, "codes": [{"code": "A", "doctor": "Rwda Ahmed"}, {"code": "B", "doctor": "Marawan Essam"}]} ] },
            { "name": "TryCare", "activity": 1211, "floors": [] }
        ];

        const dom = {
            tabs: document.querySelectorAll('.tab-btn'),
            tabContents: document.querySelectorAll('.tab-content'),
            cardSearchInput: document.getElementById('cardSearchInput'),
            clinicGrid: document.getElementById('clinicGrid'),
            resultCountEl: document.getElementById('resultCount'),
            noResultsEl: document.getElementById('noResults'),
            detailModal: document.getElementById('detailModal'),
            modalContent: document.getElementById('modalContent'),
            tableSearchInput: document.getElementById('tableSearchInput'),
            clinicFilter: document.getElementById('clinicFilter'),
            floorFilter: document.getElementById('floorFilter'),
            typeFilter: document.getElementById('typeFilter'),
            tableBody: document.getElementById('clinicTableBody'),
            tableResultCount: document.getElementById('tableResultCount'),
            noResultsMessage: document.getElementById('noResultsMessage'),
            editModal: document.getElementById('editModal'),
            editModalClose: document.getElementById('editModalClose'),
            editForm: document.getElementById('editForm'),
            addClinicBtn: document.getElementById('addClinicBtn'),
            exportHtmlBtn: document.getElementById('exportHtmlBtn'),
            editModeBtn: document.getElementById('editModeBtn'),
            saveAndExitBtn: document.getElementById('saveAndExitBtn')
        };

        let currentFilter = 'all';
        let isDataDirty = false;
        let inEditMode = false;
        let flatData = [];

        const debounce = (func, wait) => { let timeout; return (...args) => { clearTimeout(timeout); timeout = setTimeout(() => func.apply(this, args), wait); }; };
        const highlightText = (text, search) => { if (!search || !text) return text; const regex = new RegExp(`(${search.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')})`, 'gi'); return text.replace(regex, '<span class="highlight">$1</span>'); };
        
        const updateStats = () => {
            document.getElementById('totalClinics').textContent = clinicData.length;
            document.getElementById('totalFloors').textContent = clinicData.reduce((s, c) => s + c.floors.length, 0);
            document.getElementById('totalEntries').textContent = clinicData.reduce((s, c) => s + c.floors.reduce((fs, f) => fs + f.codes.length, 0), 0);
        };

        const generateFlatData = () => {
            flatData = clinicData.flatMap(c => {
                if (c.floors.length === 0) return [{ clinicName: c.name, activity: c.activity, floor: 'N/A', clinicCode: 'N/A', doctorCompany: 'N/A', type: 'N/A' }];
                return c.floors.flatMap(f => f.codes.map(code => ({ clinicName: c.name, activity: c.activity, floor: String(f.floor), clinicCode: code.code, doctorCompany: code.doctor, type: (code.doctor.toLowerCase().includes('co.') || code.doctor.toLowerCase().includes('services')) ? 'Company' : 'Doctor' })));
            });
        };

        const populateFilters = () => {
            const createOptions = (select, options, defaultLabel) => {
                const currentValue = select.value;
                select.innerHTML = `<option value="">${defaultLabel}</option>`;
                [...new Set(options)].sort((a, b) => a.localeCompare(b, undefined, {numeric: true})).forEach(opt => { select.innerHTML += `<option value="${opt}">${opt}</option>`; });
                select.value = currentValue;
            };
            createOptions(dom.clinicFilter, flatData.map(i => i.clinicName), 'All Clinics');
            createOptions(dom.floorFilter, flatData.map(i => i.floor), 'All Floors');
            createOptions(dom.typeFilter, flatData.map(i => i.type), 'All Types');
        };

        const renderTable = () => {
            const searchTerm = dom.tableSearchInput.value.toLowerCase();
            const filteredData = flatData.filter(item => 
                (!dom.clinicFilter.value || item.clinicName === dom.clinicFilter.value) &&
                (!dom.floorFilter.value || item.floor === dom.floorFilter.value) &&
                (!dom.typeFilter.value || item.type === dom.typeFilter.value) &&
                (Object.values(item).join(' ').toLowerCase().includes(searchTerm))
            );
            dom.tableBody.innerHTML = '';
            dom.tableResultCount.textContent = `Showing ${filteredData.length} of ${flatData.length} entries`;
            dom.noResultsMessage.style.display = filteredData.length === 0 ? 'block' : 'none';
            filteredData.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${highlightText(item.clinicName, searchTerm)}</td>
                    <td>${highlightText(String(item.activity || ''), searchTerm)}</td>
                    <td>${highlightText(item.floor, searchTerm)}</td>
                    <td>${highlightText(item.clinicCode, searchTerm)}</td>
                    <td>${highlightText(item.doctorCompany, searchTerm)}</td>
                    <td>${highlightText(item.type, searchTerm)}</td>
                    <td class="actions-cell">
                        <button class="edit-btn" data-type="entry" data-clinic-name="${item.clinicName}" data-floor="${item.floor}" data-code="${item.clinicCode}">✏️ Edit</button>
                        <button class="delete-btn" data-type="entry" data-clinic-name="${item.clinicName}" data-floor="${item.floor}" data-code="${item.clinicCode}">🗑️ Delete</button>
                    </td>`;
                dom.tableBody.appendChild(row);
            });
        };

        const renderClinics = () => {
            const searchLower = dom.cardSearchInput.value.toLowerCase().trim();
            const filteredClinics = clinicData.filter(clinic => {
                const searchMatch = !searchLower || [clinic.name, clinic.activity].join(' ').toLowerCase().includes(searchLower) ||
                    clinic.floors.some(f => String(f.floor).toLowerCase().includes(searchLower) || f.codes.some(c => [c.code, c.doctor].join(' ').toLowerCase().includes(searchLower)));
                const doctorMatch = clinic.floors.some(f => f.codes.some(c => {
                    const isCompany = c.doctor.toLowerCase().includes('co.') || c.doctor.toLowerCase().includes('services');
                    return currentFilter === 'all' || (currentFilter === 'doctors' && !isCompany) || (currentFilter === 'companies' && isCompany);
                }));
                return searchMatch && (doctorMatch || clinic.floors.length === 0);
            });

            dom.clinicGrid.innerHTML = filteredClinics.map(clinic => {
                const totalEntries = clinic.floors.reduce((sum, floor) => sum + floor.codes.length, 0);
                return `
                <div class="clinic-card" data-clinic="${clinic.name}">
                    <div class="clinic-header">
                        <h3>${highlightText(clinic.name, searchLower)}</h3>
                        <div class="clinic-meta">Activity: ${clinic.activity || 'N/A'} • ${clinic.floors.length} floors • ${totalEntries} entries</div>
                        <div class="card-actions">
                            <span class="edit-icon" data-type="clinic" data-clinic-name="${clinic.name}">✏️</span>
                            <span class="delete-icon" data-type="clinic" data-clinic-name="${clinic.name}">🗑️</span>
                        </div>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="clinic-content">
                        ${clinic.floors.map(floor => `
                            <div class="floor-section">
                                <div class="floor-header">Floor ${highlightText(String(floor.floor), searchLower)}</div>
                                <div class="codes-container">
                                    ${floor.codes.map(code => `
                                        <div class="code-item" data-clinic="${clinic.name}" data-floor="${floor.floor}" data-code="${code.code}" data-doctor="${code.doctor}">
                                            <div class="code-label">Clinic ${highlightText(code.code, searchLower)}</div>
                                            <div class="doctor-name">${highlightText(code.doctor, searchLower)}</div>
                                            <div class="item-actions">
                                                <span class="edit-icon" data-type="entry" data-clinic-name="${clinic.name}" data-floor="${floor.floor}" data-code="${code.code}">✏️</span>
                                                <span class="delete-icon" data-type="entry" data-clinic-name="${clinic.name}" data-floor="${floor.floor}" data-code="${code.code}">🗑️</span>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>`;
            }).join('');
        };

        const renderAll = () => { generateFlatData(); renderClinics(); renderTable(); updateStats(); populateFilters(); };
        
        const toggleEditMode = () => {
            inEditMode = !inEditMode;
            document.body.classList.toggle('edit-mode');
            dom.editModeBtn.style.display = inEditMode ? 'none' : 'inline-block';
            dom.saveAndExitBtn.style.display = inEditMode ? 'inline-block' : 'none';
            dom.addClinicBtn.style.display = inEditMode ? 'inline-block' : 'none';
            dom.exportHtmlBtn.style.display = !inEditMode && isDataDirty ? 'inline-block' : 'none';
        };

        const openEditModal = (type, data = {}) => {
            dom.editForm.reset();
            const title = document.getElementById('editModalTitle');
            const clinicNameInput = document.getElementById('clinicNameInput');
            const clinicNameSelect = document.getElementById('clinicNameSelect');
            const activityFormGroup = document.getElementById('activity-form-group');
            const floorFormGroup = document.getElementById('floorInput').parentElement;
            const codeFormGroup = document.getElementById('codeInput').parentElement;
            const doctorFormGroup = document.getElementById('doctorInput').parentElement;

            document.getElementById('edit-type').value = type;
            [activityFormGroup, floorFormGroup, codeFormGroup, doctorFormGroup].forEach(fg => fg.style.display = 'block');
            
            if (type === 'entry') {
                title.textContent = 'Edit Entry';
                clinicNameInput.style.display = 'none';
                clinicNameSelect.style.display = 'block';
                activityFormGroup.style.display = 'none';
                clinicNameSelect.innerHTML = [...new Set(clinicData.map(c => c.name))].sort().map(opt => `<option value="${opt}">${opt}</option>`).join('');
                const entry = clinicData.flatMap(c => c.floors.flatMap(f => f.codes.map(code => ({...code, clinicName: c.name, floor: f.floor})))).find(e => e.clinicName === data.clinicName && String(e.floor) === data.floor && e.code === data.code);
                if(entry) {
                    clinicNameSelect.value = entry.clinicName;
                    ['floorInput', 'codeInput', 'doctorInput'].forEach(id => document.getElementById(id).value = entry[id.replace('Input', '')]);
                    ['original-clinic-name', 'original-floor', 'original-code'].forEach(id => document.getElementById(id).value = entry[id.replace('original-', '').replace('-name', 'Name')]);
                }
            } else if (type === 'clinic') {
                title.textContent = 'Edit Clinic';
                clinicNameInput.style.display = 'block';
                clinicNameSelect.style.display = 'none';
                const clinic = clinicData.find(c => c.name === data.clinicName);
                if(clinic) {
                    clinicNameInput.value = clinic.name;
                    document.getElementById('activityInput').value = clinic.activity || '';
                }
                [floorFormGroup, codeFormGroup, doctorFormGroup].forEach(fg => fg.style.display = 'none');
                document.getElementById('original-clinic-name').value = data.clinicName;
            } else { // Add new
                title.textContent = 'Add New Entry';
                clinicNameInput.style.display = 'block';
                clinicNameSelect.style.display = 'none';
            }
            dom.editModal.style.display = 'block';
        };

        const handleDelete = (type, data) => {
            if (!confirm(`Are you sure you want to delete this ${type}?`)) return;
            if (type === 'entry') {
                const clinic = clinicData.find(c => c.name === data.clinicName);
                if(clinic) {
                    const floor = clinic.floors.find(f => String(f.floor) === data.floor);
                    if(floor) {
                        floor.codes = floor.codes.filter(c => c.code !== data.code);
                        if (floor.codes.length === 0) clinic.floors = clinic.floors.filter(f => String(f.floor) !== data.floor);
                    }
                }
            } else if (type === 'clinic') {
                clinicData = clinicData.filter(c => c.name !== data.clinicName);
            }
            isDataDirty = true;
            renderAll();
        };

        dom.editForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const type = document.getElementById('edit-type').value;
            const clinicName = document.getElementById('clinicNameInput').value || document.getElementById('clinicNameSelect').value;
            const activity = document.getElementById('activityInput').value;
            const floor = document.getElementById('floorInput').value;
            const code = document.getElementById('codeInput').value;
            const doctor = document.getElementById('doctorInput').value;
            const originalClinicName = document.getElementById('original-clinic-name').value;
            const originalFloor = document.getElementById('original-floor').value;
            const originalCode = document.getElementById('original-code').value;

            if (type === 'entry') {
                if(originalClinicName && originalFloor && originalCode) {
                    const oldClinic = clinicData.find(c => c.name === originalClinicName);
                    if(oldClinic) {
                        const oldFloor = oldClinic.floors.find(f => String(f.floor) === originalFloor);
                        if(oldFloor) {
                            oldFloor.codes = oldFloor.codes.filter(c => c.code !== originalCode);
                            if (oldFloor.codes.length === 0) oldClinic.floors = oldClinic.floors.filter(f => String(f.floor) !== originalFloor);
                        }
                    }
                }
                let targetClinic = clinicData.find(c => c.name === clinicName);
                if (!targetClinic) {
                    targetClinic = { name: clinicName, activity: '', floors: [] };
                    clinicData.push(targetClinic);
                }
                let targetFloor = targetClinic.floors.find(f => String(f.floor) === floor);
                if (!targetFloor) {
                    targetFloor = { floor: isNaN(parseInt(floor)) ? floor : parseInt(floor), codes: [] };
                    targetClinic.floors.push(targetFloor);
                }
                targetFloor.codes.push({ code, doctor });
            } else if (type === 'clinic') {
                const clinic = clinicData.find(c => c.name === originalClinicName);
                if(clinic) { clinic.name = clinicName; clinic.activity = activity; }
            } else { // Add new
                let targetClinic = clinicData.find(c => c.name === clinicName);
                if (!targetClinic) {
                    targetClinic = { name: clinicName, activity: activity, floors: [] };
                    clinicData.push(targetClinic);
                } else if(activity) {
                    targetClinic.activity = activity;
                }
                if(floor && code && doctor) {
                    let targetFloor = targetClinic.floors.find(f => String(f.floor) === floor);
                    if (!targetFloor) {
                        targetFloor = { floor: isNaN(parseInt(floor)) ? floor : parseInt(floor), codes: [] };
                        targetClinic.floors.push(targetFloor);
                    }
                    targetFloor.codes.push({ code, doctor });
                }
            }
            isDataDirty = true;
            dom.editModal.style.display = 'none';
            renderAll();
        });

        const exportHTML = () => {
            if (!confirm("This will generate and download a new HTML file with the current data. Continue?")) return;
            const newHtml = `<!DOCTYPE html>\n<html lang="en">\n<head>${document.head.innerHTML}</head>\n<body>${document.body.innerHTML}</body>\n</html>`
                .replace(/<script.*?>[\s\S]*?<\/script>/, `<script>
        document.addEventListener('DOMContentLoaded', function() {
            let clinicData = ${JSON.stringify(clinicData, null, 4)};
            ${document.querySelector('script').innerHTML.substring(document.querySelector('script').innerHTML.indexOf('const dom = {'))}
        });
    <\/script>`);
            const blob = new Blob([newHtml], { type: 'text/html' });
            const a = document.createElement('a');
            a.href = URL.createObjectURL(blob);
            a.download = 'polyclinic_directory_updated.html';
            a.click();
            URL.revokeObjectURL(a.href);
        };

        // Event Listeners
        dom.tabs.forEach(tab => tab.addEventListener('click', () => {
            dom.tabs.forEach(t => t.classList.remove('active'));
            tab.classList.add('active');
            dom.tabContents.forEach(c => c.classList.remove('active'));
            document.getElementById(tab.dataset.tab).classList.add('active');
        }));
        dom.cardSearchInput.addEventListener('input', debounce(renderClinics, 300));
        document.querySelectorAll('#cardView .filter-btn').forEach(btn => btn.addEventListener('click', (e) => {
            currentFilter = e.target.dataset.filter;
            document.querySelectorAll('#cardView .filter-btn').forEach(b => b.classList.remove('active'));
            e.target.classList.add('active');
            renderClinics();
        }));
        
        const handleAction = (e, action) => {
            const target = e.target.closest('.edit-icon, .delete-icon, .edit-btn, .delete-btn');
            if (!target) return;
            const { type, clinicName, floor, code } = target.dataset;
            action(type, { clinicName, floor, code });
        };

        dom.clinicGrid.addEventListener('click', function(e) {
            const header = e.target.closest('.clinic-header');
            if (header && !e.target.closest('.card-actions')) { header.closest('.clinic-card').classList.toggle('expanded'); return; }
            const codeItem = e.target.closest('.code-item');
            if (codeItem && !e.target.closest('.item-actions')) { const { clinic, floor, code, doctor } = codeItem.dataset; showDetails(clinic, floor, code, doctor); return; }
            handleAction(e, (type, data) => e.target.closest('.edit-icon') ? openEditModal(type, data) : handleDelete(type, data));
        });
        dom.tableBody.addEventListener('click', (e) => handleAction(e, (type, data) => e.target.closest('.edit-btn') ? openEditModal(type, data) : handleDelete(type, data)));
        
        dom.addClinicBtn.addEventListener('click', () => openEditModal('add'));
        dom.editModalClose.addEventListener('click', () => dom.editModal.style.display = 'none');
        dom.exportHtmlBtn.addEventListener('click', exportHTML);
        dom.editModeBtn.addEventListener('click', toggleEditMode);
        dom.saveAndExitBtn.addEventListener('click', toggleEditMode);
        window.addEventListener('click', (e) => { 
            if (e.target === dom.editModal) dom.editModal.style.display = 'none';
            if (e.target === dom.detailModal) dom.detailModal.style.display = 'none';
        });

        renderAll();
    });
    </script>
</body>
</html>
