import os
import re
import pathlib

def sanitize_filename(name):
    """Removes characters that are invalid in folder names."""
    # Replace slashes/backslashes with a hyphen
    name = re.sub(r'[/\\]', ' - ', name)
    # Remove other invalid characters
    name = re.sub(r'[<>:"|?*]', '', name)
    # Truncate to a reasonable length to avoid OS path limits
    return name[:150].strip()

def create_local_app():
    """
    Generates a folder structure based on clinic data and creates a local
    HTML file with links that can open these folders.
    """
    # 1. DATA (Same as before)
    clinic_data = [
        # ... (full data structure as in previous examples) ...
        { "name": "Iris - Polyclinic Mazaya 3", "floors": [ { "floor": 2, "codes": [{"code": "A", "doctor": "<PERSON>"}, {"code": "B", "doctor": "Smart Health Co."}, {"code": "C", "doctor": "<PERSON><PERSON><PERSON>"}] }, { "floor": 3, "codes": [{"code": "A+B", "doctor": "<PERSON><PERSON>"}, {"code": "C", "doctor": "<PERSON><PERSON><PERSON><PERSON>"}] }, { "floor": 4, "codes": [{"code": "A", "doctor": "Has<PERSON><PERSON>r & O<PERSON>id Metni"}, {"code": "B", "doctor": "<PERSON> Youssef <PERSON> Eissa"}, {"code": "C", "doctor": "Dr. <PERSON>m <PERSON>d Ali <PERSON>-Turki"}] } ] },
        { "name": "<PERSON> Aseel International", "floors": [ { "floor": 7, "codes": [{"code": "C", "doctor": "Daniel Alain"}] }, { "floor": 8, "codes": [{"code": "A", "doctor": "Abdullah Abdul Rahman Al Hassan"}, {"code": "B", "doctor": "Hossam Mohamed El Badri"}, {"code": "C", "doctor": "Mustafa Samy Al Kaddousy"}] }, { "floor": 9, "codes": [{"code": "A", "doctor": "Nasser Faisal Al Mutairy"}, {"code": "B", "doctor": "Andro George Mikha'eel"}, {"code": "C", "doctor": "Dr. Noor Aladdin Alomar & Dr. Aya Samara"}] }, { "floor": 10, "codes": [{"code": "A+B", "doctor": "Dr. Ali Al-Mukaimi & Nisreen Baeij"}, {"code": "C", "doctor": "Dr. Ali Al-Mukaimi"}] } ] },
        { "name": "Yarow - Polyclinic", "floors": [ { "floor": 11, "codes": [{"code": "A", "doctor": "Dr. Ahmed Abdulsamad Yehya Jassem"}, {"code": "B", "doctor": "Dr. Osamah J M Albaker"}, {"code": "C", "doctor": "Hossam Mohamed El Badri"}] }, { "floor": 12, "codes": [{"code": "A", "doctor": "Ahmed Mohamed Ahmed Ibrahim"}, {"code": "B", "doctor": "Sale Abdul Ghaffar Ma'arafie"}, {"code": "C", "doctor": "Adnan Ibrahim Ibrahim"}] } ] },
        { "name": "Fourth Medical Center", "floors": [ {"floor": 1, "codes": [{"code": "A", "doctor": "Salam Attar"}]}, {"floor": "2-3", "codes": [{"code": "A", "doctor": "One Day to Manage Projects Co."}]}, {"floor": "4-5", "codes": [{"code": "A", "doctor": "Athba Co."}]}, {"floor": 6, "codes": [{"code": "A", "doctor": "Health Care Co."}]}, {"floor": 7, "codes": [{"code": "A", "doctor": "Abdul Aziz Fahad Al Mezeiny"}]}, {"floor": 13, "codes": [{"code": "A", "doctor": "Revolution Medical Co."}]}, {"floor": 14, "codes": [{"code": "A", "doctor": "Dr. Farouk Alzoubani"}]}, {"floor": 15, "codes": [{"code": "A", "doctor": "Assem Drwesh Mostafa Abdulnabi"}]}, {"floor": 16, "codes": [{"code": "A", "doctor": "One Day to Manage Projects Co."}]}, {"floor": 17, "codes": [{"code": "A", "doctor": "Dr. Abdullah Sadad Sabri Al-Ozairi"}]}, {"floor": "18-19", "codes": [{"code": "A", "doctor": "Gulf Care Co."}]} ] },
        { "name": "Medical Harbour", "floors": [ {"floor": 1, "codes": [{"code": "C", "doctor": "Moaeyed Zaid Al Saq'abi"}]}, {"floor": 2, "codes": [{"code": "C", "doctor": "Mohamed Abdul Majid Hassan"}]}, {"floor": 3, "codes": [{"code": "C", "doctor": "Salah El Din Mohamed El Sherbini"}]}, {"floor": 4, "codes": [{"code": "C", "doctor": "Youssef Al Khleify/Rawan Al Khatib"}]}, {"floor": 8, "codes": [{"code": "C", "doctor": "Amir Eissa Attia Killa"}]}, {"floor": 9, "codes": [{"code": "C", "doctor": "Dr. Hesham Mohamed Yassin Ibrahim"}]}, {"floor": 10, "codes": [{"code": "C", "doctor": "Med Vision Medical Services"}]}, {"floor": 11, "codes": [{"code": "C", "doctor": "Fatmah Mohamed Badawy"}]}, {"floor": 12, "codes": [{"code": "C", "doctor": "Othman Youssef Al Mas'oud"}]}, {"floor": 13, "codes": [{"code": "C", "doctor": "Btissam Ibn Kiran"}]}, {"floor": 14, "codes": [{"code": "C", "doctor": "Misha'al Al Dahsh"}]}, {"floor": 15, "codes": [{"code": "C", "doctor": "Amal Al Shaiji / Faisal Al Terkeet"}]}, {"floor": 16, "codes": [{"code": "C", "doctor": "Signofa Co./Ahmed Eissa"}]}, {"floor": 17, "codes": [{"code": "C", "doctor": "Waleed Hamid Raheel"}]}, {"floor": 18, "codes": [{"code": "C", "doctor": "Eman Ghorab"}]}, {"floor": 19, "codes": [{"code": "C", "doctor": "Emad Morkos/Ahmed Youssef"}]}, {"floor": 20, "codes": [{"code": "C", "doctor": "Mohamed Al Kolk"}]}, {"floor": 21, "codes": [{"code": "C", "doctor": "Youssef Al Khleify"}]} ] },
        { "name": "Med Marine", "floors": [ {"floor": 5, "codes": [{"code": "A", "doctor": "Fatima Ne'ma Al Awadhi"}, {"code": "B", "doctor": "Mohamed As'ad Eid/Wael Bezrah"}]}, {"floor": 6, "codes": [{"code": "A+B", "doctor": "Mohamed Youssef Al Sabty"}]}, {"floor": 7, "codes": [{"code": "A+B", "doctor": "Mostafa Mohamed Tomsu"}]} ] },
        { "name": "JOYA - Polyclinic", "floors": [ {"floor": 8, "codes": [{"code": "A", "doctor": "Ihab Mohamed Younes Omar"}, {"code": "B", "doctor": "Huda Mahmoud Selim"}]}, {"floor": 9, "codes": [{"code": "A+B", "doctor": "Berlin Co./Mohamed Riyadh"}]}, {"floor": 10, "codes": [{"code": "A+B", "doctor": "Shehta Mostafa Ze'reb"}]} ] },
        { "name": "Med Grey", "floors": [ {"floor": 5, "codes": [{"code": "A", "doctor": "Dr. Amr Nabil Qutb"}]}, {"floor": "6-7", "codes": [{"code": "A", "doctor": "Dr. Shehta Mostafa Zurub"}]} ] },
        { "name": "Aram - Polyclinic", "floors": [ {"floor": 2, "codes": [{"code": "A+B", "doctor": "Dalia/Mina/Osama/Mahmoud"}]}, {"floor": 3, "codes": [{"code": "A+B", "doctor": "Ayman/Islam"}]}, {"floor": 4, "codes": [{"code": "A", "doctor": "Mohamed Al Sayyad"}, {"code": "B", "doctor": "Mohamed Al Sayyad"}]}, {"floor": 5, "codes": [{"code": "A", "doctor": "Bishoy/Mina/Zaher"}, {"code": "B", "doctor": "Nasser/Mohamed"}]}, {"floor": 6, "codes": [{"code": "A", "doctor": "Munira/Anjoud"}, {"code": "B", "doctor": "Munira/Anjoud"}]}, {"floor": 7, "codes": [{"code": "A+B", "doctor": "Sondos Ghaneim"}]}, {"floor": 8, "codes": [{"code": "A", "doctor": "Marina/Mary/Mariana"}, {"code": "B", "doctor": "Dr. Mohammed Salem"}]}, {"floor": 9, "codes": [{"code": "A", "doctor": "Rwda Ahmed"}, {"code": "B", "doctor": "Marawan Essam"}]} ] }
    ]

    base_dir = pathlib.Path("Polyclinic_File_System")
    html_rows = []

    print("Generating folder structure...")
    for clinic in clinic_data:
        s_clinic_name = sanitize_filename(clinic["name"])
        for floor in clinic["floors"]:
            s_floor_name = sanitize_filename(f"Floor {floor['floor']}")
            for code in floor["codes"]:
                s_entry_name = sanitize_filename(f"{code['code']} - {code['doctor']}")
                
                # Create the full path and the folder
                full_path = base_dir / s_clinic_name / s_floor_name / s_entry_name
                full_path.mkdir(parents=True, exist_ok=True)
                
                # Get the absolute path for the hyperlink
                absolute_path = full_path.resolve()
                
                # Create the HTML table row with a link to the folder
                html_rows.append(f"""
                <tr>
                    <td>{clinic["name"]}</td>
                    <td>{floor["floor"]}</td>
                    <td>{code["doctor"]}</td>
                    <td><a href="file:///{absolute_path}" class="folder-link">Open Folder</a></td>
                </tr>
                """)
    print("Folder structure created successfully.")

    # 2. CREATE THE HTML FILE
    html_template = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>Local Polyclinic Directory</title>
        <style>
            body {{ font-family: sans-serif; background-color: #f4f7f6; margin: 20px; }}
            .container {{ max-width: 1000px; margin: auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }}
            h1 {{ color: #1e3c72; text-align: center; }}
            table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
            th, td {{ padding: 12px; border: 1px solid #ddd; text-align: left; }}
            thead {{ background-color: #2a5298; color: white; }}
            tbody tr:nth-child(even) {{ background-color: #f2f2f2; }}
            .folder-link {{ display: inline-block; padding: 8px 12px; background-color: #28a745; color: white; text-decoration: none; border-radius: 5px; font-weight: bold; }}
            .folder-link:hover {{ background-color: #218838; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Local Polyclinic Directory</h1>
            <p><strong>IMPORTANT:</strong> This file must be opened directly from your computer (double-click it). The "Open Folder" links will not work if this page is hosted on a web server.</p>
            <table>
                <thead>
                    <tr>
                        <th>Clinic Name</th>
                        <th>Floor</th>
                        <th>Doctor/Company</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    {''.join(html_rows)}
                </tbody>
            </table>
        </div>
    </body>
    </html>
    """

    output_html_file = "polyclinic_local_browser.html"
    with open(output_html_file, "w", encoding="utf-8") as f:
        f.write(html_template)
    
    print(f"\nSuccess! '{output_html_file}' has been created.")
    print(f"-> Double-click this file in your file explorer to use the local app.")

if __name__ == "__main__":
    create_local_app()