body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f4f7f6;
  color: #333;
  margin: 0;
  padding: 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

h1 {
  text-align: center;
  color: #2c3e50;
}

/* Dashboard */
.dashboard {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
  gap: 20px;
}

.card {
  background: #ecf0f1;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  flex-grow: 1;
}

.card h2 {
  margin-top: 0;
  font-size: 1em;
  color: #7f8c8d;
}

.card p {
  margin: 0;
  font-size: 1.5em;
  font-weight: bold;
  color: #2c3e50;
}

.card .due-balance {
  color: #c0392b;
}

/* Filters */
.filters {
  margin-bottom: 20px;
}

.filters input {
  width: 100%;
  padding: 10px;
  font-size: 1em;
  border: 1px solid #bdc3c7;
  border-radius: 4px;
}

/* Table */
table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

th {
  background-color: #34495e;
  color: #fff;
  font-weight: bold;
}

tr:nth-child(even) {
  background-color: #f9f9f9;
}

tr:hover {
  background-color: #f1f1f1;
}

.due-balance-cell {
  font-weight: bold;
  color: #e74c3c;
}