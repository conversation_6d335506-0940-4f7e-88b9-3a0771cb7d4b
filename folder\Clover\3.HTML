<!DOCTYPE html>
<html>
<head>
    <title>Polyclinic Organizational Chart</title>
    <style>
        body { font-family: 'Segoe UI', sans-serif; margin: 20px; background: #f0f2f5; }
        .chart { display: flex; flex-direction: column; align-items: center; }
        .node { 
            padding: 15px; 
            margin: 10px; 
            border-radius: 8px; 
            text-align: center;
            min-width: 200px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .medical-labs { background: #ffebee; border: 2px solid #c62828; }
        .clover { background: #f3e5f5; border: 2px solid #7b1fa2; }
        .clinic { background: #e3f2fd; border: 2px solid #1976d2; }
        .floor { background: #e8f5e9; border: 2px solid #388e3c; }
        .clinic-code { background: #fffde7; border: 2px solid #f9a825; }
        .connector { width: 2px; height: 30px; background: #666; margin: 2px auto; }
        h2 { color: #1a237e; text-align: center; }
    </style>
</head>
<body>
    <h2>Polyclinic Organization Chart</h2>
    <div class="chart" id="chart"></div>

    <script>
        const data = {
            name: "Medical Labs",
            children: [
                {
                    name: "Clover",
                    children: [
                        {
                            name: "Iris - Polyclinic Mazaya 3",
                            floors: [
                                {
                                    floor: 2,
                                    codes: [
                                        {code: "A", doctor: "Abdullah Abdul Aziz Al Hajri"},
                                        {code: "B", doctor: "Smart Health Co."},
                                        {code: "C", doctor: "Walid Hamad Ashwi Raheel"}
                                    ]
                                },
                                {
                                    floor: 3,
                                    codes: [
                                        {code: "A+B", doctor: "Amr Nabil Qutb"},
                                        {code: "C", doctor: "Oxcana Bogdanovic"}
                                    ]
                                },
                                {
                                    floor: 4,
                                    codes: [
                                        {code: "A", doctor: "Hassaan A Jaber & Obaid Metni"},
                                        {code: "B", doctor: "Mohamed Youssef Al Eissa"},
                                        {code: "C", doctor: "Dr. Mariam Abed Ali Al-Turki"}
                                    ]
                                }
                            ]
                        },
                        {
                            name: "Al Aseel International",
                            floors: [
                                {
                                    floor: 7,
                                    codes: [{code: "C", doctor: "Daniel Alain"}]
                                },
                                {
                                    floor: 8,
                                    codes: [
                                        {code: "A", doctor: "Abdullah Abdul Rahman Al Hassan"},
                                        {code: "B", doctor: "Hossam Mohamed El Badri"},
                                        {code: "C", doctor: "Mustafa Samy Al Kaddousy"}
                                    ]
                                },
                                {
                                    floor: 9,
                                    codes: [
                                        {code: "A", doctor: "Nasser Faisal Al Mutairy"},
                                        {code: "B", doctor: "Andro George Mikha'eel"},
                                        {code: "C", doctor: "Dr. Noor Aladdin Alomar & Dr. Aya Samara"}
                                    ]
                                },
                                {
                                    floor: 10,
                                    codes: [
                                        {code: "A+B", doctor: "Dr. Ali Al-Mukaimi & Nisreen Baeij"},
                                        {code: "C", doctor: "Dr. Ali Al-Mukaimi"}
                                    ]
                                }
                            ]
                        },
                        {
                            name: "Yarow - Polyclinic",
                            floors: [
                                {
                                    floor: 11,
                                    codes: [
                                        {code: "A", doctor: "Dr. Ahmed Abdulsamad Yehya Jassem"},
                                        {code: "B", doctor: "Dr. Osamah J M Albaker"},
                                        {code: "C", doctor: "Hossam Mohamed El Badri"}
                                    ]
                                },
                                {
                                    floor: 12,
                                    codes: [
                                        {code: "A", doctor: "Ahmed Mohamed Ahmed Ibrahim"},
                                        {code: "B", doctor: "Sale Abdul Ghaffar Ma'arafie"},
                                        {code: "C", doctor: "Adnan Ibrahim Ibrahim"}
                                    ]
                                }
                            ]
                        },
                        {
                            name: "Fourth Medical Center",
                            floors: [
                                {floor: 1, codes: [{code: "A", doctor: "Salam Attar"}]},
                                {floor: "2-3", codes: [{code: "A", doctor: "One Day to Manage Projects Co."}]},
                                {floor: "4-5", codes: [{code: "A", doctor: "Athba Co."}]},
                                {floor: 6, codes: [{code: "A", doctor: "Health Care Co."}]},
                                {floor: 7, codes: [{code: "A", doctor: "Abdul Aziz Fahad Al Mezeiny"}]},
                                {floor: 13, codes: [{code: "A", doctor: "Revolution Medical Co."}]},
                                {floor: 14, codes: [{code: "A", doctor: "Dr. Farouk Alzoubani"}]},
                                {floor: 15, codes: [{code: "A", doctor: "Assem Drwesh Mostafa Abdulnabi"}]},
                                {floor: 16, codes: [{code: "A", doctor: "One Day to Manage Projects Co."}]},
                                {floor: 17, codes: [{code: "A", doctor: "Dr. Abdullah Sadad Sabri Al-Ozairi"}]},
                                {floor: "18-19", codes: [{code: "A", doctor: "Gulf Care Co."}]}
                            ]
                        },
                        {
                            name: "Medical Harbour",
                            floors: [
                                {floor: 1, codes: [{code: "C", doctor: "Moaeyed Zaid Al Saq'abi"}]},
                                {floor: 2, codes: [{code: "C", doctor: "Mohamed Abdul Majid Hassan"}]},
                                {floor: 3, codes: [{code: "C", doctor: "Salah El Din Mohamed El Sherbini"}]},
                                {floor: 4, codes: [{code: "C", doctor: "Youssef Al Khleify/Rawan Al Khatib"}]},
                                {floor: 8, codes: [{code: "C", doctor: "Amir Eissa Attia Killa"}]},
                                {floor: 9, codes: [{code: "C", doctor: "Dr. Hesham Mohamed Yassin Ibrahim"}]},
                                {floor: 10, codes: [{code: "C", doctor: "Med Vision Medical Services"}]},
                                {floor: 11, codes: [{code: "C", doctor: "Fatmah Mohamed Badawy"}]},
                                {floor: 12, codes: [{code: "C", doctor: "Othman Youssef Al Mas'oud"}]},
                                {floor: 13, codes: [{code: "C", doctor: "Btissam Ibn Kiran"}]},
                                {floor: 14, codes: [{code: "C", doctor: "Misha'al Al Dahsh"}]},
                                {floor: 15, codes: [{code: "C", doctor: "Amal Al Shaiji / Faisal Al Terkeet"}]},
                                {floor: 16, codes: [{code: "C", doctor: "Signofa Co./Ahmed Eissa"}]},
                                {floor: 17, codes: [{code: "C", doctor: "Waleed Hamid Raheel"}]},
                                {floor: 18, codes: [{code: "C", doctor: "Eman Ghorab"}]},
                                {floor: 19, codes: [{code: "C", doctor: "Emad Morkos/Ahmed Youssef"}]},
                                {floor: 20, codes: [{code: "C", doctor: "Mohamed Al Kolk"}]},
                                {floor: 21, codes: [{code: "C", doctor: "Youssef Al Khleify"}]}
                            ]
                        },
                        {
                            name: "Med Marine",
                            floors: [
                                {floor: 5, codes: [
                                    {code: "A", doctor: "Fatima Ne'ma Al Awadhi"},
                                    {code: "B", doctor: "Mohamed As'ad Eid/Wael Bezrah"}
                                ]},
                                {floor: 6, codes: [{code: "A+B", doctor: "Mohamed Youssef Al Sabty"}]},
                                {floor: 7, codes: [{code: "A+B", doctor: "Mostafa Mohamed Tomsu"}]}
                            ]
                        },
                        {
                            name: "JOYA - Polyclinic",
                            floors: [
                                {floor: 8, codes: [
                                    {code: "A", doctor: "Ihab Mohamed Younes Omar"},
                                    {code: "B", doctor: "Huda Mahmoud Selim"}
                                ]},
                                {floor: 9, codes: [{code: "A+B", doctor: "Berlin Co./Mohamed Riyadh"}]},
                                {floor: 10, codes: [{code: "A+B", doctor: "Shehta Mostafa Ze'reb"}]}
                            ]
                        },
                        {
                            name: "Med Grey",
                            floors: [
                                {floor: 5, codes: [{code: "A", doctor: "Dr. Amr Nabil Qutb"}]},
                                {floor: "6-7", codes: [{code: "A", doctor: "Dr. Shehta Mostafa Zurub"}]}
                            ]
                        },
                        {
                            name: "Aram - Polyclinic",
                            floors: [
                                {floor: 2, codes: [{code: "A+B", doctor: "Dalia/Mina/Osama/Mahmoud"}]},
                                {floor: 3, codes: [{code: "A+B", doctor: "Ayman/Islam"}]},
                                {floor: 4, codes: [
                                    {code: "A", doctor: "Mohamed Al Sayyad"},
                                    {code: "B", doctor: "Mohamed Al Sayyad"}
                                ]},
                                {floor: 5, codes: [
                                    {code: "A", doctor: "Bishoy/Mina/Zaher"},
                                    {code: "B", doctor: "Nasser/Mohamed"}
                                ]},
                                {floor: 6, codes: [
                                    {code: "A", doctor: "Munira/Anjoud"},
                                    {code: "B", doctor: "Munira/Anjoud"}
                                ]},
                                {floor: 7, codes: [{code: "A+B", doctor: "Sondos Ghaneim"}]},
                                {floor: 8, codes: [
                                    {code: "A", doctor: "Marina/Mary/Mariana"},
                                    {code: "B", doctor: "Dr. Mohammed Salem"}
                                ]},
                                {floor: 9, codes: [
                                    {code: "A", doctor: "Rwda Ahmed"},
                                    {code: "B", doctor: "Marawan Essam"}
                                ]}
                            ]
                        }
                    ]
                }
            ]
        };

        function createChart() {
            const chart = document.getElementById('chart');
            
            function createNodes(parent, level) {
                // Create node
                const node = document.createElement('div');
                node.className = `node ${level}`;
                
                // Add title based on level
                let title;
                if (level === 'medical-labs') title = `<h3>${parent.name}</h3>`;
                else if (level === 'clover') title = `<h4>${parent.name}</h4>`;
                else title = `<h4>${parent.name}</h4>`;
                
                node.innerHTML = title;
                chart.appendChild(node);

                // Create connector if not at lowest level
                if (parent.children || parent.floors) {
                    const connector = document.createElement('div');
                    connector.className = 'connector';
                    chart.appendChild(connector);
                }

                // Create child nodes if they exist
                if (parent.children) {
                    parent.children.forEach(child => {
                        createNodes(child, getNextLevel(level));
                    });
                }
                
                // Create floor nodes if they exist
                if (parent.floors) {
                    parent.floors.forEach(floor => {
                        const floorNode = document.createElement('div');
                        floorNode.className = 'node floor';
                        floorNode.innerHTML = `<h5>Floor ${floor.floor}</h5>`;
                        chart.appendChild(floorNode);

                        // Create connector
                        const connector = document.createElement('div');
                        connector.className = 'connector';
                        chart.appendChild(connector);

                        // Create clinic code nodes
                        floor.codes.forEach(code => {
                            const codeNode = document.createElement('div');
                            codeNode.className = 'node clinic-code';
                            codeNode.innerHTML = `
                                <strong>Clinic ${code.code}</strong><br>
                                <small>${code.doctor}</small>
                            `;
                            chart.appendChild(codeNode);
                        });
                    });
                }
            }
            
            function getNextLevel(currentLevel) {
                if (currentLevel === 'medical-labs') return 'clover';
                if (currentLevel === 'clover') return 'clinic';
                return 'clinic'; // default
            }
            
            // Start with top-level node
            createNodes(data, 'medical-labs');
        }

        // Initialize chart
        createChart();
    </script>
</body>
</html>