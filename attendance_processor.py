import pandas as pd
import re

def parse_attendance_history(html_file):
    """
    Parses the HTML attendance history table and extracts the data into a pandas DataFrame.

    Args:
        html_file (str): Path to the HTML file.

    Returns:
        pandas.DataFrame: DataFrame containing the attendance history, or None if parsing fails.
    """
    try:
        with open(html_file, 'r', encoding='windows-1256') as f:
            html_content = f.read()

        # Find all tables with id='result' (there are two in this HTML for pagination)
        table_matches = re.findall(r'<table[^>]*?id=[\'"]result[\'"].*?>(.*?)</table>', html_content, re.DOTALL)
        
        if not table_matches:
            print("Error: Could not find attendance table(s) in HTML.")
            return None

        all_data = []
        header = []

        for table_html in table_matches:
            rows_matches = re.findall(r'<tr.*?>(.*?)</tr>', table_html, re.DOTALL) 
            if not rows_matches:
                 continue # Skip if no rows found in table

            current_table_data = []
            
            # Identify header rows (first two rows, but the second one has the final column names)
            # This ensures we only capture the header once from the first table found
            if not header and len(rows_matches) >= 2:
                # The second <tr> contains the actual column names we want
                header_cells = re.findall(r'<t[dh].*?>(.*?)</t[dh]>', rows_matches[1], re.DOTALL)
                header = [re.sub(r' +', ' ', re.sub(r'<.*?>', '', cell).strip()) for cell in header_cells]
            
            # Start data extraction from the third row (index 2) onwards for each table
            # This skips the two header rows already processed or the initial empty ones
            for i in range(2, len(rows_matches)):
                row_html = rows_matches[i]
                cells_matches = re.findall(r'<t[dh].*?>(.*?)</t[dh]>', row_html, re.DOTALL) 
                if not cells_matches:
                    continue # Skip if no cells found in row

                cleaned_cells = [re.sub(r' +', ' ', re.sub(r'<.*?>', '', cell).strip()) for cell in cells_matches]
                
                # Ensure row has enough columns to match header length, fill with '' if not
                # Or truncate if too many cells (can happen with summary rows like Totals)
                if len(cleaned_cells) < len(header):
                    cleaned_cells.extend([''] * (len(header) - len(cleaned_cells)))
                elif len(cleaned_cells) > len(header):
                    cleaned_cells = cleaned_cells[:len(header)] # Truncate if too many cells

                current_table_data.append(cleaned_cells)
            
            all_data.extend(current_table_data)

        if not header:
            print("Error: Could not extract header from table. Check HTML structure.")
            return None
        if not all_data:
            print("No data rows found in the tables after initial parsing.")
            return None

        df = pd.DataFrame(all_data, columns=header)
        
        # Filter out rows that are entirely empty or represent summary rows like "Totals"
        # Assuming 'Work Time' or 'Weekday' would be empty/contain 'Totals' for such rows
        df = df[df['Work Time'].str.strip() != ''].copy() # Remove rows where Work Time is empty
        df = df[df['Weekday'].str.strip().str.lower() != 'totals'].copy() # Remove the 'Totals' row

        if df.empty:
            print("No valid attendance data found after filtering out empty/summary rows.")
            return None
            
        return df

    except FileNotFoundError:
        print(f"Error: File not found: {html_file}")
        return None
    except Exception as e:
        print(f"Error: An error occurred during parsing: {e}")
        return None


def convert_entry_time(df):
    """
    Converts the "Entry Time" column to datetime objects and adds a new column
    indicating if the entry time is after 8:30 AM.

    Args:
        df (pandas.DataFrame): DataFrame containing the attendance history.

    Returns:
        pandas.DataFrame: DataFrame with converted "Entry Time" and "After 8:30" column.
    """
    if df.empty:
        df['Entry Time Converted'] = pd.Series(dtype='object')
        df['After 8:30'] = pd.Series(dtype='bool')
        return df

    try:
        # Convert 'Entry Time' to datetime.time objects
        # Use errors='coerce' to turn unparseable dates into NaT (Not a Time)
        df['Entry Time Converted'] = pd.to_datetime(df['Entry Time'], format='%I:%M %p', errors='coerce').dt.time
        
        # Define the target time for comparison
        target_time = pd.to_datetime('8:30 AM', format='%I:%M %p').time()
        
        # Create the 'After 8:30' column
        df['After 8:30'] = df['Entry Time Converted'] > target_time
        return df
    except KeyError:
        print("Error: 'Entry Time' column not found for time conversion.")
        return df  # Return original DataFrame if column missing
    except Exception as e:
        print(f"Error during time conversion: {e}")
        return df  # Return original DataFrame if conversion fails



def analyze_attendance(df):
    """
    Analyzes the attendance data, counts the number of entries where 'After 8:30' is True.

    Args:
        df (pandas.DataFrame): DataFrame with attendance data including 'After 8:30' column.

    Returns:
        int: Number of entries after 8:30 AM.
    """
    if 'After 8:30' not in df.columns:
        print("Error: 'After 8:30' column not found. Run convert_entry_time() first.")
        return 0

    # Ensure the column is boolean before summing to count True values
    count = df['After 8:30'].sum() 
    return count


def main():
    # Make sure this matches the filename of your HTML export
    html_file = "attendance_history for may 2025.html" 
    
    # Parse the HTML and get the DataFrame
    df = parse_attendance_history(html_file)

    if df is None or df.empty:
        print("Processing finished: No valid attendance data to process or found after filtering.")
        return

    # Convert entry times and add the 'After 8:30' column
    df_processed = convert_entry_time(df.copy()) # Use a copy to avoid modifying the original 'df' if needed elsewhere

    if df_processed is None:
        print("Failed to process entry times. Excel output might be incomplete.")
        # Proceed with saving what could be processed, if df is not None
        df_to_save = df.copy() 
        print("Attempting to save raw parsed data to Excel.")
    else:
        df_to_save = df_processed.copy()

    # Count entries after 8:30 AM
    late_count = analyze_attendance(df_to_save)

    # Display the processed DataFrame
    print("--- Processed Attendance Data ---")
    print(df_to_save.to_string()) # .to_string() helps display the full DataFrame without truncation

    # Display the total count
    print(f"\nTotal number of entries after 8:30 AM (excluding non-attendance days): {late_count}")

    # Save to Excel
    output_excel_file = "attendance_analysis_may_2025.xlsx"
    try:
        # Select and reorder columns for clarity in Excel
        # Check if the 'Entry Time Converted' and 'After 8:30' columns exist before selecting
        cols_to_output = ['Weekday', 'Attendance Day', 'Work Time', 'Entry Time']
        if 'Entry Time Converted' in df_to_save.columns:
            cols_to_output.append('Entry Time Converted')
        if 'After 8:30' in df_to_save.columns:
            cols_to_output.append('After 8:30')
        
        cols_to_output.extend(['End Time', 'Exit Time', 'Early Out Permission', 'Actual Hours'])
        
        # Filter columns to only include those actually present in df_to_save
        final_cols = [col for col in cols_to_output if col in df_to_save.columns]
        output_df = df_to_save[final_cols]

        output_df.to_excel(output_excel_file, index=False)
        print(f"\nAttendance analysis saved to {output_excel_file}")
    except Exception as e:
        print(f"Error saving to Excel: {e}")

if __name__ == "__main__":
    main()