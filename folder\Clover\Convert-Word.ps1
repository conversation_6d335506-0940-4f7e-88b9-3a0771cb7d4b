$htmlPath = "C:\New folder\Clover\2.HTML"
$docxPath = "C:\New folder\Clover\3.docx"

try {
    # Verify HTML file exists and has content
    if (-not (Test-Path $htmlPath)) {
        throw "HTML file not found at $htmlPath"
    }
    if ((Get-Content $htmlPath -Raw).Trim() -eq "") {
        throw "HTML file is empty"
    }

    $word = New-Object -ComObject Word.Application
    $word.Visible = $false
    $doc = $word.Documents.Add()
    
    # Insert HTML content
    $doc.Content.InsertFile($htmlPath)
    
    if ($doc.Content.Text.Trim() -eq "") {
        throw "No content inserted into Word document"
    }

    if (Test-Path $docxPath) {
        Remove-Item $docxPath -Force
    }

    $doc.SaveAs([ref]$docxPath, [ref]16)
    Write-Host "Successfully converted to $docxPath" -ForegroundColor Green
}
catch {
    Write-Error "Conversion failed: $_"
    exit 1
}
finally {
    if ($doc) { $doc.Close($false) }
    if ($word) { $word.Quit() }
    [Runtime.InteropServices.Marshal]::ReleaseComObject($doc) | Out-Null
    [Runtime.InteropServices.Marshal]::ReleaseComObject($word) | Out-Null
    [GC]::Collect()
    [GC]::WaitForPendingFinalizers()
}