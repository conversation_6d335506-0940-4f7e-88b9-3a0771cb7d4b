import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './App.css'; // We will create this file for styling

function App() {
  const [customers, setCustomers] = useState([]);
  const [filter, setFilter] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Fetch data from the backend API
    axios.get('http://127.0.0.1:5000/api/data')
      .then(response => {
        setCustomers(response.data);
        setLoading(false);
      })
      .catch(error => {
        console.error("There was an error fetching the data!", error);
        setLoading(false);
      });
  }, []); // The empty array means this effect runs once on mount

  // --- Calculations for the Dashboard ---
  const totalDueBalance = customers.reduce((sum, customer) => sum + customer.due_balance, 0);
  const totalOpeningBalance = customers.reduce((sum, customer) => sum + (customer.opening_balance || 0), 0);

  // --- Filtering Logic ---
  const filteredCustomers = customers.filter(customer =>
    customer.customer_name.toLowerCase().includes(filter.toLowerCase())
  );

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="container">
      <h1>Customer Utility Dashboard</h1>

      {/* --- Dashboard Summary --- */}
      <div className="dashboard">
        <div className="card">
          <h2>Total Customers</h2>
          <p>{customers.length}</p>
        </div>
        <div className="card">
          <h2>Total Opening Balance</h2>
          <p>${totalOpeningBalance.toLocaleString()}</p>
        </div>
        <div className="card">
          <h2>Total Outstanding Balance</h2>
          <p className="due-balance">${totalDueBalance.toLocaleString()}</p>
        </div>
      </div>

      {/* --- Filters --- */}
      <div className="filters">
        <input
          type="text"
          placeholder="Filter by Customer Name..."
          value={filter}
          onChange={e => setFilter(e.target.value)}
        />
      </div>

      {/* --- Data Table --- */}
      <table>
        <thead>
          <tr>
            <th>Customer Name</th>
            <th>Unit</th>
            <th>Opening Balance</th>
            <th>Total Invoiced</th>
            <th>Total Collected</th>
            <th>Due Balance</th>
          </tr>
        </thead>
        <tbody>
          {filteredCustomers.map(customer => (
            <tr key={customer.id}>
              <td>{customer.customer_name}</td>
              <td>{customer.unit_code}</td>
              <td>${(customer.opening_balance || 0).toLocaleString()}</td>
              <td>${customer.total_invoiced.toLocaleString()}</td>
              <td>${customer.total_collected.toLocaleString()}</td>
              <td className="due-balance-cell">${customer.due_balance.toLocaleString()}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

export default App;