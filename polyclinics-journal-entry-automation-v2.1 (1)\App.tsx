

import React, { useState, useCallback, useRef } from 'react';
import { JournalEntry, ExtractedData, ExtractedTransaction } from './types';
import { extractTransactionsFromText } from './services/geminiService';
import { generateJournalEntries, convertToXLSX } from './services/journalService';
import { SpinnerIcon, ProcessIcon, DownloadIcon, XIcon } from './components/icons';
import { OUTPUT_HEADER } from './constants';
import * as pdfjs from 'pdfjs-dist';
import JSZip from 'jszip';


// Set worker path to a static CDN URL to ensure robust loading.
// This avoids dynamic URL construction errors and matches the version in the import map.
pdfjs.GlobalWorkerOptions.workerSrc = `https://esm.sh/pdfjs-dist@4.5.136/build/pdf.worker.mjs`;

const delay = (ms: number) => new Promise(res => setTimeout(res, ms));

const Header: React.FC = () => (
    <header className="w-full bg-gray-900 shadow-md p-4 flex flex-col sm:flex-row items-center justify-between">
        <div className="flex items-center">
            <ProcessIcon className="w-8 h-8 text-teal-400 mr-3" aria-hidden="true" />
            <h1 className="text-2xl font-bold text-gray-200">Journal Entry Automation</h1>
        </div>
         <div className="flex items-center space-x-4">
            <p className="text-sm text-gray-400 mt-2 sm:mt-0">Prepared by Haitham Soliman Abdou</p>
        </div>
    </header>
);

const FileListItem: React.FC<{ file: File; onRemove: (name: string) => void }> = ({ file, onRemove }) => (
    <div className="flex items-center justify-between bg-gray-700 p-2 rounded-md text-sm animate-fade-in">
        <span className="text-gray-300 truncate pr-2">{file.name}</span>
        <button onClick={() => onRemove(file.name)} className="text-gray-500 hover:text-red-400 p-1 rounded-full focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors">
            <XIcon className="w-4 h-4" />
        </button>
    </div>
);


const ReviewData: React.FC<{ file: File, data: ExtractedData }> = ({ file, data }) => (
  <div className="bg-gray-800 p-4 rounded-lg border border-gray-700 animate-fade-in">
    <h3 className="font-bold text-lg text-teal-400 mb-1">{file.name}</h3>
    <div className="bg-gray-900/50 p-2 rounded-md mb-3">
      <p className="text-sm text-gray-400">Detected Account Name: <span className="font-semibold text-gray-200">{data.accountName}</span></p>
    </div>
    <div className="overflow-y-auto flex-grow rounded-lg border border-gray-700" style={{maxHeight: '300px'}}>
      <table className="min-w-full text-sm text-left text-gray-300">
        <thead className="text-xs text-teal-400 uppercase bg-gray-700/50 sticky top-0 backdrop-blur-sm">
          <tr>
            <th scope="col" className="px-4 py-2">Date</th>
            <th scope="col" className="px-4 py-2">Description</th>
            <th scope="col" className="px-4 py-2 text-right">Amount</th>
            <th scope="col" className="px-4 py-2 text-center">Type</th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-700">
          {data.transactions.map((tx: ExtractedTransaction, index: number) => (
            <tr key={index} className="hover:bg-gray-800">
              <td className="px-4 py-2 whitespace-nowrap">{tx.date}</td>
              <td className="px-4 py-2">{tx.description}</td>
              <td className="px-4 py-2 text-right font-mono">{tx.amount.toFixed(2)}</td>
              <td className="px-4 py-2 text-center">
                <span className={`px-2 py-1 text-xs font-semibold rounded-full ${tx.type === 'credit' ? 'bg-green-900 text-green-300' : 'bg-yellow-900 text-yellow-300'}`}>
                  {tx.type.toUpperCase()}
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  </div>
);


export default function App() {
    const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
    const [extractedDataList, setExtractedDataList] = useState<{ file: File; data: ExtractedData }[]>([]);
    const [journalEntriesByFile, setJournalEntriesByFile] = useState<{ [fileName: string]: JournalEntry[] }>({});
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [errors, setErrors] = useState<{ [fileName: string]: string }>({});
    const fileInputRef = useRef<HTMLInputElement>(null);

    const getTextFromPdf = async (file: File): Promise<string> => {
        const arrayBuffer = await file.arrayBuffer();
        const pdf = await pdfjs.getDocument({ data: arrayBuffer }).promise;
        let fullText = '';
        for (let i = 1; i <= pdf.numPages; i++) {
            const page = await pdf.getPage(i);
            const textContent = await page.getTextContent();
            const pageText = textContent.items.map(item => 'str' in item ? item.str : '').join(' ');
            fullText += pageText + '\n';
        }
        return fullText;
    };

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files;
        if (files) {
            setSelectedFiles(Array.from(files));
            setErrors({});
            setExtractedDataList([]);
            setJournalEntriesByFile({});
        }
    };
    
    const handleRemoveFile = (fileNameToRemove: string) => {
        setSelectedFiles(prevFiles => prevFiles.filter(file => file.name !== fileNameToRemove));
    };
    
    const resetState = () => {
        setSelectedFiles([]);
        setErrors({});
        setExtractedDataList([]);
        setJournalEntriesByFile({});
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    const handleProcess = useCallback(async () => {
        if (selectedFiles.length === 0) {
            setErrors({ general: "Please upload at least one PDF file." });
            return;
        }
        setIsLoading(true);
        setErrors({});
        setExtractedDataList([]);
        setJournalEntriesByFile({});

        const successData: { file: File; data: ExtractedData }[] = [];
        const newErrors: { [fileName: string]: string } = {};

        // Process files sequentially with a delay to avoid API rate limits.
        for (const [index, file] of selectedFiles.entries()) {
            try {
                const rawText = await getTextFromPdf(file);
                if (!rawText.trim()) {
                    throw new Error("Could not extract any text from the PDF. It might be an image-based PDF or corrupted.");
                }
                const data = await extractTransactionsFromText(rawText);
                successData.push({ file, data });
            } catch (err: any) {
                 newErrors[file.name] = err.message || "An unknown error occurred.";
            }
            
            // Add a delay after each API call to respect rate limits (e.g., 60 RPM).
            // Skip the delay on the very last file.
            if (index < selectedFiles.length - 1) {
                await delay(1000); // 1-second delay
            }
        }

        setExtractedDataList(successData);
        setErrors(newErrors);
        setIsLoading(false);
    }, [selectedFiles]);

    const handleGenerateJournals = useCallback(() => {
        if (extractedDataList.length === 0) return;
        try {
            const entriesByFile: { [fileName: string]: JournalEntry[] } = {};
            for (const { file, data } of extractedDataList) {
                const entries = generateJournalEntries(data);
                entriesByFile[file.name] = entries;
            }
            setJournalEntriesByFile(entriesByFile);
            setExtractedDataList([]); // Clear this to move to the next stage
        } catch (err: any) {
            setErrors({ general: err.message || "An error occurred while generating entries." });
            setJournalEntriesByFile({});
        }
    }, [extractedDataList]);

    const handleDownload = useCallback(async () => {
        if (Object.keys(journalEntriesByFile).length === 0) return;

        const zip = new JSZip();

        for (const fileName in journalEntriesByFile) {
            if (journalEntriesByFile[fileName].length > 0) {
                const xlsxContent = convertToXLSX(journalEntriesByFile[fileName]);
                const newFileName = fileName.replace(/\.pdf$/i, '.xlsx');
                zip.file(newFileName, xlsxContent);
            }
        }
        
        const zipBlob = await zip.generateAsync({ type: 'blob' });
        const link = document.createElement('a');
        if (link.href) {
            URL.revokeObjectURL(link.href);
        }
        link.href = URL.createObjectURL(zipBlob);
        link.download = "JournalEntries.zip";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

    }, [journalEntriesByFile]);

    const handleDownloadSingleSheet = useCallback(() => {
        if (Object.keys(journalEntriesByFile).length === 0) return;
    
        const consolidatedEntries: JournalEntry[] = [];
        let journalNumberBase = 0;

        // Sort filenames for a deterministic, sequential order
        const sortedFileNames = Object.keys(journalEntriesByFile).sort();

        for (const fileName of sortedFileNames) {
            const entriesForFile = journalEntriesByFile[fileName];
            if (!entriesForFile || entriesForFile.length === 0) continue;

            // Assign new, sequential journal numbers for this file's entries
            const creditJournalNumber = journalNumberBase + 1;
            const debitJournalNumber = journalNumberBase + 2;

            const modifiedEntries = entriesForFile.map(entry => {
                const newEntry = { ...entry }; // Copy the entry
                if (entry.journalName === 'CRNOTE') {
                    newEntry.journalNumber = creditJournalNumber;
                } else if (entry.journalName === 'STVINV') {
                    newEntry.journalNumber = debitJournalNumber;
                }
                return newEntry;
            });
            
            consolidatedEntries.push(...modifiedEntries);
            journalNumberBase += 2; // Increment base for the next file
        }

        // Re-sort the entire collection by the new journal number, then by date
        consolidatedEntries.sort((a, b) => {
            if (a.journalNumber !== b.journalNumber) {
                return a.journalNumber - b.journalNumber;
            }
            // Date is in DD-MM-YYYY format, must parse correctly for comparison
            const dateA = new Date(a.postingDate.split('-').reverse().join('-')).getTime();
            const dateB = new Date(b.postingDate.split('-').reverse().join('-')).getTime();
            if (isNaN(dateA) || isNaN(dateB)) return 0; // Fallback for invalid dates
            return dateA - dateB;
        });

        // After sorting, recalculate Line Num and Number of Voucher to be sequential
        let lineNumCounter = 0;
        let lastJournalNum = -1;
        const finalEntries = consolidatedEntries.map(entry => {
            if (entry.journalNumber !== lastJournalNum) {
                lastJournalNum = entry.journalNumber;
                lineNumCounter = 1;
            } else {
                lineNumCounter++;
            }
            return {
                ...entry,
                lineNum: lineNumCounter,
                numberOfVoucher: lineNumCounter,
            };
        });
    
        if (finalEntries.length === 0) {
            setErrors({ general: "No journal entries were generated to download." });
            return;
        }
    
        try {
            const xlsxContent = convertToXLSX(finalEntries);
            const blob = new Blob([xlsxContent], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            
            const link = document.createElement('a');
            if (link.href) {
                URL.revokeObjectURL(link.href);
            }
            link.href = URL.createObjectURL(blob);
            link.download = "Consolidated_Journal_Entries.xlsx";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } catch (err: any) {
            setErrors({ general: err.message || "An error occurred while creating the consolidated Excel file." });
        }
    }, [journalEntriesByFile]);

    const hasErrors = Object.keys(errors).length > 0;
    const hasJournalEntries = Object.keys(journalEntriesByFile).length > 0;
    const hasExtractedData = extractedDataList.length > 0;

    const getRightPanelTitle = () => {
        if (hasJournalEntries) return "3. Download Results";
        if (hasExtractedData || hasErrors) return "2. Review Extracted Data";
        return "2. Review & Generate";
    };
    
    const getRightPanelDescription = () => {
        if (hasJournalEntries) return `Generated journal entries for ${Object.keys(journalEntriesByFile).length} file(s). Choose your download format.`;
        if (hasExtractedData) return "Verify the transactions extracted by the AI. Confirm to generate the journal entries for all valid documents.";
        if (hasErrors && !hasExtractedData) return "Some files could not be processed. Please review the errors below.";
        return "After processing, your results will appear here.";
    }

    return (
        <div className="min-h-screen bg-gray-800 text-gray-200 font-sans">
            <Header />
            <main className="p-4 md:p-8">
                <div className="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Input Panel */}
                    <div className="bg-gray-900 p-6 rounded-lg shadow-lg">
                        <div className="flex justify-between items-center">
                            <h2 className="text-xl font-semibold mb-3">1. Upload Bank Statements</h2>
                             {selectedFiles.length > 0 && (
                                <button onClick={resetState} className="text-sm text-teal-400 hover:text-teal-300">Start Over</button>
                            )}
                        </div>
                        <p className="text-sm text-gray-400 mb-4">
                           Select one or more bank statements in PDF format.
                        </p>
                        
                        <div 
                            className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-700 border-dashed rounded-md cursor-pointer hover:border-teal-500 transition-colors"
                            onClick={() => fileInputRef.current?.click()}
                            onDrop={(e) => { e.preventDefault(); handleFileChange({ target: { files: e.dataTransfer.files } } as any); }}
                            onDragOver={(e) => e.preventDefault()}
                        >
                            <div className="space-y-1 text-center">
                                <svg className="mx-auto h-12 w-12 text-gray-500" stroke="currentColor" fill="none" viewBox="0 0 24 24" aria-hidden="true">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m3.75 9v6m3-3H9m1.5-12H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                                </svg>
                                <div className="flex text-sm text-gray-500 justify-center">
                                    <label htmlFor="file-upload" className="relative cursor-pointer bg-gray-900 rounded-md font-medium text-teal-500 hover:text-teal-400 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-offset-gray-900 focus-within:ring-teal-600">
                                        <span>Upload files</span>
                                        <input id="file-upload" name="file-upload" type="file" className="sr-only" multiple onChange={handleFileChange} ref={fileInputRef} accept=".pdf" />
                                    </label>
                                    <p className="pl-1">or drag and drop</p>
                                </div>
                                <p className="text-xs text-gray-400">PDFs only</p>
                            </div>
                        </div>

                        {selectedFiles.length > 0 && (
                            <div className="mt-4 space-y-2">
                                <h3 className="font-semibold text-gray-300">Selected files:</h3>
                                {selectedFiles.map((file, index) => (
                                    <FileListItem key={index} file={file} onRemove={handleRemoveFile} />
                                ))}
                            </div>
                        )}

                        <div className="mt-6">
                            <button
                                onClick={handleProcess}
                                disabled={isLoading || selectedFiles.length === 0}
                                className="w-full inline-flex justify-center items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-teal-500 disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors"
                            >
                                {isLoading ? <SpinnerIcon className="animate-spin -ml-1 mr-3 h-5 w-5" /> : <ProcessIcon className="-ml-1 mr-3 h-5 w-5" />}
                                {isLoading ? 'Processing...' : 'Process Files'}
                            </button>
                        </div>
                    </div>

                    {/* Output Panel */}
                    <div className="bg-gray-900 p-6 rounded-lg shadow-lg flex flex-col">
                        <h2 className="text-xl font-semibold mb-1">{getRightPanelTitle()}</h2>
                        <p className="text-sm text-gray-400 mb-4 h-10">{getRightPanelDescription()}</p>
                        
                        <div className="flex-grow flex flex-col justify-start space-y-4">
                           {hasErrors && Object.entries(errors).map(([fileName, errorMsg]) => (
                                <div key={fileName} className="bg-red-900/50 border border-red-700 text-red-300 px-4 py-3 rounded-md animate-fade-in" role="alert">
                                    <strong className="font-bold">{fileName === 'general' ? 'Error' : `Error in ${fileName}`}: </strong>
                                    <span className="block sm:inline">{errorMsg}</span>
                                </div>
                            ))}

                            {extractedDataList.length > 0 && (
                                <>
                                    <div className="space-y-4">
                                        {extractedDataList.map(({ file, data }) => (
                                           <ReviewData key={file.name} file={file} data={data} />
                                        ))}
                                    </div>
                                    <button
                                        onClick={handleGenerateJournals}
                                        className="w-full mt-4 inline-flex justify-center items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-teal-500"
                                    >
                                        Confirm & Generate Journal Entries
                                    </button>
                                </>
                            )}

                            {hasJournalEntries && (
                                <div className="space-y-4">
                                     <div className="bg-gray-800 p-4 rounded-lg border border-gray-700 animate-fade-in">
                                        <div className="overflow-y-auto" style={{ maxHeight: '400px' }}>
                                             <table className="min-w-full text-sm text-left text-gray-300">
                                                <thead className="text-xs text-teal-400 uppercase bg-gray-700/50 sticky top-0 backdrop-blur-sm">
                                                    <tr>
                                                        {OUTPUT_HEADER.slice(0, 7).map(header => (
                                                            <th key={header} scope="col" className="px-4 py-2">{header}</th>
                                                        ))}
                                                    </tr>
                                                </thead>
                                                <tbody className="divide-y divide-gray-700">
                                                   {Object.values(journalEntriesByFile).flat().slice(0, 100).map((entry, index) => (
                                                        <tr key={index} className="hover:bg-gray-800">
                                                          <td className="px-4 py-2">{entry.journalNumber}</td>
                                                          <td className="px-4 py-2">{entry.journalName}</td>
                                                          <td className="px-4 py-2">{entry.lineNum}</td>
                                                          <td className="px-4 py-2">{entry.postingDate}</td>
                                                          <td className="px-4 py-2">{entry.accountType}</td>
                                                          <td className="px-4 py-2">{entry.accountNo}</td>
                                                          <td className="px-4 py-2 truncate" title={entry.description}>{entry.description}</td>
                                                        </tr>
                                                    ))}
                                                </tbody>
                                            </table>
                                        </div>
                                         {Object.values(journalEntriesByFile).flat().length > 100 && <p className="text-xs text-gray-500 text-center pt-2">Showing first 100 entries...</p>}
                                    </div>
                                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                        <button
                                            onClick={handleDownload}
                                            className="w-full inline-flex justify-center items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-blue-500"
                                        >
                                            <DownloadIcon className="-ml-1 mr-3 h-5 w-5" />
                                            Download All as ZIP
                                        </button>
                                        <button
                                            onClick={handleDownloadSingleSheet}
                                            className="w-full inline-flex justify-center items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-green-500"
                                        >
                                           <DownloadIcon className="-ml-1 mr-3 h-5 w-5" />
                                            Download as One Sheet
                                        </button>
                                    </div>
                                </div>
                            )}

                             {(!isLoading && !hasExtractedData && !hasJournalEntries && !hasErrors) && (
                                <div className="text-center text-gray-500 flex-grow flex items-center justify-center">
                                    <p>Upload files and click 'Process' to begin.</p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </main>
        </div>
    );
}
