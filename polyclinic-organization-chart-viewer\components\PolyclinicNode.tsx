
import React from 'react';
import { Polyclinic, PolyclinicType } from '../types';
import FloorNode from './FloorNode';

interface PolyclinicNodeProps {
  polyclinic: Polyclinic;
}

const PolyclinicNode: React.FC<PolyclinicNodeProps> = ({ polyclinic }) => {
  let bgColor = 'bg-blue-100 hover:bg-blue-200';
  let textColor = 'text-blue-800';
  let borderColor = 'border-blue-400';

  if (polyclinic.type === PolyclinicType.SPECIAL) {
    bgColor = 'bg-purple-100 hover:bg-purple-200';
    textColor = 'text-purple-800';
    borderColor = 'border-purple-400';
  }
  
  // Specific override for "Medical Labs" if it has no floors to avoid large empty space
  const isEmptyMedicalLabs = polyclinic.name === "Medical Labs" && polyclinic.floors.length === 0;


  return (
    <div className="mb-3">
      <div className={`flex items-start ${isEmptyMedicalLabs ? 'mb-0' : 'mb-2'}`}>
        <div 
          className={`px-3 py-2 ${bgColor} ${textColor} border ${borderColor} rounded-md shadow-sm font-semibold text-sm transition-colors duration-150 ease-in-out min-w-[200px] text-center sm:text-left`}
        >
          {polyclinic.name}
        </div>
        {polyclinic.floors && polyclinic.floors.length > 0 && (
           <div className="pt-1"> {/* Aligns the connecting line better with the middle of the parent card */}
             <div className="ml-3 pl-3 border-l-2 border-gray-300 space-y-2">
                {polyclinic.floors.map((floor) => (
                    <FloorNode key={floor.id} floor={floor} />
                ))}
            </div>
           </div>
        )}
      </div>
    </div>
  );
};

export default PolyclinicNode;
