import pandas as pd
import re
import os
from datetime import datetime
from openpyxl import Workbook
import streamlit as st
import pdfplumber
from io import BytesIO

# Simulated data (these could also be loaded from config or DB)
clover_bank_details = [
    {"Account Name": "YARROW POLYCLINIC", "Account No": "KIBYR-4773", "Activities": "1198", "Property_ID": "CLO3"},
    {"Account Name": "ALASEEL POLYCLINIC", "Account No": "KIBAA-2380", "Activities": "1194", "Property_ID": "CLO3"},
    {"Account Name": "JOYA POLYCLINIC", "Account No": "KIBJY-2258", "Activities": "1197", "Property_ID": "CLO6"},
    {"Account Name": "IRIS POLYCLINIC", "Account No": "KIBIR-2304", "Activities": "1193", "Property_ID": "CLO3"},
    {"Account Name": "ARAM MEDICAL POLYCLINIC", "Account No": "KIBAM-3577", "Activities": "1199", "Property_ID": "CLO8"},
    {"Account Name": "FOURTH MEDICAL CENTER", "Account No": "KIBFR-8602", "Activities": "1195", "Property_ID": "CLO5"},
    {"Account Name": "MEDICAL HARBOUR CENTER", "Account No": "KIBMH-2231", "Activities": "1196", "Property_ID": "CLO6"},
    {"Account Name": "MED GRAY POLYCLINIC", "Account No": "KIBMG-2320", "Activities": "1192", "Property_ID": "CLO7"},
    {"Account Name": "MEWL POLYCLINIC", "Account No": "KIBML-6601", "Activities": "1205", "Property_ID": "CLO4"},
    {"Account Name": "MED MARINE POLYCLINIC", "Account No": "KIBMM-2207", "Activities": "1191", "Property_ID": "CLO6"}
]

vendor_offset_accounts = {
    "Prime Medical": "50-000001",
    "Joya Medical Poly Clinic": "50-000022",
    "Med Gray Polyclinic": "50-000023",
    "IRISH Polyclinc": "50-000024",
    "Yarrow Polyclinic": "50-000025",
    "Medmarine Polyclincs": "50-000026",
    "Aram Medical Polyclinics": "50-000027",
    "Medical Harbour Clinic": "50-000028",
    "Fourth Medical Polyclinic": "50-000029",
    "Al Aseel Polyclinics": "50-000031",
    "Medwell Policlinics": "50-000041"
}

clover_dict = {entry["Account Name"].upper(): entry for entry in clover_bank_details}
vendor_dict = {key.upper(): value for key, value in vendor_offset_accounts.items()}

headers = [
    "Journal Number", "Journal Name", "Line Num", "Posting Date",
    "Account Type - Ledger - 0/ Customer - 1 /Vendor - 2/ Fixed assets - 5/ Bank - 6",
    "Account No", "Description", "Debit Amount", "Credit Amount",
    "Currency Code", "Exchange Rate",
    "Offset account Type - Ledger - 0/ Customer - 1 /Vendor - 2/ Fixed assets - 5/ Bank - 6",
    "Offset account", "Invoice No", "Document No", "Document Date", "Due Date",
    "Asset trans type - Acq - 1 / Depre - 3", "Posting Profile", "Payment Mode",
    "Payment Reference", "Number of Voucher", "Activities", "Country",
    "Departments", "Project_ID", "Property_ID", "Unit_ID"
]

def extract_text_from_pdf(file):
    rows = []
    with pdfplumber.open(file) as pdf:
        for page in pdf.pages:
            text = page.extract_text()
            if text:
                lines = text.split('\n')
                for line in lines:
                    # Very basic parsing - adjust regex based on real format
                    match = re.search(r'(\d{4}-\d{2}-\d{2})\s+(.*?)\s+(\d+\.\d+)', line)
                    if match:
                        date, description, amount = match.groups()
                        rows.append({
                            "Date": date,
                            "Account Name": description.split()[0],
                            "Description": description,
                            "Credit Amount": float(amount)
                        })
    return pd.DataFrame(rows)

def process_bank_data(df):
    result = []
    line_num = 1

    for index, row in df.iterrows():
        credit = row.get("Credit Amount", 0)
        if pd.notna(credit) and credit > 0:
            account_name = row.get("Account Name", "").strip().upper()
            clover = clover_dict.get(account_name)
            vendor = vendor_dict.get(account_name)

            if not clover or not vendor:
                continue

            post_date = pd.to_datetime(row.get("Date"), errors='coerce')
            if pd.isna(post_date):
                continue

            month_str = f"{post_date.month:02}"
            invoice_no = f"{account_name.replace(' ', '')}-sal-{month_str}-{line_num}"

            entry = [
                1, "CRNOTE", line_num, post_date.strftime('%Y-%m-%d'),
                6, clover["Account No"], row.get("Description", "Credit Transaction"),
                "", credit, "KWD", 100, 2, vendor,
                invoice_no, "", post_date.strftime('%Y-%m-%d'),
                post_date.strftime('%Y-%m-%d"), "", "Vend Post", "", "", "",
                clover["Activities"], "01", 113, 104, clover["Property_ID"], ""
            ]
            result.append(entry)
            line_num += 1

    return pd.DataFrame(result, columns=headers)

def main():
    st.title("Bank Statement to Journal Entry Generator")
    uploaded_file = st.file_uploader("Upload Bank Statement (.xlsx or .pdf)", type=["xlsx", "pdf"])

    if uploaded_file:
        if uploaded_file.name.endswith(".pdf"):
            df = extract_text_from_pdf(uploaded_file)
        else:
            df = pd.read_excel(uploaded_file)

        journal_df = process_bank_data(df)

        if not journal_df.empty:
            output = BytesIO()
            journal_df.to_excel(output, index=False)
            st.success("JournalEntries.xlsx generated successfully!")
            st.download_button(
                label="Download JournalEntries.xlsx",
                data=output.getvalue(),
                file_name="JournalEntries.xlsx",
                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            )
        else:
            st.warning("No valid credit transactions found or unmatched account names.")

if __name__ == "__main__":
    main()
