
import { CloverBankInfo, VendorOffsetAccounts } from './types';

export const CLOVER_BANK_INFO: CloverBankInfo[] = [
    { accountName: "YARROW POLYCLINIC", accountNo: "KIBYR-4773", activities: "1198", propertyId: "CLO3", country: "01", departments: "113", projectId: "104" },
    { accountName: "ALASEEL POLYCLINIC", accountNo: "KIBAA-2380", activities: "1194", propertyId: "CLO3", country: "01", departments: "113", projectId: "104" },
    { accountName: "JOYA POLYCLINIC", accountNo: "KIBJY-2258", activities: "1197", propertyId: "CLO6", country: "01", departments: "113", projectId: "104" },
    { accountName: "IRIS POLYCLINIC", accountNo: "KIBIR-2304", activities: "1193", propertyId: "CLO3", country: "01", departments: "113", projectId: "104" },
    { accountName: "ARAM MEDICAL POLYCLINIC", accountNo: "KIBAM-3577", activities: "1199", propertyId: "CLO8", country: "01", departments: "113", projectId: "104" },
    { accountName: "FOURTH MEDICAL CENTER", accountNo: "KIBFR-8602", activities: "1195", propertyId: "CLO5", country: "01", departments: "113", projectId: "104" },
    { accountName: "MEDICAL HARBOUR CENTER", accountNo: "KIBMH-2231", activities: "1196", propertyId: "CLO6", country: "01", departments: "113", projectId: "104" },
    { accountName: "MED GRAY POLYCLINIC", accountNo: "KIBMG-2320", activities: "1192", propertyId: "CLO7", country: "01", departments: "113", projectId: "104" },
    { accountName: "MEWL POLYCLINIC", accountNo: "KIBML-6601", activities: "1205", propertyId: "CLO4", country: "01", departments: "113", projectId: "104" },
    { accountName: "MED MARINE POLYCLINIC", accountNo: "KIBMM-2207", activities: "1191", propertyId: "CLO6", country: "01", departments: "113", projectId: "104" }
];

export const VENDOR_OFFSET_ACCOUNTS: VendorOffsetAccounts = {
    "Prime Medical": "50-000001",
    "Joya Medical Poly Clinic": "50-000022",
    "Med Gray Polyclinic": "50-000023",
    "IRIS Polyclinic": "50-000024",
    "Yarrow Polyclinic": "50-000025",
    "Med Marine Medical Polyclinic": "50-000026",
    "Aram Medical Polyclinic": "50-000027",
    "Medical Harbour Clinic": "50-000028",
    "Fourth Medical Polyclinic": "50-000029",
    "Al Aseel Polyclinic": "50-000031",
    "Medwell Polyclinic": "50-000041"
};

export const OUTPUT_HEADER: string[] = [
    'Journal Number', 'Journal Name', 'Line Num', 'Posting Date', 'Account Type - Ledger - 0/ Customer - 1 /Vendor - 2/ Fixed assets - 5/ Bank - 6', 
    'Account No', 'Description', 'Debit Amount', 'Credit Amount', 'Currency Code', 'Exchange Rate', 
    'Offset account Type - Ledger - 0/ Customer - 1 /Vendor - 2/ Fixed assets - 5/ Bank - 6', 'Offset account', 
    'Invoice No', 'Document No', 'Document Date', 'Due Date', 'Asset trans type - Acq - 1 / Depre - 3', 
    'Posting Profile', 'Payment Mode', 'Payment Reference', 'Number of Voucher', 'Activities', 'Country', 
    'Departments', 'Project_ID', 'Property_ID'
];
