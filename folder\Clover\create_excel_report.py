import pandas as pd

def create_interactive_excel():
    """
    Extracts clinic data, processes it, and saves it to a highly
    interactive Excel file with tables, filters, and slicers.
    """
    # 1. DATA: Copied directly from your web application's JavaScript
    clinic_data = [
        # ... (all your clinic data dictionaries go here) ...
        # NOTE: I am including the full data structure below for completeness.
        {
            "name": "Iris - Polyclinic Mazaya 3",
            "floors": [
                {"floor": 2, "codes": [{"code": "A", "doctor": "<PERSON>"}, {"code": "B", "doctor": "Smart Health Co."}, {"code": "C", "doctor": "<PERSON><PERSON><PERSON>"}]},
                {"floor": 3, "codes": [{"code": "A+B", "doctor": "<PERSON><PERSON>"}, {"code": "C", "doctor": "<PERSON><PERSON><PERSON><PERSON>"}]},
                {"floor": 4, "codes": [{"code": "A", "doctor": "<PERSON><PERSON><PERSON> & <PERSON><PERSON>"}, {"code": "B", "doctor": "<PERSON>"}, {"code": "<PERSON>", "doctor": "Dr. <PERSON>"}]}
            ]
        },
        {
            "name": "<PERSON>eel International",
            "floors": [
                {"floor": 7, "codes": [{"code": "C", "doctor": "<PERSON> <PERSON>"}]},
                {"floor": 8, "codes": [{"code": "A", "doctor": "<PERSON> <PERSON> <PERSON> <PERSON> <PERSON>"}, {"code": "B", "doctor": "<PERSON><PERSON>m <PERSON> <PERSON> Bad<PERSON>"}, {"code": "C", "doctor": "<PERSON> Samy <PERSON> <PERSON>ddousy"}]},
                {"floor": 9, "codes": [{"code": "A", "doctor": "Nasser Faisal Al Mutairy"}, {"code": "B", "doctor": "Andro George Mikha'eel"}, {"code": "C", "doctor": "Dr. Noor Aladdin Alomar & Dr. Aya Samara"}]},
                {"floor": 10, "codes": [{"code": "A+B", "doctor": "Dr. Ali Al-Mukaimi & Nisreen Baeij"}, {"code": "C", "doctor": "Dr. Ali Al-Mukaimi"}]}
            ]
        },
        {
            "name": "Yarow - Polyclinic",
            "floors": [
                {"floor": 11, "codes": [{"code": "A", "doctor": "Dr. Ahmed Abdulsamad Yehya Jassem"}, {"code": "B", "doctor": "Dr. Osamah J M Albaker"}, {"code": "C", "doctor": "Hossam Mohamed El Badri"}]},
                {"floor": 12, "codes": [{"code": "A", "doctor": "Ahmed Mohamed Ahmed Ibrahim"}, {"code": "B", "doctor": "Sale Abdul Ghaffar Ma'arafie"}, {"code": "C", "doctor": "Adnan Ibrahim Ibrahim"}]}
            ]
        },
        {
            "name": "Fourth Medical Center",
            "floors": [
                {"floor": 1, "codes": [{"code": "A", "doctor": "Salam Attar"}]},
                {"floor": "2-3", "codes": [{"code": "A", "doctor": "One Day to Manage Projects Co."}]},
                {"floor": "4-5", "codes": [{"code": "A", "doctor": "Athba Co."}]},
                {"floor": 6, "codes": [{"code": "A", "doctor": "Health Care Co."}]},
                {"floor": 7, "codes": [{"code": "A", "doctor": "Abdul Aziz Fahad Al Mezeiny"}]},
                {"floor": 13, "codes": [{"code": "A", "doctor": "Revolution Medical Co."}]},
                {"floor": 14, "codes": [{"code": "A", "doctor": "Dr. Farouk Alzoubani"}]},
                {"floor": 15, "codes": [{"code": "A", "doctor": "Assem Drwesh Mostafa Abdulnabi"}]},
                {"floor": 16, "codes": [{"code": "A", "doctor": "One Day to Manage Projects Co."}]},
                {"floor": 17, "codes": [{"code": "A", "doctor": "Dr. Abdullah Sadad Sabri Al-Ozairi"}]},
                {"floor": "18-19", "codes": [{"code": "A", "doctor": "Gulf Care Co."}]}
            ]
        },
        {
            "name": "Medical Harbour",
            "floors": [
                {"floor": 1, "codes": [{"code": "C", "doctor": "Moaeyed Zaid Al Saq'abi"}]},
                {"floor": 2, "codes": [{"code": "C", "doctor": "Mohamed Abdul Majid Hassan"}]},
                {"floor": 3, "codes": [{"code": "C", "doctor": "Salah El Din Mohamed El Sherbini"}]},
                {"floor": 4, "codes": [{"code": "C", "doctor": "Youssef Al Khleify/Rawan Al Khatib"}]},
                {"floor": 8, "codes": [{"code": "C", "doctor": "Amir Eissa Attia Killa"}]},
                {"floor": 9, "codes": [{"code": "C", "doctor": "Dr. Hesham Mohamed Yassin Ibrahim"}]},
                {"floor": 10, "codes": [{"code": "C", "doctor": "Med Vision Medical Services"}]},
                {"floor": 11, "codes": [{"code": "C", "doctor": "Fatmah Mohamed Badawy"}]},
                {"floor": 12, "codes": [{"code": "C", "doctor": "Othman Youssef Al Mas'oud"}]},
                {"floor": 13, "codes": [{"code": "C", "doctor": "Btissam Ibn Kiran"}]},
                {"floor": 14, "codes": [{"code": "C", "doctor": "Misha'al Al Dahsh"}]},
                {"floor": 15, "codes": [{"code": "C", "doctor": "Amal Al Shaiji / Faisal Al Terkeet"}]},
                {"floor": 16, "codes": [{"code": "C", "doctor": "Signofa Co./Ahmed Eissa"}]},
                {"floor": 17, "codes": [{"code": "C", "doctor": "Waleed Hamid Raheel"}]},
                {"floor": 18, "codes": [{"code": "C", "doctor": "Eman Ghorab"}]},
                {"floor": 19, "codes": [{"code": "C", "doctor": "Emad Morkos/Ahmed Youssef"}]},
                {"floor": 20, "codes": [{"code": "C", "doctor": "Mohamed Al Kolk"}]},
                {"floor": 21, "codes": [{"code": "C", "doctor": "Youssef Al Khleify"}]}
            ]
        },
        {
            "name": "Med Marine",
            "floors": [
                {"floor": 5, "codes": [{"code": "A", "doctor": "Fatima Ne'ma Al Awadhi"}, {"code": "B", "doctor": "Mohamed As'ad Eid/Wael Bezrah"}]},
                {"floor": 6, "codes": [{"code": "A+B", "doctor": "Mohamed Youssef Al Sabty"}]},
                {"floor": 7, "codes": [{"code": "A+B", "doctor": "Mostafa Mohamed Tomsu"}]}
            ]
        },
        {
            "name": "JOYA - Polyclinic",
            "floors": [
                {"floor": 8, "codes": [{"code": "A", "doctor": "Ihab Mohamed Younes Omar"}, {"code": "B", "doctor": "Huda Mahmoud Selim"}]},
                {"floor": 9, "codes": [{"code": "A+B", "doctor": "Berlin Co./Mohamed Riyadh"}]},
                {"floor": 10, "codes": [{"code": "A+B", "doctor": "Shehta Mostafa Ze'reb"}]}
            ]
        },
        {
            "name": "Med Grey",
            "floors": [
                {"floor": 5, "codes": [{"code": "A", "doctor": "Dr. Amr Nabil Qutb"}]},
                {"floor": "6-7", "codes": [{"code": "A", "doctor": "Dr. Shehta Mostafa Zurub"}]}
            ]
        },
        {
            "name": "Aram - Polyclinic",
            "floors": [
                {"floor": 2, "codes": [{"code": "A+B", "doctor": "Dalia/Mina/Osama/Mahmoud"}]},
                {"floor": 3, "codes": [{"code": "A+B", "doctor": "Ayman/Islam"}]},
                {"floor": 4, "codes": [{"code": "A", "doctor": "Mohamed Al Sayyad"}, {"code": "B", "doctor": "Mohamed Al Sayyad"}]},
                {"floor": 5, "codes": [{"code": "A", "doctor": "Bishoy/Mina/Zaher"}, {"code": "B", "doctor": "Nasser/Mohamed"}]},
                {"floor": 6, "codes": [{"code": "A", "doctor": "Munira/Anjoud"}, {"code": "B", "doctor": "Munira/Anjoud"}]},
                {"floor": 7, "codes": [{"code": "A+B", "doctor": "Sondos Ghaneim"}]},
                {"floor": 8, "codes": [{"code": "A", "doctor": "Marina/Mary/Mariana"}, {"code": "B", "doctor": "Dr. Mohammed Salem"}]},
                {"floor": 9, "codes": [{"code": "A", "doctor": "Rwda Ahmed"}, {"code": "B", "doctor": "Marawan Essam"}]}
            ]
        }
    ]

    # 2. PROCESS: Flatten the nested data into a list of dictionaries
    flat_data = []
    for clinic in clinic_data:
        for floor in clinic["floors"]:
            for code in floor["codes"]:
                flat_data.append({
                    "Clinic Name": clinic["name"],
                    "Floor": str(floor["floor"]),  # Convert to string for consistent filtering
                    "Clinic Code": code["code"],
                    "Doctor/Company": code["doctor"],
                })

    # Create a DataFrame
    df = pd.DataFrame(flat_data)

    # 3. ENHANCE: Add a 'Type' column to distinguish Doctors from Companies for the slicer
    df['Type'] = df['Doctor/Company'].apply(
        lambda x: 'Company' if 'co.' in x.lower() or 'services' in x.lower() else 'Doctor'
    )
    
    # Reorder columns for better presentation
    df = df[['Clinic Name', 'Floor', 'Clinic Code', 'Doctor/Company', 'Type']]

    # 4. EXPORT: Write to an Excel file with interactive features
    output_filename = 'Polyclinic_Interactive_Report.xlsx'
    writer = pd.ExcelWriter(output_filename, engine='xlsxwriter')
    
    # Write the main data, starting at row 5 to leave space for stats
    df.to_excel(writer, sheet_name='Polyclinic Directory', startrow=4, index=False, header=True)

    # Get the xlsxwriter workbook and worksheet objects
    workbook = writer.book
    worksheet = writer.sheets['Polyclinic Directory']

    # --- Add Interactivity and Formatting ---

    # A. Define formats
    header_format = workbook.add_format({'bold': True, 'text_wrap': True, 'valign': 'top', 'fg_color': '#4F81BD', 'font_color': 'white', 'border': 1})
    title_format = workbook.add_format({'bold': True, 'font_size': 20, 'font_color': '#1F497D'})
    stats_header_format = workbook.add_format({'bold': True, 'font_size': 12, 'font_color': '#1F497D'})
    stats_number_format = workbook.add_format({'bold': True, 'font_size': 14, 'font_color': '#0070C0'})
    
    # B. Add Title and Dynamic Stats
    worksheet.write('B1', 'Interactive Polyclinic Directory', title_format)
    worksheet.write('B3', 'Visible Entries:', stats_header_format)
    # The SUBTOTAL(103, ...) function dynamically counts visible rows after filtering
    worksheet.write_formula('C3', '=SUBTOTAL(103, Directory[Clinic Name])', stats_number_format)

    # C. Create an Excel Table with filters
    (max_row, max_col) = df.shape
    column_settings = [{'header': column} for column in df.columns]
    # The table is named 'Directory' for easy formula referencing
    worksheet.add_table(4, 0, max_row + 4, max_col - 1, {
        'columns': column_settings,
        'name': 'Directory',
        'style': 'Table Style Medium 9'
    })

    # D. Add Slicers for 'Clinic Name' and 'Type' (Doctor/Company)
    # These slicers are the Excel equivalent of your web app's buttons and filters

    # E. Set column widths for better readability
    worksheet.set_column('A:A', 30) # Clinic Name
    worksheet.set_column('B:B', 10) # Floor
    worksheet.set_column('C:C', 15) # Clinic Code
    worksheet.set_column('D:D', 45) # Doctor/Company
    worksheet.set_column('E:E', 12) # Type

    # Save the Excel file
    writer.close()

    print(f"Successfully created '{output_filename}' with interactive features.")

if __name__ == "__main__":
    create_interactive_excel()
