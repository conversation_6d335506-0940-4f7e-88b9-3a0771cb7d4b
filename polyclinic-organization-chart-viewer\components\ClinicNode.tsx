
import React from 'react';
import { Clinic } from '../types';

interface ClinicNodeProps {
  clinic: Clinic;
}

const ClinicNode: React.FC<ClinicNodeProps> = ({ clinic }) => {
  return (
    <div className="px-2.5 py-1 bg-yellow-100 hover:bg-yellow-200 text-yellow-800 border border-yellow-400 rounded-md shadow-sm text-xs transition-colors duration-150 ease-in-out min-w-[150px]">
      <span className="font-semibold">{clinic.name}</span>
      {clinic.occupant && <span className="italic text-yellow-700"> - {clinic.occupant}</span>}
    </div>
  );
};

export default ClinicNode;
