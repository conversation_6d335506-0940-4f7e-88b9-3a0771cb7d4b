import { CloverBankInfo, VendorOffsetAccounts } from './types';

export const CLOVER_BANK_INFO: CloverBankInfo[] = [
    // AL ASEEL INTERNATIONAL POLYCLINIC
    { accountName: "AL ASEEL INTERNATIONAL POLYCLINIC", oldAccountNo: "************", accountNo: "KIBAA-2380", activities: "1194", propertyId: "CLO3", country: "01", departments: "113", projectId: "104" },
    { accountName: "AL ASEEL INTERNATIONAL POLYCLINIC", accountNo: "KIBAA-2398", activities: "1194", propertyId: "CLO3", country: "01", departments: "113", projectId: "104" },
    { accountName: "AL ASEEL INTERNATIONAL POLYCLINIC", accountNo: "KIBAA-2401", activities: "1194", propertyId: "CLO3", country: "01", departments: "113", projectId: "104" },
    
    // IRIS POLYCLINIC
    { accountName: "IRIS POLYCLINIC", oldAccountNo: "************", accountNo: "KIBIR-2282", activities: "1193", propertyId: "CLO3", country: "01", departments: "113", projectId: "104" },
    { accountName: "IRIS POLYCLINIC", oldAccountNo: "************", accountNo: "KIBIR-2304", activities: "1193", propertyId: "CLO3", country: "01", departments: "113", projectId: "104" },
    { accountName: "IRIS POLYCLINIC", oldAccountNo: "************", accountNo: "KIBIR-8645", activities: "1193", propertyId: "CLO3", country: "01", departments: "113", projectId: "104" },

    // YARROW POLYCLINIC
    { accountName: "YARROW POLYCLINIC", oldAccountNo: "************", accountNo: "KIBYR-4765", activities: "1198", propertyId: "CLO6", country: "01", departments: "113", projectId: "104" },
    { accountName: "YARROW POLYCLINIC", oldAccountNo: "************", accountNo: "KIBYR-4773", activities: "1198", propertyId: "CLO6", country: "01", departments: "113", projectId: "104" },
    
    // MEWL POLYCLINIC
    { accountName: "MEWL POLYCLINIC", oldAccountNo: "************", accountNo: "KIBML-6601", activities: "1205", propertyId: "CLO4", country: "01", departments: "113", projectId: "104" },
    { accountName: "MEWL POLYCLINIC", oldAccountNo: "************", accountNo: "KIBML-6610", activities: "1205", propertyId: "CLO4", country: "01", departments: "113", projectId: "104" },
    
    // FOURTH MEDICAL CENTER
    { accountName: "FOURTH MEDICAL CENTER", oldAccountNo: "************", accountNo: "KIBFR-8602", activities: "1195", propertyId: "CLO5", country: "01", departments: "113", projectId: "104" },
    { accountName: "FOURTH MEDICAL CENTER", oldAccountNo: "************", accountNo: "KIBFR-2770", activities: "1195", propertyId: "CLO5", country: "01", departments: "113", projectId: "104" },
    { accountName: "FOURTH MEDICAL CENTER", oldAccountNo: "************", accountNo: "KIBFR-2789", activities: "1195", propertyId: "CLO5", country: "01", departments: "113", projectId: "104" },
    
    // JOYA POLYCLINIC
    { accountName: "JOYA POLYCLINIC", oldAccountNo: "************", accountNo: "KIBJY-2258", activities: "1197", propertyId: "CLO3", country: "01", departments: "113", projectId: "104" },
    { accountName: "JOYA POLYCLINIC", oldAccountNo: "************", accountNo: "KIBJY-2266", activities: "1197", propertyId: "CLO3", country: "01", departments: "113", projectId: "104" },
    
    // MEDICAL HARBOUR CENTER
    { accountName: "MEDICAL HARBOUR CENTER", oldAccountNo: "************", accountNo: "KIBMH-2231", activities: "1196", propertyId: "CLO6", country: "01", departments: "113", projectId: "104" },
    { accountName: "MEDICAL HARBOUR CENTER", oldAccountNo: "************", accountNo: "KIBMH-2240", activities: "1196", propertyId: "CLO6", country: "01", departments: "113", projectId: "104" },
    
    // MED MARINE POLYCLINIC
    { accountName: "MED MARINE POLYCLINIC", oldAccountNo: "************", accountNo: "KIBMM-2207", activities: "1191", propertyId: "CLO6", country: "01", departments: "113", projectId: "104" },
    { accountName: "MED MARINE POLYCLINIC", accountNo: "KIBMM-2215", activities: "1191", propertyId: "CLO6", country: "01", departments: "113", projectId: "104" },
    { accountName: "MED MARINE POLYCLINIC", accountNo: "KIBMM-2223", activities: "1191", propertyId: "CLO6", country: "01", departments: "113", projectId: "104" },

    // MED GRAY POLYCLINIC
    { accountName: "MED GRAY POLYCLINIC", oldAccountNo: "************", accountNo: "KIBMG-2320", activities: "1192", propertyId: "CLO7", country: "01", departments: "113", projectId: "104" },
    { accountName: "MED GRAY POLYCLINIC", accountNo: "KIBMG-2339", activities: "1192", propertyId: "CLO7", country: "01", departments: "113", projectId: "104" },
    { accountName: "MED GRAY POLYCLINIC", accountNo: "KIBMG-2347", activities: "1192", propertyId: "CLO7", country: "01", departments: "113", projectId: "104" },
    
    // ARAM MEDICAL POLYCLINIC
    { accountName: "ARAM MEDICAL POLYCLINIC", accountNo: "KIBAM-2290", activities: "1199", propertyId: "CLO8", country: "01", departments: "113", projectId: "104" },
    { accountName: "ARAM MEDICAL POLYCLINIC", oldAccountNo: "************", accountNo: "KIBAM-3577", activities: "1199", propertyId: "CLO8", country: "01", departments: "113", projectId: "104" },
];

export const VENDOR_OFFSET_ACCOUNTS: VendorOffsetAccounts = {
    "AL ASEEL INTERNATIONAL POLYCLINIC": "50-000031",
    "IRIS POLYCLINIC": "50-000024",
    "YARROW POLYCLINIC": "50-000025",
    "MEWL POLYCLINIC": "50-000041",
    "FOURTH MEDICAL CENTER": "50-000029",
    "JOYA POLYCLINIC": "50-000022",
    "MEDICAL HARBOUR CENTER": "50-000028",
    "MED MARINE POLYCLINIC": "50-000026",
    "MED GRAY POLYCLINIC": "50-000023",
    "ARAM MEDICAL POLYCLINIC": "50-000027",
};


export const OUTPUT_HEADER = [
  "Journal Number", "Journal Name", "Line Num", "Posting Date", "Account Type", 
  "Account No", "Description", "Debit Amount", "Credit Amount", "Currency Code", 
  "Exchange Rate", "Offset Account Type", "Offset Account", "Invoice No", "Document No", 
  "Document Date", "Due Date", "Asset Trans Type", "Posting Profile", "Payment Mode", 
  "Payment Reference", "Number of Voucher", "Activities", "Country", "Departments", 
  "Project_ID", "Property_ID"
];