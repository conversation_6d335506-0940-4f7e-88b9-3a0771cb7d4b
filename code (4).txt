'---------------------------------------------------------------------------------------
' Module:  DynamicFolderCreation
' Author:  (Generated from user request)
' Date:    [Current Date]
' Purpose: Reads clinic data from a specified Excel file ("medical.xlsx") and sheet
'          ("Sheet1") to dynamically create a folder structure on the user's Desktop.
'---------------------------------------------------------------------------------------
Option Explicit

' Helper function to sanitize a string to be used as a valid folder name.
Private Function SanitizeName(ByVal name As String) As String
    Dim invalidChars As Variant
    Dim char As Variant
    invalidChars = Array("/", "\", ":", "*", "?", """", "<", ">", "|")
    
    SanitizeName = name
    For Each char In invalidChars
        SanitizeName = Replace(SanitizeName, char, "_")
    Next char
End Function

' Main subroutine to create the entire folder structure from an Excel file.
Public Sub CreateFoldersFromExcel()
    ' --- Objects and Variables ---
    Dim fso As Object
    Dim wb As Workbook
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim i As Long
    
    ' --- Path and Data Variables ---
    Dim excelFilePath As String
    Dim desktopPath As String
    Dim basePath As String
    Dim cloverPath As String
    Dim clinicPath As String
    Dim floorPath As String
    Dim filePath As String
    
    ' --- Data from Excel ---
    Dim clinicName As String
    Dim floorNumber As String
    Dim clinicCode As String
    Dim doctorName As String
    
    '--- Error Handling Setup ---
    On Error GoTo ErrorHandler
    
    ' --- 1. Select the source Excel file ---
    excelFilePath = Application.GetOpenFilename( _
        FileFilter:="Excel Files (*.xls*),*.xls*", _
        Title:="Please select the 'medical.xlsx' file")
        
    If excelFilePath = "False" Then
        MsgBox "Operation cancelled by user.", vbInformation
        Exit Sub
    End If
    
    ' --- 2. Setup File System Object and Output Paths ---
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    ' Get Desktop path safely
    desktopPath = CreateObject("WScript.Shell").SpecialFolders("Desktop")
    If desktopPath = "" Then
        MsgBox "Could not find your Desktop path. The script will stop.", vbCritical
        Exit Sub
    End If

    ' Define and create the base output directory on the Desktop
    basePath = fso.BuildPath(desktopPath, "Clinic Folders")
    If Not fso.FolderExists(basePath) Then fso.CreateFolder basePath
    
    cloverPath = fso.BuildPath(basePath, "Clover")
    If Not fso.FolderExists(cloverPath) Then fso.CreateFolder cloverPath
    
    Application.ScreenUpdating = False
    Application.StatusBar = "Processing Excel file... Please wait."
    
    ' --- 3. Open the source workbook and get the sheet ---
    Set wb = Workbooks.Open(excelFilePath)
    
    On Error Resume Next ' Check if "Sheet1" exists
    Set ws = wb.Sheets("Sheet1")
    On Error GoTo ErrorHandler ' Restore normal error handling
    
    If ws Is Nothing Then
        wb.Close SaveChanges:=False
        MsgBox "Could not find a worksheet named 'Sheet1' in the selected file.", vbCritical, "Sheet Not Found"
        GoTo Cleanup
    End If
    
    ' --- 4. Loop through the rows and create folders/files ---
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row ' Find last row in Column A
    
    For i = 2 To lastRow ' Start from row 2 to skip header
        ' Read data from the current row
        clinicName = Trim(ws.Cells(i, 1).Value)  ' Column A
        floorNumber = Trim(ws.Cells(i, 2).Value) ' Column B
        clinicCode = Trim(ws.Cells(i, 3).Value)  ' Column C
        doctorName = Trim(ws.Cells(i, 4).Value)  ' Column D
        
        ' Skip empty rows to prevent errors
        If clinicName = "" Or floorNumber = "" Or clinicCode = "" Then
            GoTo NextRow
        End If
        
        ' Build paths dynamically
        clinicPath = fso.BuildPath(cloverPath, SanitizeName(clinicName))
        floorPath = fso.BuildPath(clinicPath, "Floor_" & floorNumber)
        
        ' Create folders if they don't exist (this prevents "Path not found" error)
        If Not fso.FolderExists(clinicPath) Then fso.CreateFolder clinicPath
        If Not fso.FolderExists(floorPath) Then fso.CreateFolder floorPath
        
        ' Create the final text file with doctor's name
        Dim fileStream As Object
        filePath = fso.BuildPath(floorPath, SanitizeName(clinicCode) & ".txt")
        Set fileStream = fso.CreateTextFile(filePath, True, False) ' True=Overwrite
        fileStream.Write doctorName
        fileStream.Close
        
NextRow:
    Next i
    
    ' --- 5. Cleanup and Success Message ---
    wb.Close SaveChanges:=False
    
    Application.StatusBar = ""
    Application.ScreenUpdating = True
    MsgBox "Folder structure created successfully in:" & vbCrLf & basePath, vbInformation, "Success"
    
Cleanup:
    ' Release all objects
    Set ws = Nothing
    Set wb = Nothing
    Set fso = Nothing
    Exit Sub

'--- Error Handling Block ---
ErrorHandler:
    Application.StatusBar = ""
    Application.ScreenUpdating = True
    MsgBox "An unexpected error occurred:" & vbCrLf & vbCrLf & _
           "Error " & Err.Number & ": " & Err.Description, vbCritical, "Macro Failed"
    
    ' Attempt to close the workbook if it was opened
    If Not wb Is Nothing Then
        wb.Close SaveChanges:=False
    End If
    
    GoTo Cleanup

End Sub